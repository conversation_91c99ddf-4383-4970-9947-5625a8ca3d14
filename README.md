# 接入


## 1. 申请APP

- 申请 App 审批通过后获得 AppSecret。

## 2. 依赖
项目添加依赖
```xml
<dependency>
    <groupId>com.mioffice.ums</groupId>
    <artifactId>api-sdk</artifactId>
    <version>1.0-SNAPSHOT</version>
</dependency>
```

## 3. 配置

> application.yml

```yaml
ums:
  open:
    sdk:
      app-config:
        app-id: S12345
        app-secret: xxxxx
      env: test
```
> env 可选范围 `test`, `prod`

## 4. 使用

```java
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class UmsHttpStubTest {
    @Autowired
    private UmsHttpStub umsHttpStub;

    @Test
    public void testHttp() {
        BatchMessage batchMessage = new BatchMessage();
        // set...

        UmsResponse<String> umsResponse = umsHttpStub.sendBatch(batchMessage);
        log.info("{}", umsResponse);
    }
}
```
