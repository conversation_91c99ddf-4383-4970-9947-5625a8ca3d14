[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] nacosConfigProperties : NacosConfigProperties{serverAddr='http://staging-nacos.api.xiaomi.net:80', contextPath='null', encode='null', endpoint='null', namespace='info_oa_test', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='null', dataIds='common_config', group='infra.ums', type=YAML, maxRetry='3', configLongPollTimeout='30000', configRetryTime='2000', enableRemoteSyncConfig=true, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] isMultiInstance:false
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] load config from nacos, data-id is : common_config, group is : infra.ums
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [ClientAuthPluginManager]	Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [ClientAuthPluginManager]	Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] nacos.cache.data.init.snapshot = true 
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [RpcClientFactory]	create a new rpc client of 173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$177/2117193231
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$178/161113555
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Try to connect to server on start up, server: {serverIp = 'staging-nacos.api.xiaomi.net', server main port = 80}
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Success to connect to server [staging-nacos.api.xiaomi.net:80]	on start up, connectionId = 1711027958151_10.38.164.111_50407
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Notify connected event to listeners.
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$186/1555055322
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [173bbd86-5e88-449f-ab47-40b25dcbc7e8_config-0]	Connected,notify listen context...
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [fixed-info_oa_test-staging-nacos.api.xiaomi.net_80]	[subscribe]	common_config+infra.ums+info_oa_test
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] [fixed-info_oa_test-staging-nacos.api.xiaomi.net_80]	[add-listener]	ok, tenant=info_oa_test, dataId=common_config, group=infra.ums, cnt=1
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] Starting Application on WN-20210427WVJC with PID 21408 (E:\code\ums-open-api\api-server\target\classes started by dongjunfu in E:\code\ums-open-api)
[2024-03-21 21:32:36] [ums-open-server] [WN-20210427WVJC] [INFO] [] The following profiles are active: dev
[2024-03-21 21:32:38] [ums-open-server] [WN-20210427WVJC] [WARN] [] Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [INFO] [] Multiple Spring Data modules found, entering strict repository configuration mode!
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [INFO] [] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [INFO] [] Finished Spring Data repository scanning in 41ms. Found 0 Redis repository interfaces.
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [INFO] [] got blockingProxy
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [INFO] [] got blockingProxy
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [WARN] [] not configured
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [INFO] [] registerRpcClient beanName: ums-engine-message-server, category: ums-engine-message-server, interfaceClass: com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageServiceBlockingClient, stubType: BLOCKING, declaringClass: com.mioffice.ums.open.server.remote.MessageRpcClient
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [WARN] [] not configured
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [INFO] [] registerRpcClient beanName: ums-admin-server, category: ums-admin-server, interfaceClass: com.xiaomi.info.pb.app.mioffice.ums.admin.v1.EmployeeInfoServiceBlockingClient, stubType: BLOCKING, declaringClass: com.mioffice.ums.open.server.remote.UmsAdminRpcClient
[2024-03-21 21:32:39] [ums-open-server] [WN-20210427WVJC] [WARN] [] No value bound check metric config
[2024-03-21 21:32:40] [ums-open-server] [WN-20210427WVJC] [INFO] [] Tomcat initialized with port(s): 8001 (http)
[2024-03-21 21:32:40] [ums-open-server] [WN-20210427WVJC] [INFO] [] Starting service [Tomcat]
[2024-03-21 21:32:40] [ums-open-server] [WN-20210427WVJC] [INFO] [] Starting Servlet engine: [Apache Tomcat/9.0.35]
[2024-03-21 21:32:41] [ums-open-server] [WN-20210427WVJC] [INFO] [] Initializing Spring embedded WebApplicationContext
[2024-03-21 21:32:41] [ums-open-server] [WN-20210427WVJC] [INFO] [] Root WebApplicationContext: initialization completed in 4190 ms
[2024-03-21 21:32:42] [ums-open-server] [WN-20210427WVJC] [INFO] [] Redisson 3.14.0
[2024-03-21 21:32:43] [ums-open-server] [WN-20210427WVJC] [INFO] [] 1 connections initialized for wcc.cache01.test.b2c.srv/*************:22122
[2024-03-21 21:32:44] [ums-open-server] [WN-20210427WVJC] [INFO] [] 24 connections initialized for wcc.cache01.test.b2c.srv/*************:22122
[2024-03-21 21:32:44] [ums-open-server] [WN-20210427WVJC] [INFO] [] scriptLoad success local limit_root = KEYS[1];
local limit_key = KEYS[2];
local limit_conf = KEYS[3];
local limit_ms_conf = KEYS[4];
local rate = redis.call('HGET', limit_root, limit_conf);
local timestamp_ms = ARGV[1];
local interval_ms = ARGV[2];
local timeout_ms = ARGV[3];
local rate_ms = redis.call('HGET', limit_root, limit_ms_conf);

if(rate_ms)
then
    if(tonumber(rate_ms) > 0) then
        rate_ms = tonumber(rate_ms)
    end
else
    rate_ms = 1000
end

if(rate)
then
    if(tonumber(rate) > 0)
    then
        interval_ms = rate_ms / tonumber(rate)
    end
end

local val = redis.call('HGET', limit_root, limit_key);
local wait_ms = 0;
if(val)
then
    val = tonumber(val)
    local diff = tonumber(timestamp_ms) - tonumber(interval_ms);
    if(diff < val) then
        wait_ms = val - diff
        timestamp_ms = val + tonumber(interval_ms)
    end
end
if(tonumber(timeout_ms) <= 0) then
    redis.call('HSET', limit_root, limit_key, timestamp_ms);
else
    if(wait_ms < tonumber(timeout_ms)) then
        redis.call('HSET', limit_root, limit_key, timestamp_ms);
    end
end
return wait_ms;
[2024-03-21 21:32:45] [ums-open-server] [WN-20210427WVJC] [INFO] [] Initializing ExecutorService 'applicationTaskExecutor'
[2024-03-21 21:32:45] [ums-open-server] [WN-20210427WVJC] [WARN] [] No suitable sender found. Using NoopSender, meaning that data will not be sent anywhere!
[2024-03-21 21:32:45] [ums-open-server] [WN-20210427WVJC] [INFO] [] Initialized tracer=JaegerTracer(version=Java-0.35.2, serviceName=info-application:ums_open_api, reporter=RemoteReporter(sender=NoopSender(), closeEnqueueTimeout=1000), sampler=ConstSampler(decision=true, tags={sampler.type=const, sampler.param=true}), tags={hostname=WN-20210427WVJC, jaeger.version=Java-0.35.2, ip=**************}, zipkinSharedRpcSpan=false, expandExceptionLogs=false, useTraceId128Bit=false)
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] >> scaned rpc services:[{appListServer=com.mioffice.ums.open.server.rpc.AppListServer@207f7baa, openServer=com.mioffice.ums.open.server.rpc.OpenServer@1067bc4c, pushSummaryGrpcService=com.mioffice.ums.open.server.rpc.PushSummaryGrpcService@69ee0861}]
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] mrpc bind service: bean=appListServer
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] mrpc bind service: bean=openServer
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] mrpc bind service: bean=pushSummaryGrpcService
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] mrpc server started, listening on 10102
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] mrpc server version: 2.2.3-SNAPSHOT
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] rollingPolicy create success. /home/<USER>/logs/applogs/opentracing\ums_open_api.%d{yyyyMMdd}.%i.log
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] encoder create success. %m%n
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] rollingFileAppender create success. /home/<USER>/logs/applogs/opentracing\ums_open_api.log
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] logger creat success. com.xiaomi.info.infra.mrpc.tracer.sender.LogSender
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] rollingPolicy create success. /home/<USER>/logs/applogs\ums_open_api.%d{yyyyMMdd}.%i.log
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] encoder create success. [%d{yyyy-MM-dd HH:mm:ss}]	[ums_open_api]	[WN-20210427WVJC]	[%p]	[%X{traceid}]	%msg %replace(%ex){'[\n\t]+', ' '}%nopex%n
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] rollingFileAppender create success. /home/<USER>/logs/applogs\ums_open_api.log
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] logger creat success. com.xiaomi.info.grpc.common.Logger
[2024-03-21 21:32:46] [ums-open-server] [WN-20210427WVJC] [INFO] [] creator run success. logback
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Using default implementation for ThreadExecutor
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Quartz Scheduler v.2.3.2 created.
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] RAMJobStore initialized.
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Quartz scheduler version: 2.3.2
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@10395bc4
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Tomcat started on port(s): 8001 (http) with context path ''
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Tomcat initialized with port(s): 8080 (http)
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Starting service [Tomcat]
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Starting Servlet engine: [Apache Tomcat/9.0.35]
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Initializing Spring embedded WebApplicationContext
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Root WebApplicationContext: initialization completed in 110 ms
[2024-03-21 21:32:47] [ums-open-server] [WN-20210427WVJC] [INFO] [] Exposing 6 endpoint(s) beneath base path '/debug'
[2024-03-21 21:32:48] [ums-open-server] [WN-20210427WVJC] [INFO] [] Tomcat started on port(s): 8080 (http) with context path ''
[2024-03-21 21:32:48] [ums-open-server] [WN-20210427WVJC] [INFO] [] Starting Quartz Scheduler now
[2024-03-21 21:32:48] [ums-open-server] [WN-20210427WVJC] [INFO] [] Scheduler quartzScheduler_$_NON_CLUSTERED started.
[2024-03-21 21:32:48] [ums-open-server] [WN-20210427WVJC] [INFO] [] Started Application in 14.575 seconds (JVM running for 15.871)
[2024-03-21 21:32:53] [ums-open-server] [WN-20210427WVJC] [INFO] [] start register
[2024-03-21 21:32:55] [ums-open-server] [WN-20210427WVJC] [INFO] [] start register complete
