create table `app_sys_info`
(
    `id`                 bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

    `app_id`             varchar(64)         not null default '' comment '应用id',
    `app_secret`         varchar(64)         not null default '' comment '应用secret',
    `app_name`           varchar(128)        not null default '' comment '应用name',


    `create_time`        bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`        bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',

    `qps`                int(10)             not null default 0 comment '预估qps',

    `app_sys_status`     tinyint(3)          not null default 0 comment '应用状态（1 审核中，2 审核通过，3 审核驳回， 4 已取消，5 已停用）',
    `old_app_sys_status` tinyint(3)          not null default 0 comment '停用前应用状态（1 审核中，2 审核通过，3 审核驳回， 4 已取消，5 已停用）',

    `update_username`    varchar(64)         not null default '' comment '更新者username',
    `update_name`        varchar(64)         not null default '' comment '更新者name',
    `create_name`        varchar(64)         not null default '' comment '创建者name',
    `create_username`    varchar(64)         not null default '' comment '创建者username',

    PRIMARY KEY (`id`),
    index idx_app_id (`app_id`),
    unique uk_app_id_secret (`app_id`, `app_secret`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='应用表';

create table `user_app_sys_info`
(

    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_sys_id`  bigint(20)          NOT NULL DEFAULT 0 COMMENT 'app_sys_info 外键id',
    `app_id`      varchar(64)         not null default '' comment '应用id',
    `username`    VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '用户username',
    `name`        VARCHAR(64)         NOT NULL DEFAULT '' COMMENT '用户name',
    `user_type`   tinyint(3)          NOT NULL DEFAULT 0 COMMENT '用户类型(1：负责人)',
    `create_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`),
    index idx_app_id (`app_id`),
    index idx_app_sys_id (`app_sys_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='应用用户表';

create table `dept_app_sys_info`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_sys_id`  bigint(20)          NOT NULL DEFAULT 0 COMMENT 'app_sys_info 外键id',
    `app_id`      varchar(64)         not null default '' comment '应用id',

    `dept_id`     VARCHAR(64)         NOT NULL DEFAULT '' COMMENT '部门ID',
    `dept_name`   VARCHAR(64)         NOT NULL DEFAULT '' COMMENT '部门name',

    `create_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',

    PRIMARY KEY (`id`),
    index idx_app_id (`app_id`),
    index idx_app_sys_id (`app_sys_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='应用部门表';

create table `app_sys_bot_info`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_sys_id`  bigint(20)          NOT NULL DEFAULT 0 COMMENT 'app_sys_info 外键id',
    `app_id`      varchar(64)         not null default '' comment '应用id',

    `bot_app_id`  varchar(64)         not null default '' comment 'bot对应的appId',
    `channel`     tinyint(3)          NOT NULL DEFAULT 0 COMMENT '消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)',
    `is_out_send` tinyint(3)          not null default 0 comment '是否发送外部(1 内部，2 外部， 3 内部和外部)',

    `create_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',

    PRIMARY KEY (`id`),
    index idx_app_id (`app_id`),
    index idx_app_sys_id (`app_sys_id`),
    unique uk_app_sys_id_channel_bot_app_id (`app_sys_id`, `channel`, `bot_app_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='应用拥有机器人资源表';
alter table app_sys_bot_info
    add bot_biz_id varchar(32) default '' null comment '机器人业务id';



create table `app_sys_apply_record_info`
(
    `id`                  bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

    `apply_status`        tinyint(3)          not null default 0 comment '审批状态 1-审核中，2-审核通过，3-审核拒绝',
    `apply_desc`          text comment '申请说明',
    `apply_user_username` varchar(64)         not null default '' comment '用户名',
    `apply_user_name`     varchar(64)         not null default '' comment '姓名',

    `approval_username`   varchar(64)         not null default '' comment '审批人',
    `approval_name`       varchar(64)         not null default '' comment '审批人姓名',
    `approval_desc`       text comment '审批说明',

    `app_sys_id`          bigint(20)                   default 0 not null comment '系统id',

    `create_time`         bigint(20)          not null default 0 comment '创建时间',
    `update_time`         bigint(20)          not null default 0 comment '更新时间',

    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='应用申请记录表';



create table `app_sys_template_info`
(
    `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `biz_id`           varchar(64)         not null default '' comment '模板业务id',

    `app_sys_id`       bigint(20)          NOT NULL DEFAULT 0 COMMENT 'app_sys_info 外键id',
    `app_id`           varchar(64)         not null default '' comment '应用id',

    `template_name`    varchar(128)        not null default '' comment '模板主题',
    `bot_app_id`       varchar(64)         not null default '' comment 'bot对应的appId',
    `template_content` text comment '模板内容',
    `template_id`      bigint(20)          not null default 0 comment '引擎模板id',

    `template_channel` tinyint(3)                   default 0 not null comment '模板渠道',

    `create_time`      bigint(20)          not null default 0 comment '创建时间',
    `update_time`      bigint(20)          not null default 0 comment '更新时间',

    `update_username`  varchar(64)         not null default '' comment '更新者username',
    `create_username`  varchar(64)         not null default '' comment '创建者username',

    `update_name`      varchar(64)         not null default '' comment '更新者name',
    `create_name`      varchar(64)         not null default '' comment '创建者name',
    PRIMARY KEY (`id`),
    index idx_biz_id (`biz_id`),
    index idx_template_id (`template_id`),
    unique uk_biz_id (`biz_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='应用模板表';


create table `app_sys_monitor_info`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

    `app_sys_id`      bigint(20)          NOT NULL DEFAULT 0 COMMENT 'app_sys_info 外键id',
    `app_id`          varchar(64)         not null default '' comment '应用id',

    `channel`         tinyint(3)          NOT NULL DEFAULT 0 COMMENT '消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)',

    `all_count`       bigint(20)          not null default 0 comment '总条数',
    `push_count`      bigint(20)          not null default 0 comment '推送成功条数',
    `todo_count`      bigint(20)          not null default 0 comment '待推送条数',
    `fail_count`      bigint(20)          not null default 0 comment '失败条数',
    `interrupt_count` bigint(20)          not null default 0 comment '中断条数',

    `create_time`     bigint(20)          not null default 0 comment '创建时间',
    `update_time`     bigint(20)          not null default 0 comment '更新时间',

    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='应用每天统计推送表';

create table `app_sys_push_record_info`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_id`          varchar(64)         not null default '' comment '应用id',
    `channel`         tinyint(3)          NOT NULL DEFAULT 0 COMMENT '消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)',
    `bot_app_id`      varchar(64)         not null default '' comment 'bot对应的appId',
    `title_cn`        text comment '标题cn',
    `title_en`        text comment '标题en',
    `extra_id`        varchar(64)         not null default '' comment '组id',
    `template_id`     bigint(20)          not null default 0 comment '引擎模板id',
    `template_biz_id` varchar(64)         not null default '' comment '模板业务id',
    `all_count`       bigint(20)          not null default 0 comment '总条数',
    `create_time`     bigint(20)          not null default 0 comment '创建时间',
    `update_time`     bigint(20)          not null default 0 comment '更新时间',
    PRIMARY KEY (`id`),
    index idx_extra_id (`extra_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='批次任务记录表';

alter table app_sys_push_record_info
    add bot_biz_id varchar(32) default '' null comment '机器人业务id';

alter table app_sys_monitor_info
    add push_date bigint(20) default 0 not null comment '记录推送时间';

alter table app_sys_template_info
    add bot_biz_id varchar(32) default '' not null comment '机器人业务id';


alter table app_sys_apply_record_info
    add valid tinyint(3) default 0 not null comment '是否有效（1无效，2有效）';

alter table app_sys_bot_info
    add bot_biz_id varchar(64) default '' not null comment '机器人业务id';


alter table app_sys_template_info
    modify template_content longtext null comment '模板内容';


-- 2020年11月23日14:54:13
create table `app_msg_repeat_info`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

    `app_id`       varchar(64)         not null default '' comment '应用id',
    `bot_biz_id`   varchar(32)         not null default '' comment '机器人业务id',

    `content_md5`  varchar(32)         not null default '' comment '内容MD5',
    `repeat_count` bigint(20)          not null default 0 comment '次数',
    `create_time`  bigint(20)          not null default 0 comment '创建时间',
    `update_time`  bigint(20)          not null default 0 comment '更新时间',

    PRIMARY KEY (`id`),
    index idx_content_md5 (`content_md5`)

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='消息重复推送记录表';

alter table app_sys_info
    add alarm_status tinyint(1) not null default 1 comment '报警状态（0关闭，1开启）';

alter table app_sys_info
    add alarm_threshold int(8) not null default 2 comment '报警阈值';


-- 2020年11月24日21:02:44
alter table app_msg_repeat_info
    add temp_user_md5 varchar(32) not null default '' comment '模板用户md5';

alter table app_msg_repeat_info
    add index idx_temp_user_md5 (`temp_user_md5`);

alter table app_msg_repeat_info
    add `user_id` varchar(64) not null default '' comment 'username/email/phone';

alter table app_msg_repeat_info
    add `channel` tinyint(3) not null default 0 comment '消息渠道';


-- 2020年12月01日15:01:22
alter table app_sys_info
    add `deduplicate_status` tinyint(3) not null default 1 comment '去重状态（0关闭，1开启）';


-- 2020年12月09日14:49:43
alter table app_msg_repeat_info
    add `template_biz_id` varchar(64) not null default '' comment '模板biz_id';


-- 2021年12月21日10:55:26
alter table app_sys_info
    add white_status tinyint(3) not null default 0 comment '白名单状态(0关闭，1开启)';

CREATE TABLE `app_white_list_info` (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `app_id` varchar(64) NOT NULL DEFAULT '' COMMENT '应用id',
                                       `channel` tinyint(3) NOT NULL DEFAULT '0' COMMENT '消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)',
                                       `white_id` varchar(64) NOT NULL DEFAULT '' COMMENT '白名单id',
                                       `create_time` bigint(20) NOT NULL COMMENT '创建时间',
                                       `update_time` bigint(20) NOT NULL COMMENT '更新时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_app_id_channel_white_id` (`app_id`,`channel`,`white_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用白名单表';