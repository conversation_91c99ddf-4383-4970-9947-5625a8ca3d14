FROM hub.pf.xiaomi.com/infoarch/maven-openjdk8-onbuild-artifactory as builder

# 对应是 .gitlab-ci.yml 文件里的 SERVICE_NAME
ARG APP
#  对应是 build.gitlab-ci.yml 文件里的 PROFILE_NAME
ARG ENV
RUN  mkdir -p  /home/<USER>/data/www/${APP}
WORKDIR /home/<USER>/data/www/${APP}
# COPY项目入口
COPY . .
RUN  mvn -B package -DskipTests -P ${ENV}

# deploy step
FROM hub.pf.xiaomi.com/neo-images/online-app:centos7.3-openjdk1.8

ARG APP
ARG ENV
ENV SPRING_PROFILE_ACTIVE ${ENV}

WORKDIR /home/<USER>/data/www/${APP}
COPY --from=builder /home/<USER>/data/www/${APP}/api-server/target/*.jar ./app.jar
ADD https://pkgs.d.xiaomi.net/artifactory/releases/io/opentelemetry/javaagent/opentelemetry-javaagent/1.1.0-mi1.1.1/opentelemetry-javaagent-1.1.0-mi1.1.1-all.jar /home/<USER>/app/mitelemetry/agent/opentelemetry-javaagent-all.jar