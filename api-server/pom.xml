<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ums-open-api</artifactId>
        <groupId>com.mioffice.ums</groupId>
        <version>1.9.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>api-server</artifactId>

    <properties>
        <java.version>1.8</java.version>
        <nacos-starter-version>0.2.13-xiaomi</nacos-starter-version>
        <nacos-actuator-version>0.2.13-xiaomi</nacos-actuator-version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mioffice.ums</groupId>
            <artifactId>api-common</artifactId>
        </dependency>

        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>3.9.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.info.grpc</groupId>
            <artifactId>mi-grpc-client-spring-boot-starter</artifactId>
            <version>${starter.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.info.grpc.proto</groupId>
            <artifactId>app-mioffice-ums-engine-v1</artifactId>
            <version>0.0.78-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- 可选, 非必须 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
            <version>1.6.1</version>
        </dependency>


        <dependency>
            <groupId>com.xiaomi.info.grpc.proto</groupId>
            <artifactId>app-mioffice-ums-open-v1</artifactId>
            <version>0.0.48-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.info.grpc.proto</groupId>
            <artifactId>app-mioffice-ums-admin-v1</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.3.7</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.7</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.46</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.info.grpc</groupId>
            <artifactId>mi-grpc-server-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.14</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>6.0</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.info.infra</groupId>
            <artifactId>log-be-spring-boot-starter</artifactId>
            <version>1.0.3.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-starter</artifactId>
            <version>1.1.8</version>
        </dependency>

        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-core</artifactId>
            <version>1.14.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-generator-annprocess</artifactId>
            <version>1.14.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.mi.oa.infra.mibpm</groupId>
            <artifactId>mibpm-bpm-spring-boot-starter</artifactId>
            <version>1.2.4-SNAPSHOT</version>
        </dependency>

        <!--   nacos     -->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>${nacos-starter-version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-actuator</artifactId>
            <version>${nacos-actuator-version}</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cloud</groupId>
            <artifactId>miplan-springboot-starter</artifactId>
            <version>2.1.8.release</version>
        </dependency>


    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <env>pre</env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/resources-env/${env}</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
