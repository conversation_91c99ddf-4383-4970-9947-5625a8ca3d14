server:
  shutdown: graceful
  port: 8001
logging:
  file:
    name: /home/<USER>/logs/applogs/${spring.application.name}.log
  level:
    root: info
    sql: trace
spring:
  application:
    name: ums-open-server
  cache:
    type: redis
    redis:
      time-to-live: 300s
      key-prefix: "ums:open:server:"
  mvc:
    converters:
      preferred-json-mapper: gson
  #  暂用此 redis
  redis:
    redisson:
      config: |
        singleServerConfig:
          database: 0
          address: redis://wcc.cache01.test.b2c.srv:22122
          password: cn_info-application_cn_info-application_administration_notice_admin_svxri9jWvrSo
          connectionPoolSize: 50
  datasource:
    master:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      jdbcUrl: **************************************************************************************************************
      password@kc-sid: oa-infra.g
      username: ums_open_wn
      password: GDApAT1mirP4lrwrm/W3Q9NjIIERjkNR2GEgScein09lsXanFoRXuQ96O+oh8A/iC7gYErx2fPLbh0OloHzqh3HdNZPN/xgQ91HI1+bLTJ26+0PkrydYMxgUF0I3rxFnZ8EaBjSGE1RUe9oIO0UA
      # hikari 的相关配置
      continue-on-error: true
      maximum-pool-size: 100
      minimum-idle: 50
      auto-commit: true
      connection-test-query: SELECT 1
      connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci
    slave:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.p6spy.engine.spy.P6SpyDriver
      jdbcUrl: **************************************************************************************************************
      password@kc-sid: oa-infra.g
      username: ums_open_wn
      password: GDApAT1mirP4lrwrm/W3Q9NjIIERjkNR2GEgScein09lsXanFoRXuQ96O+oh8A/iC7gYErx2fPLbh0OloHzqh3HdNZPN/xgQ91HI1+bLTJ26+0PkrydYMxgUF0I3rxFnZ8EaBjSGE1RUe9oIO0UA
      # hikari 的相关配置
      continue-on-error: true
      maximum-pool-size: 100
      minimum-idle: 50
      auto-commit: true
      connection-test-query: SELECT 1
      connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci
  mi:
    plan:
      env: TEST   #TEST/PROD
      project-uid: daa0213fb07c420198cfc47cb39d3bad   #就是上面生成的uid
      country-domain: cn
      autoStartup: true    #默认为false:表示不启动

# 监控配置
management:
  server:
    port: 8080
  endpoints:
    web:
      exposure:
        include: info,health,metrics,prometheus
      base-path: /debug
  health:
    redis:
      enabled: false

# grpc client
mrpc:
  client:
    server-name: ums_open_api
    server-group: info-application
    category:
      ums-engine-message-server:
        server-name: ums_engine
        use-soa: true
      ums-admin-server:
        server-name: ums_admin
        use-soa: true
  server:
    server-group: info-application
    server-name: ums_open_api
    auto-register: true


# mybatis
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  global-config:
    db-config:
      id-type: auto   # 可以拿到返回的主键id

lark:
  appId: cli_9fec8864dcac5063

# 消息预警
ums:
  open:
    app-id: S00126
    bot-id: cli_9fcecd191e7a5063
    sms-sign: 9000085
    templates:
      lark-repeat-tmp-id: TLB012300018
      sms-repeat-tmp-id: TMB000100033
      lark-report-tmp-id: TLB012300014
    super-sys-admin-list: zhanggong,liuben,raojian,qicen

# neo 服务api调用
neo:
  ums-api-neo-tree-id: 12051
  url: http://neo.b2cop.b2c.srv
  token@kc-sid: oa-infra.g
  token: GDAywDx+pXPWsNS9PLeFuC7VsUBquaPhi/d5QdiFUl2nPBLcPk5Im4N8VyOnFP8JakMYElpEbT/lv0boviLm6S6uCMIs/xgQoyUR7xXYRMaaJcLPriDcNRgUDlSwn9TfSBm1eAd74B7L/4lezxYA


oaucf:
  auth:
    appId: Ottp3cfCKfDF          # 应用的id
    appSecret@kc-sid: oa-infra.g
    appSecret: GDCdrI1zf/xUWNXfJCcexQa2+dlOLJQLPXLzv4UzhCLgvwEarhP14V3Db+M+7faW0zwYEhAuwkPF1kDKhrN2SxowizkB/xgQSOd/0/SaTCKsfVkhvHHWDBgUCPQFxV7u164jmnxVHVzIzoayCTQA  # 应用的secret
  bpm:
    enabled: true
    url: https://bpm-infra.test.mioffice.cn/runtime    # api的url
    connectTimeout: 10          # 连接超时时间 单位秒
    readTimeout: 60             # 读取超时时间 单位秒

nacos:
  config:
    bootstrap:
      enable: true
    remote-first: true
    # server端地址，填写Nacos对应环境的服务端地址
    server-addr: http://staging-nacos.api.xiaomi.net:80
    # 命名空间设置，注意此处需要填：namespaceId的值，public命名空间对应的id为空
    namespace: info_oa_test
    #填写Nacos提供的登陆用户名和密码
    username: info_oa_test
    password@kc-sid: oa-infra.g
    password: GDCNtNQMsoYZhjhSCoQ/WZbRWK/kEO8P01iiKcMJ8gUNVy+XoMDrBGt73D3JChSjHy4YEjpv9NHTIktMrJ3e2IisG3rO/xgQ/5+F2MFFTFCKGGFU5y8bJRgUjTDHvY7pe34x3cD2wW8v0uMIBvkA
    #使用nacos配置集的dataId，也就是在上一步的nacos console新建的配置文件
    data-ids: common_config
    #开启自动刷新,默认为false
    auto-refresh: true
    #配置中心的组配置,默认为DEFAULT_GROUP
    group: infra.ums
    #配置中心配置文件文件类型
    type: yaml
    #拉取配置的重试次数，默认3次
    max-retry: 3
    #长轮询任务重试时间，单位为毫秒
    config-retry-time: 2000
    #拉取配置超时时间 单位毫秒 默认30000
    config-long-poll-timeout: 30000
    #监听器首次添加时拉取远端配置 默认false
    enable-remote-sync-config: true