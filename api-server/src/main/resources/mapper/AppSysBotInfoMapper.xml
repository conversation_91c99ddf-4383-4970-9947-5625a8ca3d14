<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppSysBotInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppSysBotInfo">
    <!--@mbg.generated-->
    <!--@Table app_sys_bot_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_sys_id" jdbcType="BIGINT" property="appSysId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="bot_app_id" jdbcType="VARCHAR" property="botAppId" />
    <result column="channel" jdbcType="TINYINT" property="channel" />
    <result column="is_out_send" jdbcType="TINYINT" property="isOutSend" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="bot_biz_id" jdbcType="VARCHAR" property="botBizId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_sys_id, app_id, bot_app_id, channel, is_out_send, create_time, update_time, 
    bot_biz_id
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_sys_bot_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_sys_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appSysId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bot_app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.botAppId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="is_out_send = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isOutSend,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="bot_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_sys_bot_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_sys_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appSysId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appSysId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="bot_app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.botAppId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.botAppId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channel != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_out_send = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isOutSend != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isOutSend,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="bot_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.botBizId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_bot_info
    (app_sys_id, app_id, bot_app_id, channel, is_out_send, create_time, update_time, 
      bot_biz_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appSysId,jdbcType=BIGINT}, #{item.appId,jdbcType=VARCHAR}, #{item.botAppId,jdbcType=VARCHAR}, 
        #{item.channel,jdbcType=TINYINT}, #{item.isOutSend,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=BIGINT}, #{item.botBizId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysBotInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_bot_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      app_sys_id,
      app_id,
      bot_app_id,
      channel,
      is_out_send,
      create_time,
      update_time,
      bot_biz_id,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{appSysId,jdbcType=BIGINT},
      #{appId,jdbcType=VARCHAR},
      #{botAppId,jdbcType=VARCHAR},
      #{channel,jdbcType=TINYINT},
      #{isOutSend,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
      #{botBizId,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      app_sys_id = #{appSysId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=VARCHAR},
      bot_app_id = #{botAppId,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=TINYINT},
      is_out_send = #{isOutSend,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      bot_biz_id = #{botBizId,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysBotInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_bot_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appSysId != null">
        app_sys_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="botAppId != null">
        bot_app_id,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="isOutSend != null">
        is_out_send,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="botBizId != null">
        bot_biz_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appSysId != null">
        #{appSysId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="botAppId != null">
        #{botAppId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=TINYINT},
      </if>
      <if test="isOutSend != null">
        #{isOutSend,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="botBizId != null">
        #{botBizId,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="appSysId != null">
        app_sys_id = #{appSysId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="botAppId != null">
        bot_app_id = #{botAppId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=TINYINT},
      </if>
      <if test="isOutSend != null">
        is_out_send = #{isOutSend,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="botBizId != null">
        bot_biz_id = #{botBizId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>