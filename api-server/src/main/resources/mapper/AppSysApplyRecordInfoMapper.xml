<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppSysApplyRecordInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo">
    <!--@mbg.generated-->
    <!--@Table app_sys_apply_record_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_status" jdbcType="TINYINT" property="applyStatus" />
    <result column="apply_desc" jdbcType="LONGVARCHAR" property="applyDesc" />
    <result column="apply_user_username" jdbcType="VARCHAR" property="applyUserUsername" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="approval_username" jdbcType="VARCHAR" property="approvalUsername" />
    <result column="approval_name" jdbcType="VARCHAR" property="approvalName" />
    <result column="approval_desc" jdbcType="LONGVARCHAR" property="approvalDesc" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="app_sys_id" jdbcType="BIGINT" property="appSysId" />
    <result column="valid" jdbcType="TINYINT" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, apply_status, apply_desc, apply_user_username, apply_user_name, approval_username, 
    approval_name, approval_desc, create_time, update_time, app_sys_id, `valid`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_sys_apply_record_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="apply_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.applyStatus,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="apply_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.applyDesc,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="apply_user_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.applyUserUsername,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="apply_user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.applyUserName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="approval_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.approvalUsername,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="approval_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.approvalName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="approval_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.approvalDesc,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="app_sys_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appSysId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`valid` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.valid,jdbcType=TINYINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_sys_apply_record_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="apply_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyStatus != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.applyStatus,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="apply_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyDesc != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.applyDesc,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="apply_user_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyUserUsername != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.applyUserUsername,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="apply_user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyUserName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.applyUserName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="approval_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.approvalUsername != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.approvalUsername,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="approval_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.approvalName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.approvalName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="approval_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.approvalDesc != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.approvalDesc,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_sys_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appSysId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appSysId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`valid` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.valid != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.valid,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_apply_record_info
    (apply_status, apply_desc, apply_user_username, apply_user_name, approval_username, 
      approval_name, approval_desc, create_time, update_time, app_sys_id, `valid`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.applyStatus,jdbcType=TINYINT}, #{item.applyDesc,jdbcType=LONGVARCHAR}, #{item.applyUserUsername,jdbcType=VARCHAR}, 
        #{item.applyUserName,jdbcType=VARCHAR}, #{item.approvalUsername,jdbcType=VARCHAR}, 
        #{item.approvalName,jdbcType=VARCHAR}, #{item.approvalDesc,jdbcType=LONGVARCHAR}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.appSysId,jdbcType=BIGINT}, 
        #{item.valid,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_apply_record_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      apply_status,
      apply_desc,
      apply_user_username,
      apply_user_name,
      approval_username,
      approval_name,
      approval_desc,
      create_time,
      update_time,
      app_sys_id,
      `valid`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{applyStatus,jdbcType=TINYINT},
      #{applyDesc,jdbcType=LONGVARCHAR},
      #{applyUserUsername,jdbcType=VARCHAR},
      #{applyUserName,jdbcType=VARCHAR},
      #{approvalUsername,jdbcType=VARCHAR},
      #{approvalName,jdbcType=VARCHAR},
      #{approvalDesc,jdbcType=LONGVARCHAR},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
      #{appSysId,jdbcType=BIGINT},
      #{valid,jdbcType=TINYINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      apply_status = #{applyStatus,jdbcType=TINYINT},
      apply_desc = #{applyDesc,jdbcType=LONGVARCHAR},
      apply_user_username = #{applyUserUsername,jdbcType=VARCHAR},
      apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      approval_username = #{approvalUsername,jdbcType=VARCHAR},
      approval_name = #{approvalName,jdbcType=VARCHAR},
      approval_desc = #{approvalDesc,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      app_sys_id = #{appSysId,jdbcType=BIGINT},
      `valid` = #{valid,jdbcType=TINYINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_apply_record_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applyStatus != null">
        apply_status,
      </if>
      <if test="applyDesc != null">
        apply_desc,
      </if>
      <if test="applyUserUsername != null">
        apply_user_username,
      </if>
      <if test="applyUserName != null">
        apply_user_name,
      </if>
      <if test="approvalUsername != null">
        approval_username,
      </if>
      <if test="approvalName != null">
        approval_name,
      </if>
      <if test="approvalDesc != null">
        approval_desc,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="appSysId != null">
        app_sys_id,
      </if>
      <if test="valid != null">
        `valid`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="applyStatus != null">
        #{applyStatus,jdbcType=TINYINT},
      </if>
      <if test="applyDesc != null">
        #{applyDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="applyUserUsername != null">
        #{applyUserUsername,jdbcType=VARCHAR},
      </if>
      <if test="applyUserName != null">
        #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="approvalUsername != null">
        #{approvalUsername,jdbcType=VARCHAR},
      </if>
      <if test="approvalName != null">
        #{approvalName,jdbcType=VARCHAR},
      </if>
      <if test="approvalDesc != null">
        #{approvalDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="appSysId != null">
        #{appSysId,jdbcType=BIGINT},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=TINYINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="applyStatus != null">
        apply_status = #{applyStatus,jdbcType=TINYINT},
      </if>
      <if test="applyDesc != null">
        apply_desc = #{applyDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="applyUserUsername != null">
        apply_user_username = #{applyUserUsername,jdbcType=VARCHAR},
      </if>
      <if test="applyUserName != null">
        apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="approvalUsername != null">
        approval_username = #{approvalUsername,jdbcType=VARCHAR},
      </if>
      <if test="approvalName != null">
        approval_name = #{approvalName,jdbcType=VARCHAR},
      </if>
      <if test="approvalDesc != null">
        approval_desc = #{approvalDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="appSysId != null">
        app_sys_id = #{appSysId,jdbcType=BIGINT},
      </if>
      <if test="valid != null">
        `valid` = #{valid,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
</mapper>