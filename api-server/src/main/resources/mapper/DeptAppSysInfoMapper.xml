<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.DeptAppSysInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.DeptAppSysInfo">
    <!--@mbg.generated-->
    <!--@Table dept_app_sys_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_sys_id" jdbcType="BIGINT" property="appSysId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_sys_id, app_id, dept_id, dept_name, create_time, update_time
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update dept_app_sys_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_sys_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appSysId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dept_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into dept_app_sys_info
    (app_sys_id, app_id, dept_id, dept_name, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appSysId,jdbcType=BIGINT}, #{item.appId,jdbcType=VARCHAR}, #{item.deptId,jdbcType=VARCHAR}, 
        #{item.deptName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.DeptAppSysInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into dept_app_sys_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      app_sys_id,
      app_id,
      dept_id,
      dept_name,
      create_time,
      update_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{appSysId,jdbcType=BIGINT},
      #{appId,jdbcType=VARCHAR},
      #{deptId,jdbcType=VARCHAR},
      #{deptName,jdbcType=VARCHAR},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      app_sys_id = #{appSysId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=VARCHAR},
      dept_name = #{deptName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.DeptAppSysInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into dept_app_sys_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appSysId != null">
        app_sys_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="deptName != null">
        dept_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appSysId != null">
        #{appSysId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="appSysId != null">
        app_sys_id = #{appSysId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        dept_name = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>