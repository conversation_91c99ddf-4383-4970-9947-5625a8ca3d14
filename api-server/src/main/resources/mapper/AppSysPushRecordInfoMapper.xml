<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppSysPushRecordInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppSysPushRecordInfo">
    <!--@mbg.generated-->
    <!--@Table app_sys_push_record_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="channel" jdbcType="TINYINT" property="channel" />
    <result column="bot_app_id" jdbcType="VARCHAR" property="botAppId" />
    <result column="title_cn" jdbcType="LONGVARCHAR" property="titleCn" />
    <result column="title_en" jdbcType="LONGVARCHAR" property="titleEn" />
    <result column="extra_id" jdbcType="VARCHAR" property="extraId" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="template_biz_id" jdbcType="VARCHAR" property="templateBizId" />
    <result column="all_count" jdbcType="BIGINT" property="allCount" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="bot_biz_id" jdbcType="VARCHAR" property="botBizId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, channel, bot_app_id, title_cn, title_en, extra_id, template_id, template_biz_id,
    all_count, create_time, update_time, bot_biz_id
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_sys_push_record_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="bot_app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.botAppId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="title_cn = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.titleCn,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="title_en = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.titleEn,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="extra_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="template_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="template_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.templateBizId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="all_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="bot_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_sys_push_record_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channel != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="bot_app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.botAppId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.botAppId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="title_cn = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.titleCn != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.titleCn,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="title_en = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.titleEn != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.titleEn,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="extra_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extraId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="template_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="template_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateBizId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.templateBizId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="all_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.allCount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="bot_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.botBizId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_push_record_info
    (app_id, channel, bot_app_id, title_cn, title_en, extra_id, template_id, template_biz_id,
      all_count, create_time, update_time, bot_biz_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.channel,jdbcType=TINYINT}, #{item.botAppId,jdbcType=VARCHAR},
        #{item.titleCn,jdbcType=LONGVARCHAR}, #{item.titleEn,jdbcType=LONGVARCHAR}, #{item.extraId,jdbcType=VARCHAR},
        #{item.templateId,jdbcType=BIGINT}, #{item.templateBizId,jdbcType=VARCHAR}, #{item.allCount,jdbcType=BIGINT},
        #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.botBizId,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysPushRecordInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_push_record_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      app_id,
      channel,
      bot_app_id,
      title_cn,
      title_en,
      extra_id,
      template_id,
      template_biz_id,
      all_count,
      create_time,
      update_time,
      bot_biz_id,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{appId,jdbcType=VARCHAR},
      #{channel,jdbcType=TINYINT},
      #{botAppId,jdbcType=VARCHAR},
      #{titleCn,jdbcType=LONGVARCHAR},
      #{titleEn,jdbcType=LONGVARCHAR},
      #{extraId,jdbcType=VARCHAR},
      #{templateId,jdbcType=BIGINT},
      #{templateBizId,jdbcType=VARCHAR},
      #{allCount,jdbcType=BIGINT},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
      #{botBizId,jdbcType=VARCHAR},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      app_id = #{appId,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=TINYINT},
      bot_app_id = #{botAppId,jdbcType=VARCHAR},
      title_cn = #{titleCn,jdbcType=LONGVARCHAR},
      title_en = #{titleEn,jdbcType=LONGVARCHAR},
      extra_id = #{extraId,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=BIGINT},
      template_biz_id = #{templateBizId,jdbcType=VARCHAR},
      all_count = #{allCount,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      bot_biz_id = #{botBizId,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysPushRecordInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_push_record_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="botAppId != null">
        bot_app_id,
      </if>
      <if test="titleCn != null">
        title_cn,
      </if>
      <if test="titleEn != null">
        title_en,
      </if>
      <if test="extraId != null">
        extra_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateBizId != null">
        template_biz_id,
      </if>
      <if test="allCount != null">
        all_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="botBizId != null">
        bot_biz_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=TINYINT},
      </if>
      <if test="botAppId != null">
        #{botAppId,jdbcType=VARCHAR},
      </if>
      <if test="titleCn != null">
        #{titleCn,jdbcType=LONGVARCHAR},
      </if>
      <if test="titleEn != null">
        #{titleEn,jdbcType=LONGVARCHAR},
      </if>
      <if test="extraId != null">
        #{extraId,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="templateBizId != null">
        #{templateBizId,jdbcType=VARCHAR},
      </if>
      <if test="allCount != null">
        #{allCount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="botBizId != null">
        #{botBizId,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=TINYINT},
      </if>
      <if test="botAppId != null">
        bot_app_id = #{botAppId,jdbcType=VARCHAR},
      </if>
      <if test="titleCn != null">
        title_cn = #{titleCn,jdbcType=LONGVARCHAR},
      </if>
      <if test="titleEn != null">
        title_en = #{titleEn,jdbcType=LONGVARCHAR},
      </if>
      <if test="extraId != null">
        extra_id = #{extraId,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="templateBizId != null">
        template_biz_id = #{templateBizId,jdbcType=VARCHAR},
      </if>
      <if test="allCount != null">
        all_count = #{allCount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="botBizId != null">
        bot_biz_id = #{botBizId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>



    <resultMap id="AppMessageCountBoMap" type="com.mioffice.ums.open.server.entity.bo.AppMessageCountBo">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="all_count" property="allCount" />
        <collection column="id" ofType="com.mioffice.ums.open.server.entity.bo.AppManagerBo" property="appManagerList" select="selectAppManagerList">
            <result column="username" property="username" />
            <result column="name" property="name" />
        </collection>
        <collection column="id" ofType="com.mioffice.ums.open.server.entity.bo.DeptBo" property="deptList" select="selectDeptList">
            <result column="dept_id" property="deptId" />
            <result column="dept_name" property="deptName" />
        </collection>
    </resultMap>
    <select id="selectAppManagerList" parameterType="long" resultType="com.mioffice.ums.open.server.entity.bo.AppManagerBo">
        select name, username
        from user_app_sys_info
        where app_sys_id = #{id}
    </select>
    <select id="selectDeptList" parameterType="long" resultType="com.mioffice.ums.open.server.entity.bo.DeptBo">
        select dept_id, dept_name
        from dept_app_sys_info
        where app_sys_id = #{id}
    </select>
    <select id="selectTopCount" resultMap="AppMessageCountBoMap">
        select asi.id as id, aspri.app_id as app_id, asi.app_name as app_name, sum(aspri.all_count) as all_count
        from app_sys_push_record_info aspri
                 join app_sys_info asi on aspri.app_id = asi.app_id
        where aspri.create_time between #{beginTime,jdbcType=BIGINT} and #{endTime,jdbcType=BIGINT}
        group by aspri.app_id
        order by all_count desc
        limit #{topCount,jdbcType=INTEGER}
    </select>


    <select id="selectAppChannelCount" resultType="com.mioffice.ums.open.server.entity.bo.AppChannelCountBo">
        select sum(aspri.all_count) as all_count, aspri.channel from app_sys_push_record_info aspri
        join app_sys_info asi on aspri.app_id = asi.app_id
        where aspri.create_time between #{beginTime,jdbcType=BIGINT} and #{endTime,jdbcType=BIGINT}
        <if test="appSysIdList != null and appSysIdList.size() != 0">
            and asi.id in
            <foreach close=")" collection="appSysIdList" item="item" open="(" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        group by aspri.channel
    </select>


  <select id="selectAppTaskLog" resultType="com.mioffice.ums.open.server.bo.AppTaskLogBo">
        select app.app_id as appId, app.channel, app.all_count as allCount,
        app.title_cn as titleCn, app.title_en as titleEn, app.extra_id as extraId, app.create_time as createTime
        from app_sys_push_record_info app
        <where>
            1 = 1
            <if test="appId != null and appId != ''">
                and app.app_id =  #{appId,jdbcType=VARCHAR}
            </if>
            <if test="extraId != null and extraId != ''">
                and app.extra_id like concat('%', #{extraId,jdbcType=VARCHAR}, '%')
            </if>
            <if test="channels != null and channels.size() != 0">
                and app.channel in
                <foreach close=")" collection="channels" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="startTime != null">
                and app.create_time  &gt;= #{startTime,jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and app.create_time  &lt;= #{endTime,jdbcType=BIGINT}
            </if>
        </where>
        order by createTime desc
  </select>


</mapper>