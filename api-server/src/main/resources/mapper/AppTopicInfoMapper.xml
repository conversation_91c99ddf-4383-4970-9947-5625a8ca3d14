<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppTopicInfoMapper">

    <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppTopicInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="topic" column="topic" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="bpmInstanceId" column="bpm_instance_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,topic,,app_id,create_by,
        update_by,create_time,update_time,
        status,bpm_instance_id
    </sql>

    <select id="getAvailableByRobotId" resultMap="BaseResultMap">
        select ati.*
        from app_sys_bot_info asbi
                 join app_topic_info ati on asbi.app_id = ati.app_id
        where asbi.bot_app_id = #{robotId,jdbcType=VARCHAR}
          and ati.status = 2
    </select>
</mapper>
