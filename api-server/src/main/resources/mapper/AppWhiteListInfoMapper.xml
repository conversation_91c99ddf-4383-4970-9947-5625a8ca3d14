<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppWhiteListInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppWhiteListInfo">
    <!--@mbg.generated-->
    <!--@Table app_white_list_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="channel" jdbcType="TINYINT" property="channel" />
    <result column="white_id" jdbcType="VARCHAR" property="whiteId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, channel, white_id, create_time, update_time
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_white_list_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="white_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.whiteId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_white_list_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channel != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="white_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.whiteId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.whiteId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_white_list_info
    (app_id, channel, white_id, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.channel,jdbcType=TINYINT}, #{item.whiteId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppWhiteListInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_white_list_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      app_id,
      channel,
      white_id,
      create_time,
      update_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{appId,jdbcType=VARCHAR},
      #{channel,jdbcType=TINYINT},
      #{whiteId,jdbcType=VARCHAR},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      app_id = #{appId,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=TINYINT},
      white_id = #{whiteId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppWhiteListInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_white_list_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="whiteId != null">
        white_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=TINYINT},
      </if>
      <if test="whiteId != null">
        #{whiteId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=TINYINT},
      </if>
      <if test="whiteId != null">
        white_id = #{whiteId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <insert id="batchInsertUpdate" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into app_white_list_info
    (app_id, channel, white_id, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.channel,jdbcType=TINYINT}, #{item.whiteId,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT})
    </foreach>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="appWhiteListInfo.updateTime != null">
        update_time = #{appWhiteListInfo.updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>