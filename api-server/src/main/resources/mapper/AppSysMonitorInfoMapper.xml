<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppSysMonitorInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppSysMonitorInfo">
        <!--@mbg.generated-->
        <!--@Table app_sys_monitor_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_sys_id" jdbcType="BIGINT" property="appSysId"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="all_count" jdbcType="BIGINT" property="allCount"/>
        <result column="push_count" jdbcType="BIGINT" property="pushCount"/>
        <result column="todo_count" jdbcType="BIGINT" property="todoCount"/>
        <result column="fail_count" jdbcType="BIGINT" property="failCount"/>
        <result column="interrupt_count" jdbcType="BIGINT" property="interruptCount"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="push_date" jdbcType="BIGINT" property="pushDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, app_sys_id, app_id, channel, all_count, push_count, todo_count, fail_count, interrupt_count,
        create_time, update_time, push_date
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update app_sys_monitor_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="app_sys_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.appSysId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="all_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.allCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="push_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.pushCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="todo_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.todoCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="fail_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.failCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="interrupt_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.interruptCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="push_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.pushDate,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_sys_monitor_info
        (app_sys_id, app_id, channel, all_count, push_count, todo_count, fail_count, interrupt_count,
        create_time, update_time, push_date)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.appSysId,jdbcType=BIGINT}, #{item.appId,jdbcType=VARCHAR}, #{item.channel,jdbcType=TINYINT},
            #{item.allCount,jdbcType=BIGINT}, #{item.pushCount,jdbcType=BIGINT}, #{item.todoCount,jdbcType=BIGINT},
            #{item.failCount,jdbcType=BIGINT}, #{item.interruptCount,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=BIGINT}, #{item.pushDate,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysMonitorInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_sys_monitor_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            app_sys_id,
            app_id,
            channel,
            all_count,
            push_count,
            todo_count,
            fail_count,
            interrupt_count,
            create_time,
            update_time,
            push_date,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{appSysId,jdbcType=BIGINT},
            #{appId,jdbcType=VARCHAR},
            #{channel,jdbcType=TINYINT},
            #{allCount,jdbcType=BIGINT},
            #{pushCount,jdbcType=BIGINT},
            #{todoCount,jdbcType=BIGINT},
            #{failCount,jdbcType=BIGINT},
            #{interruptCount,jdbcType=BIGINT},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{pushDate,jdbcType=BIGINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            app_sys_id = #{appSysId,jdbcType=BIGINT},
            app_id = #{appId,jdbcType=VARCHAR},
            channel = #{channel,jdbcType=TINYINT},
            all_count = #{allCount,jdbcType=BIGINT},
            push_count = #{pushCount,jdbcType=BIGINT},
            todo_count = #{todoCount,jdbcType=BIGINT},
            fail_count = #{failCount,jdbcType=BIGINT},
            interrupt_count = #{interruptCount,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            push_date = #{pushDate,jdbcType=BIGINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysMonitorInfo"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_sys_monitor_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="appSysId != null">
                app_sys_id,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="allCount != null">
                all_count,
            </if>
            <if test="pushCount != null">
                push_count,
            </if>
            <if test="todoCount != null">
                todo_count,
            </if>
            <if test="failCount != null">
                fail_count,
            </if>
            <if test="interruptCount != null">
                interrupt_count,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="pushDate != null">
                push_date,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="appSysId != null">
                #{appSysId,jdbcType=BIGINT},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=TINYINT},
            </if>
            <if test="allCount != null">
                #{allCount,jdbcType=BIGINT},
            </if>
            <if test="pushCount != null">
                #{pushCount,jdbcType=BIGINT},
            </if>
            <if test="todoCount != null">
                #{todoCount,jdbcType=BIGINT},
            </if>
            <if test="failCount != null">
                #{failCount,jdbcType=BIGINT},
            </if>
            <if test="interruptCount != null">
                #{interruptCount,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="pushDate != null">
                #{pushDate,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="appSysId != null">
                app_sys_id = #{appSysId,jdbcType=BIGINT},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=TINYINT},
            </if>
            <if test="allCount != null">
                all_count = #{allCount,jdbcType=BIGINT},
            </if>
            <if test="pushCount != null">
                push_count = #{pushCount,jdbcType=BIGINT},
            </if>
            <if test="todoCount != null">
                todo_count = #{todoCount,jdbcType=BIGINT},
            </if>
            <if test="failCount != null">
                fail_count = #{failCount,jdbcType=BIGINT},
            </if>
            <if test="interruptCount != null">
                interrupt_count = #{interruptCount,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="pushDate != null">
                push_date = #{pushDate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>


    <select id="selectAppPushRecords" resultType="com.mioffice.ums.open.server.bo.AppSysPushRecordBo">
        select FROM_UNIXTIME(push_date/1000,'%Y-%m-%d') as dateStr, channel, all_count as pushCount
        from app_sys_monitor_info
        <where>
            and 1 = 1
            <if test="startTime != null">
                and push_date &gt;= #{startTime,jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and push_date &lt;= #{endTime,jdbcType=BIGINT}
            </if>
            <if test="systemIdList != null and systemIdList.size() != 0">
                and app_sys_id in
                <foreach close=")" collection="systemIdList" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectReportData" resultType="com.mioffice.ums.open.server.entity.bo.AppReportBO">
        select asmi.app_id, asi.app_name, sum(all_count) as all_count, sum(push_count) as push_count, sum(fail_count + interrupt_count) as fail_count
        from app_sys_monitor_info asmi
                 join app_sys_info asi on asi.app_id = asmi.app_id
        where app_sys_status = 2
          and push_date between #{beginTime,jdbcType=BIGINT} and #{endTime,jdbcType=BIGINT}
        group by asmi.app_id;
    </select>
</mapper>