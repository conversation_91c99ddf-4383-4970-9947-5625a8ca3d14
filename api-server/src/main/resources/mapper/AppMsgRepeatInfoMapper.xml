<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppMsgRepeatInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo">
    <!--@mbg.generated-->
    <!--@Table app_msg_repeat_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="bot_biz_id" jdbcType="VARCHAR" property="botBizId" />
    <result column="content_md5" jdbcType="VARCHAR" property="contentMd5" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="temp_user_md5" jdbcType="VARCHAR" property="tempUserMd5" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="channel" jdbcType="TINYINT" property="channel" />
    <result column="template_biz_id" jdbcType="VARCHAR" property="templateBizId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, bot_biz_id, content_md5, create_time, update_time, temp_user_md5, user_id, 
    channel, template_biz_id
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_msg_repeat_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bot_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="content_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.contentMd5,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="temp_user_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.tempUserMd5,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="template_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.templateBizId,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_msg_repeat_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="bot_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.botBizId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="content_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contentMd5 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.contentMd5,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="temp_user_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tempUserMd5 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tempUserMd5,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channel != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="template_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateBizId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.templateBizId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_msg_repeat_info
    (app_id, bot_biz_id, content_md5, create_time, update_time, temp_user_md5, user_id, 
      channel, template_biz_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.botBizId,jdbcType=VARCHAR}, #{item.contentMd5,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.tempUserMd5,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=VARCHAR}, #{item.channel,jdbcType=TINYINT}, #{item.templateBizId,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_msg_repeat_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      app_id,
      bot_biz_id,
      content_md5,
      create_time,
      update_time,
      temp_user_md5,
      user_id,
      channel,
      template_biz_id,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{appId,jdbcType=VARCHAR},
      #{botBizId,jdbcType=VARCHAR},
      #{contentMd5,jdbcType=VARCHAR},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
      #{tempUserMd5,jdbcType=VARCHAR},
      #{userId,jdbcType=VARCHAR},
      #{channel,jdbcType=TINYINT},
      #{templateBizId,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      app_id = #{appId,jdbcType=VARCHAR},
      bot_biz_id = #{botBizId,jdbcType=VARCHAR},
      content_md5 = #{contentMd5,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      temp_user_md5 = #{tempUserMd5,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=TINYINT},
      template_biz_id = #{templateBizId,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_msg_repeat_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="botBizId != null">
        bot_biz_id,
      </if>
      <if test="contentMd5 != null">
        content_md5,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tempUserMd5 != null">
        temp_user_md5,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="templateBizId != null">
        template_biz_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="botBizId != null">
        #{botBizId,jdbcType=VARCHAR},
      </if>
      <if test="contentMd5 != null">
        #{contentMd5,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="tempUserMd5 != null">
        #{tempUserMd5,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=TINYINT},
      </if>
      <if test="templateBizId != null">
        #{templateBizId,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="botBizId != null">
        bot_biz_id = #{botBizId,jdbcType=VARCHAR},
      </if>
      <if test="contentMd5 != null">
        content_md5 = #{contentMd5,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="tempUserMd5 != null">
        temp_user_md5 = #{tempUserMd5,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=TINYINT},
      </if>
      <if test="templateBizId != null">
        template_biz_id = #{templateBizId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <!-- 去重逻辑开启的话，就不进行报警 -->
    <select id="selectContentMd5CountByRecentTime" resultType="com.mioffice.ums.open.server.entity.bo.MsgMd5RepeatBO">
        select tp.count, tp.md5, tp.app_id, tp.bot_biz_id, template_biz_id
        from (
                 select count(*) as count, content_md5 as md5, app_id, bot_biz_id, template_biz_id
                 from app_msg_repeat_info
                 where create_time between #{beginTime,jdbcType=BIGINT} and #{endTime,jdbcType=BIGINT}
                 group by content_md5
             ) as tp
                 join app_sys_info asi on asi.app_id = tp.app_id
        where tp.count &gt;= asi.alarm_threshold
          and asi.alarm_status = 1
          and asi.app_sys_status = 2
          and asi.deduplicate_status = 0;
    </select>
</mapper>