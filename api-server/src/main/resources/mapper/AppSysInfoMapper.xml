<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppSysInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppSysInfo">
        <!--@mbg.generated-->
        <!--@Table app_sys_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="app_secret" jdbcType="VARCHAR" property="appSecret"/>
        <result column="app_name" jdbcType="VARCHAR" property="appName"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="qps" jdbcType="INTEGER" property="qps"/>
        <result column="app_sys_status" jdbcType="TINYINT" property="appSysStatus"/>
        <result column="old_app_sys_status" jdbcType="TINYINT" property="oldAppSysStatus"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="alarm_status" jdbcType="BOOLEAN" property="alarmStatus"/>
        <result column="alarm_threshold" jdbcType="INTEGER" property="alarmThreshold"/>
        <result column="deduplicate_status" jdbcType="TINYINT" property="deduplicateStatus"/>
        <result column="white_status" jdbcType="TINYINT" property="whiteStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, app_id, app_secret, app_name, create_time, update_time, qps, app_sys_status,
        old_app_sys_status, update_username, update_name, create_name, create_username, alarm_status,
        alarm_threshold, deduplicate_status, white_status
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update app_sys_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="app_secret = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.appSecret,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="app_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.appName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="qps = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.qps,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="app_sys_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.appSysStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="old_app_sys_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.oldAppSysStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="alarm_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.alarmStatus,jdbcType=BOOLEAN}
                </foreach>
            </trim>
            <trim prefix="alarm_threshold = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.alarmThreshold,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="deduplicate_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deduplicateStatus,jdbcType=TINYINT}
                </foreach>
            </trim>

            <trim prefix="white_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.whiteStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update app_sys_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="app_secret = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appSecret != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.appSecret,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="app_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.appName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="qps = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.qps != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.qps,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="app_sys_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appSysStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.appSysStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="old_app_sys_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.oldAppSysStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.oldAppSysStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createUsername != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="alarm_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.alarmStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.alarmStatus,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="alarm_threshold = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.alarmThreshold != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.alarmThreshold,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deduplicate_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deduplicateStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deduplicateStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>

            <trim prefix="white_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.whiteStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.whiteStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_sys_info
        (app_id, app_secret, app_name, create_time, update_time, qps, app_sys_status, old_app_sys_status,
        update_username, update_name, create_name, create_username, alarm_status, alarm_threshold,
        deduplicate_status, white_status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.appId,jdbcType=VARCHAR}, #{item.appSecret,jdbcType=VARCHAR}, #{item.appName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.qps,jdbcType=INTEGER},
            #{item.appSysStatus,jdbcType=TINYINT}, #{item.oldAppSysStatus,jdbcType=TINYINT},
            #{item.updateUsername,jdbcType=VARCHAR}, #{item.updateName,jdbcType=VARCHAR},
            #{item.createName,jdbcType=VARCHAR},
            #{item.createUsername,jdbcType=VARCHAR}, #{item.alarmStatus,jdbcType=BOOLEAN},
            #{item.alarmThreshold,jdbcType=INTEGER}, #{item.deduplicateStatus,jdbcType=TINYINT},
            #{item.whiteStatus,jdbcType=TINYINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.open.server.entity.info.AppSysInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_sys_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            app_id,
            app_secret,
            app_name,
            create_time,
            update_time,
            qps,
            app_sys_status,
            old_app_sys_status,
            update_username,
            update_name,
            create_name,
            create_username,
            alarm_status,
            alarm_threshold,
            deduplicate_status,
            white_status,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{appId,jdbcType=VARCHAR},
            #{appSecret,jdbcType=VARCHAR},
            #{appName,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{qps,jdbcType=INTEGER},
            #{appSysStatus,jdbcType=TINYINT},
            #{oldAppSysStatus,jdbcType=TINYINT},
            #{updateUsername,jdbcType=VARCHAR},
            #{updateName,jdbcType=VARCHAR},
            #{createName,jdbcType=VARCHAR},
            #{createUsername,jdbcType=VARCHAR},
            #{alarmStatus,jdbcType=BOOLEAN},
            #{alarmThreshold,jdbcType=INTEGER},
            #{deduplicateStatus,jdbcType=TINYINT},
            #{whiteStatus,jdbcType=TINYINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            app_id = #{appId,jdbcType=VARCHAR},
            app_secret = #{appSecret,jdbcType=VARCHAR},
            app_name = #{appName,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            qps = #{qps,jdbcType=INTEGER},
            app_sys_status = #{appSysStatus,jdbcType=TINYINT},
            old_app_sys_status = #{oldAppSysStatus,jdbcType=TINYINT},
            update_username = #{updateUsername,jdbcType=VARCHAR},
            update_name = #{updateName,jdbcType=VARCHAR},
            create_name = #{createName,jdbcType=VARCHAR},
            create_username = #{createUsername,jdbcType=VARCHAR},
            alarm_status = #{alarmStatus,jdbcType=BOOLEAN},
            alarm_threshold = #{alarmThreshold,jdbcType=INTEGER},
            deduplicate_status = #{deduplicateStatus,jdbcType=TINYINT},
            white_status = #{whiteStatus,jdbcType=TINYINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.open.server.entity.info.AppSysInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_sys_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="appSecret != null">
                app_secret,
            </if>
            <if test="appName != null">
                app_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="qps != null">
                qps,
            </if>
            <if test="appSysStatus != null">
                app_sys_status,
            </if>
            <if test="oldAppSysStatus != null">
                old_app_sys_status,
            </if>
            <if test="updateUsername != null">
                update_username,
            </if>
            <if test="updateName != null">
                update_name,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createUsername != null">
                create_username,
            </if>
            <if test="alarmStatus != null">
                alarm_status,
            </if>
            <if test="alarmThreshold != null">
                alarm_threshold,
            </if>
            <if test="deduplicateStatus != null">
                deduplicate_status,
            </if>
            <if test="whiteStatus != null">
                white_status,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="appSecret != null">
                #{appSecret,jdbcType=VARCHAR},
            </if>
            <if test="appName != null">
                #{appName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="qps != null">
                #{qps,jdbcType=INTEGER},
            </if>
            <if test="appSysStatus != null">
                #{appSysStatus,jdbcType=TINYINT},
            </if>
            <if test="oldAppSysStatus != null">
                #{oldAppSysStatus,jdbcType=TINYINT},
            </if>
            <if test="updateUsername != null">
                #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateName != null">
                #{updateName,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createUsername != null">
                #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="alarmStatus != null">
                #{alarmStatus,jdbcType=BOOLEAN},
            </if>
            <if test="alarmThreshold != null">
                #{alarmThreshold,jdbcType=INTEGER},
            </if>
            <if test="deduplicateStatus != null">
                #{deduplicateStatus,jdbcType=TINYINT},
            </if>
            <if test="whiteStatus != null">
                #{whiteStatus,jdbcType=TINYINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="appSecret != null">
                app_secret = #{appSecret,jdbcType=VARCHAR},
            </if>
            <if test="appName != null">
                app_name = #{appName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="qps != null">
                qps = #{qps,jdbcType=INTEGER},
            </if>
            <if test="appSysStatus != null">
                app_sys_status = #{appSysStatus,jdbcType=TINYINT},
            </if>
            <if test="oldAppSysStatus != null">
                old_app_sys_status = #{oldAppSysStatus,jdbcType=TINYINT},
            </if>
            <if test="updateUsername != null">
                update_username = #{updateUsername,jdbcType=VARCHAR},
            </if>
            <if test="updateName != null">
                update_name = #{updateName,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createUsername != null">
                create_username = #{createUsername,jdbcType=VARCHAR},
            </if>
            <if test="alarmStatus != null">
                alarm_status = #{alarmStatus,jdbcType=BOOLEAN},
            </if>
            <if test="alarmThreshold != null">
                alarm_threshold = #{alarmThreshold,jdbcType=INTEGER},
            </if>
            <if test="deduplicateStatus != null">
                deduplicate_status = #{deduplicateStatus,jdbcType=TINYINT},
            </if>
            <if test="whiteStatus != null">
                white_status = #{whiteStatus,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <select id="selectManager" parameterType="Long"
            resultType="com.mioffice.ums.open.server.entity.bo.UsernameAndNameBO">
        select username,
               name
        from user_app_sys_info
        where app_sys_id = #{id}
          and user_type = 1;
    </select>
    <select id="selectChannel" parameterType="Long" resultType="int">
        select distinct channel
        from app_sys_bot_info
        where app_sys_id = #{id};
    </select>
    <select id="selectPushCount" parameterType="String" resultType="long">
        select count(all_count)
        from (select all_count
              from app_sys_push_record_info
              where app_id = #{app_id}
                and create_time &gt; (REPLACE(unix_timestamp(NOW(3)), '.', '') - 86400000)) allcount;
    </select>
    <resultMap id="AppListRecordInfoMap" type="com.mioffice.ums.open.server.entity.bo.AppListRecordInfoBO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="app_name" property="appName"/>
        <result column="create_username" property="createUser.username"/>
        <result column="create_name" property="createUser.name"/>
        <result column="app_sys_status" property="appSysStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="topic_status" property="topicStatus"/>
        <collection column="id" ofType="com.mioffice.ums.open.server.entity.bo.UsernameAndNameBO" property="manager"
                    select="selectManager">
            <result column="username" jdbcType="VARCHAR" property="username"/>
            <result column="name" jdbcType="VARCHAR" property="name"/>
        </collection>
        <collection column="id" ofType="int" property="channel" select="selectChannel">
        </collection>
        <collection column="app_id" ofType="long" property="pushCount" select="selectPushCount">
        </collection>
    </resultMap>
    <select id="selectAppListPage" resultMap="AppListRecordInfoMap">
        select
        asi.id as id,
        asi.app_id as app_id,
        asi.app_name as app_name,
        asi.create_username as create_username,
        asi.create_name as create_name,
        asi.app_sys_status as app_sys_status,
        asi.create_time as create_time,
        coalesce(ati.status,0) as topic_status
        from
        app_sys_info asi
        join user_app_sys_info uasi
        on
        asi.id = uasi.app_sys_id
        join app_sys_bot_info asbi
        on asi.id = asbi.app_sys_id
        left join app_topic_info ati
        on asi.app_id = ati.app_id
        <where>
            1=1
            <if test="loginUsername != null and loginUsername != ''">
                and
                (asi.create_username = #{loginUsername,jdbcType=VARCHAR} or (uasi.username =
                #{loginUsername,jdbcType=VARCHAR} and uasi.user_type = 1))
            </if>
            <if test="appName != null and appName != ''">
                <bind name="appNameLike" value=" '%' + appName+ '%' "/>
                and
                asi.app_name like #{appNameLike}
            </if>
            <if test="managerUsernameList != null and managerUsernameList.size() != 0">
                and
                uasi.username
                in
                <foreach close=")" collection="managerUsernameList" index="index" item="managerUsername" open="("
                         separator=",">
                    #{managerUsername,jdbcType=VARCHAR}
                </foreach>
                and
                uasi.user_type = 1
            </if>
            <if test="applyUsernameList != null and applyUsernameList.size() != 0">
                and
                asi.create_username
                in
                <foreach close=")" collection="applyUsernameList" index="index" item="username" open="(" separator=",">
                    #{username,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="channelList != null and channelList.size() != 0">
                and
                asbi.channel
                in
                <foreach close=")" collection="channelList" index="index" item="channel" open="(" separator=",">
                    #{channel,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="appSysStatusList != null and appSysStatusList.size() != 0">
                and
                asi.app_sys_status
                in
                <foreach close=")" collection="appSysStatusList" index="index" item="appSysStatus" open="("
                         separator=",">
                    #{appSysStatus,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="beginTime != 0">
                and
                asi.create_time &gt; #{beginTime}
            </if>
            <if test="endTime != 0">
                and
                asi.create_time &lt; #{endTime}
            </if>
        </where>
        group by asi.id
        order by asi.id desc
    </select>


    <select id="selectApprovalManager" parameterType="Long"
            resultType="com.mioffice.ums.open.server.entity.bo.UsernameAndNameBO">
        select username,
               name
        from user_app_sys_info
        where app_sys_id = #{app_sys_id}
          and user_type = 1;
    </select>
    <select id="selectApprovalCreateUser" parameterType="Long"
            resultType="com.mioffice.ums.open.server.entity.bo.UsernameAndNameBO">
        select create_username as username,
               create_name     as name
        from app_sys_info
        where id = #{app_sys_id}
    </select>
    <select id="selectApprovalChannel" parameterType="Long" resultType="Integer">
        select distinct channel
        from app_sys_bot_info
        where app_sys_id = #{app_sys_id};
    </select>
    <select id="selectIsOutSend" parameterType="Long" resultType="Integer">
        select distinct is_out_send
        from app_sys_bot_info
        where app_sys_id = #{app_sys_id};
    </select>
    <resultMap id="AppApprovalListRecordInfoMap"
               type="com.mioffice.ums.open.server.entity.bo.AppApprovalListRecordInfoBO">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="app_name" property="appName"/>
        <result column="create_time" property="createTime"/>
        <collection column="app_sys_id" ofType="com.mioffice.ums.open.server.entity.bo.UsernameAndNameBO"
                    property="manager" select="selectApprovalManager">
            <result column="username" jdbcType="VARCHAR" property="username"/>
            <result column="name" jdbcType="VARCHAR" property="name"/>
        </collection>
        <collection column="app_sys_id" ofType="com.mioffice.ums.open.server.entity.bo.UsernameAndNameBO"
                    property="createUser" select="selectApprovalCreateUser">
            <result column="username" jdbcType="VARCHAR" property="username"/>
            <result column="name" jdbcType="VARCHAR" property="name"/>
        </collection>
        <collection column="app_sys_id" ofType="Integer" property="channel" select="selectApprovalChannel">
        </collection>
        <collection column="app_sys_id" ofType="Integer" property="isOutSend" select="selectIsOutSend">
        </collection>
    </resultMap>
    <select id="selectAppApprovalListPage" resultMap="AppApprovalListRecordInfoMap">
        select
        asari.app_sys_id as app_sys_id,
        asari.id as asari_id,
        asi.id as id,
        asi.app_id as app_id,
        asi.app_name as app_name,
        asari.create_time as create_time
        from
        app_sys_apply_record_info asari
        join app_sys_info asi on asi.id = asari.app_sys_id
        join app_sys_bot_info asbi on asari.app_sys_id = asbi.app_sys_id
        join user_app_sys_info uasi on asari.app_sys_id = uasi.app_sys_id
        <where>
            asari.valid = 2
            and asari.apply_status = 1
            <if test="appName != null and appName != ''">
                <bind name="appNameLike" value=" '%' + appName+ '%' "/>
                and
                asi.app_name like #{appNameLike}
            </if>
            <if test="managerUsernameList != null and managerUsernameList.size() != 0">
                and
                uasi.username
                in
                <foreach close=")" collection="managerUsernameList" index="index" item="managerUsername" open="("
                         separator=",">
                    #{managerUsername,jdbcType=VARCHAR}
                </foreach>
                and
                uasi.user_type = 1
            </if>
            <if test="channelList != null and channelList.size() != 0">
                and
                asbi.channel
                in
                <foreach close=")" collection="channelList" index="index" item="channel" open="(" separator=",">
                    #{channel,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="beginTime != 0">
                and
                asari.create_time &gt; #{beginTime}
            </if>
            <if test="endTime != 0">
                and
                asari.create_time &lt; #{endTime}
            </if>
        </where>
        group by asari.id
        order by asari.id desc
    </select>
</mapper>
