<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppMsgRuleInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppMsgRuleInfo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="app_sys_id" jdbcType="BIGINT" property="appSysId" />
        <result column="callback" jdbcType="VARCHAR" property="callback" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime" />
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="placeholder" jdbcType="VARCHAR" property="placeholder"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        rule_name,
        app_sys_id,
        callback,
        create_time,
        create_username,
        update_time,
        update_username,
        placeholder
    </sql>

</mapper>