<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.open.server.mapper.AppSysTemplateInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo">
    <!--@mbg.generated-->
    <!--@Table app_sys_template_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="app_sys_id" jdbcType="BIGINT" property="appSysId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="bot_app_id" jdbcType="VARCHAR" property="botAppId" />
    <result column="template_content" jdbcType="LONGVARCHAR" property="templateContent" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="update_username" jdbcType="VARCHAR" property="updateUsername" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="template_channel" jdbcType="TINYINT" property="templateChannel" />
    <result column="bot_biz_id" jdbcType="VARCHAR" property="botBizId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, biz_id, app_sys_id, app_id, template_name, bot_app_id, template_content, template_id, 
    create_time, update_time, update_username, create_username, update_name, create_name, 
    template_channel, bot_biz_id
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_sys_template_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.bizId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="app_sys_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appSysId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="template_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.templateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bot_app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.botAppId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="template_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.templateContent,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="template_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="template_channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.templateChannel,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="bot_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update app_sys_template_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bizId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.bizId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_sys_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appSysId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appSysId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="template_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.templateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="bot_app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.botAppId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.botAppId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="template_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateContent != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.templateContent,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="template_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateUsername != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createUsername != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="template_channel = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateChannel != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.templateChannel,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="bot_biz_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.botBizId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.botBizId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_template_info
    (biz_id, app_sys_id, app_id, template_name, bot_app_id, template_content, template_id, 
      create_time, update_time, update_username, create_username, update_name, create_name, 
      template_channel, bot_biz_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizId,jdbcType=VARCHAR}, #{item.appSysId,jdbcType=BIGINT}, #{item.appId,jdbcType=VARCHAR}, 
        #{item.templateName,jdbcType=VARCHAR}, #{item.botAppId,jdbcType=VARCHAR}, #{item.templateContent,jdbcType=LONGVARCHAR}, 
        #{item.templateId,jdbcType=BIGINT}, #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, 
        #{item.updateUsername,jdbcType=VARCHAR}, #{item.createUsername,jdbcType=VARCHAR}, 
        #{item.updateName,jdbcType=VARCHAR}, #{item.createName,jdbcType=VARCHAR}, #{item.templateChannel,jdbcType=TINYINT}, 
        #{item.botBizId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_template_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      biz_id,
      app_sys_id,
      app_id,
      template_name,
      bot_app_id,
      template_content,
      template_id,
      create_time,
      update_time,
      update_username,
      create_username,
      update_name,
      create_name,
      template_channel,
      bot_biz_id,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{bizId,jdbcType=VARCHAR},
      #{appSysId,jdbcType=BIGINT},
      #{appId,jdbcType=VARCHAR},
      #{templateName,jdbcType=VARCHAR},
      #{botAppId,jdbcType=VARCHAR},
      #{templateContent,jdbcType=LONGVARCHAR},
      #{templateId,jdbcType=BIGINT},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
      #{updateUsername,jdbcType=VARCHAR},
      #{createUsername,jdbcType=VARCHAR},
      #{updateName,jdbcType=VARCHAR},
      #{createName,jdbcType=VARCHAR},
      #{templateChannel,jdbcType=TINYINT},
      #{botBizId,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      biz_id = #{bizId,jdbcType=VARCHAR},
      app_sys_id = #{appSysId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=VARCHAR},
      template_name = #{templateName,jdbcType=VARCHAR},
      bot_app_id = #{botAppId,jdbcType=VARCHAR},
      template_content = #{templateContent,jdbcType=LONGVARCHAR},
      template_id = #{templateId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      update_username = #{updateUsername,jdbcType=VARCHAR},
      create_username = #{createUsername,jdbcType=VARCHAR},
      update_name = #{updateName,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      template_channel = #{templateChannel,jdbcType=TINYINT},
      bot_biz_id = #{botBizId,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_sys_template_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="appSysId != null">
        app_sys_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="botAppId != null">
        bot_app_id,
      </if>
      <if test="templateContent != null">
        template_content,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUsername != null">
        update_username,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="templateChannel != null">
        template_channel,
      </if>
      <if test="botBizId != null">
        bot_biz_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="appSysId != null">
        #{appSysId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="botAppId != null">
        #{botAppId,jdbcType=VARCHAR},
      </if>
      <if test="templateContent != null">
        #{templateContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUsername != null">
        #{updateUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="templateChannel != null">
        #{templateChannel,jdbcType=TINYINT},
      </if>
      <if test="botBizId != null">
        #{botBizId,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="appSysId != null">
        app_sys_id = #{appSysId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="botAppId != null">
        bot_app_id = #{botAppId,jdbcType=VARCHAR},
      </if>
      <if test="templateContent != null">
        template_content = #{templateContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updateUsername != null">
        update_username = #{updateUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="templateChannel != null">
        template_channel = #{templateChannel,jdbcType=TINYINT},
      </if>
      <if test="botBizId != null">
        bot_biz_id = #{botBizId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>