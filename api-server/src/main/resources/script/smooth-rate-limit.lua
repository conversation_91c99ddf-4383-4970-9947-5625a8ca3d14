local limit_root = KEYS[1];
local limit_key = KEYS[2];
local limit_conf = KEYS[3];
local limit_ms_conf = KEYS[4];
local rate = redis.call('HGET', limit_root, limit_conf);
local timestamp_ms = ARGV[1];
local interval_ms = ARGV[2];
local timeout_ms = ARGV[3];
local rate_ms = redis.call('HGET', limit_root, limit_ms_conf);

if(rate_ms)
then
    if(tonumber(rate_ms) > 0) then
        rate_ms = tonumber(rate_ms)
    end
else
    rate_ms = 1000
end

if(rate)
then
    if(tonumber(rate) > 0)
    then
        interval_ms = rate_ms / tonumber(rate)
    end
end

local val = redis.call('HGET', limit_root, limit_key);
local wait_ms = 0;
if(val)
then
    val = tonumber(val)
    local diff = tonumber(timestamp_ms) - tonumber(interval_ms);
    if(diff < val) then
        wait_ms = val - diff
        timestamp_ms = val + tonumber(interval_ms)
    end
end
if(tonumber(timeout_ms) <= 0) then
    redis.call('HSET', limit_root, limit_key, timestamp_ms);
else
    if(wait_ms < tonumber(timeout_ms)) then
        redis.call('HSET', limit_root, limit_key, timestamp_ms);
    end
end
return wait_ms;