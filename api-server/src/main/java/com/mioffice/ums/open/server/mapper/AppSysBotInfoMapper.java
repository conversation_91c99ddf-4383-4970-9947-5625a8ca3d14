package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.open.server.entity.info.AppSysBotInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.09.16
 */
public interface AppSysBotInfoMapper extends BaseMapper<AppSysBotInfo> {
    int updateBatch(List<AppSysBotInfo> list);

    int updateBatchSelective(List<AppSysBotInfo> list);

    int batchInsert(@Param("list") List<AppSysBotInfo> list);

    int insertOrUpdate(AppSysBotInfo record);

    int insertOrUpdateSelective(AppSysBotInfo record);
}
