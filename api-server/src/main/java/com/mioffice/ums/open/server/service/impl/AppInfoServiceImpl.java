package com.mioffice.ums.open.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.open.common.constats.AppSysStatus;
import com.mioffice.ums.open.common.constats.RedisConst;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.exception.BizException;
import com.mioffice.ums.open.common.util.HmacUtil;
import com.mioffice.ums.open.common.util.JsonUtils;
import com.mioffice.ums.open.server.entity.bo.AppInfoBo;
import com.mioffice.ums.open.server.entity.bo.AppInfoStatusBo;
import com.mioffice.ums.open.server.entity.bo.AppManagerBo;
import com.mioffice.ums.open.server.entity.bo.DeptBo;
import com.mioffice.ums.open.server.entity.bo.MessageChannelBo;
import com.mioffice.ums.open.server.entity.info.AppSysBotInfo;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.entity.info.DeptAppSysInfo;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.mioffice.ums.open.server.manager.AppSysApplyManager;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.service.AppInfoService;
import com.mioffice.ums.open.server.service.AppSysBotInfoService;
import com.mioffice.ums.open.server.service.DeptAppSysInfoService;
import com.mioffice.ums.open.server.service.UserAppSysInfoService;
import com.mioffice.ums.open.server.utils.MapperUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Slf4j
@Service
@CacheConfig(cacheNames = "app_info")
public class AppInfoServiceImpl extends BaseServiceImpl<AppSysInfoMapper, AppSysInfo> implements AppInfoService {
    @Autowired
    AppSysBotInfoService appSysBotInfoService;
    @Autowired
    DeptAppSysInfoService deptAppSysInfoService;
    @Autowired
    UserAppSysInfoService userAppSysInfoService;
    @Autowired
    RedissonClient redissonClient;
    @Autowired
    AppSysApplyManager appSysApplyManager;

    @Override
    @Cacheable(key = "#appId")
    public String getSecret(@NonNull String appId) {
        AppSysInfo info = getOne(Wrappers.<AppSysInfo>lambdaQuery().eq(AppSysInfo::getAppId, appId)
                .eq(AppSysInfo::getAppSysStatus, AppSysStatus.PASS.getCode()), false);
        if (info == null) {
            return null;
        }
        return info.getAppSecret();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "#appInfoBo.appId != null ? #appInfoBo.appId : ''")
    public AppInfoStatusBo saveOrUpdate(AppInfoBo appInfoBo, List<String> roleList) {

        checkPermission(appInfoBo, roleList);

        // appSysInfo
        AppSysInfo appSysInfo = handleAppSysInfo(appInfoBo);
        // bots
        handlerBots(appSysInfo, appInfoBo.getChannels());
        // dept
        handlerDept(appInfoBo, appSysInfo);
        // 负责人
        handlerManager(appInfoBo, appSysInfo);

        log.info("appSysInfo = [{}]", JsonUtils.toJson(appSysInfo));
        if (!AppSysStatus.PASS.getCode().equals(appSysInfo.getAppSysStatus())) {
            appSysApplyManager.createAppSysApplyRecordInfo(appInfoBo.getOperatorName(), appInfoBo.getOperatorUsername(),
                    appSysInfo.getId());
        }

        AppInfoStatusBo appInfoStatusBo = new AppInfoStatusBo();
        appInfoStatusBo.setId(appSysInfo.getId());
        appInfoStatusBo.setStatus(appSysInfo.getAppSysStatus() == null ? 0 : (int) appSysInfo.getAppSysStatus());

        return appInfoStatusBo;
    }

    private void checkPermission(AppInfoBo appInfoBo, List<String> roleList) {
        if (appInfoBo.getId() > 0) {
            // 修改操作
            if (!(roleList.contains("ROLE_SYS_SUPER_ADMIN") || roleList.contains("ROLE_SYS_ADMIN") ||
                    roleList.contains("ROLE_OPERATOR") ||
                    userAppSysInfoService.<UserAppSysInfo>lambdaQuery().
                            eq(UserAppSysInfo::getAppSysId, appInfoBo.getId())
                            .in(UserAppSysInfo::getUsername,
                                    appInfoBo.getOperatorUsername()).count() > 0
            )) {
                // 无权操作
                throw new RuntimeException("无权操作");
            }
        }
    }

    @Override
    public AppInfoBo queryById(Long id, String userName, List<String> roleList) {

        if (!(roleList.contains("ROLE_SYS_SUPER_ADMIN") || roleList.contains("ROLE_SYS_ADMIN") ||
                roleList.contains("ROLE_OPERATOR") ||
                userAppSysInfoService.<UserAppSysInfo>lambdaQuery()
                        .eq(UserAppSysInfo::getAppSysId, id)
                        .in(UserAppSysInfo::getUsername, userName).count() > 0
        )) {
            // 无权操作
            throw new BizException(ResponseCode.APP_NO_AUTH);
        }

        AppSysInfo info = getById(id);
        if (info == null) {
            return null;
        }

        AppInfoBo appInfoBo = new AppInfoBo();
        appInfoBo.setAppName(info.getAppName());
        appInfoBo.setId(id);
        appInfoBo.setQps(info.getQps());
        appInfoBo.setAppId(info.getAppId());
        appInfoBo.setAppSecret(info.getAppSecret());
        appInfoBo.setAppSysStatus(info.getAppSysStatus());
        appInfoBo.setCreateTime(info.getCreateTime());
        appInfoBo.setAlarmStatus(info.getAlarmStatus());
        appInfoBo.setAlarmThreshold(info.getAlarmThreshold());
        appInfoBo.setDeduplicateStatus(info.getDeduplicateStatus());
        appInfoBo.setWhiteStatus(info.getWhiteStatus());
        List<UserAppSysInfo> list = userAppSysInfoService.lambdaQuery().eq(UserAppSysInfo::getAppSysId, id).list();
        appInfoBo.setAppManagers(MapperUtil.INSTANCE.mapToAppManagerBo(list));

        Map<Byte, MessageChannelBo> map = new HashMap<>();
        List<AppSysBotInfo> botList = appSysBotInfoService.lambdaQuery()
                .eq(AppSysBotInfo::getAppSysId, id)
                .list();
        botList.forEach(appSysBotInfo -> {
            MessageChannelBo bo = map.computeIfAbsent(appSysBotInfo.getChannel(), k -> {
                MessageChannelBo messageChannelBo = new MessageChannelBo();
                messageChannelBo.setBotAppIds(new LinkedList<>());
                messageChannelBo.setChannel(k);
                messageChannelBo.setIsOutSend(appSysBotInfo.getIsOutSend());
                return messageChannelBo;
            });
            bo.getBotAppIds()
                    .add(new MessageChannelBo.BotInfoDesc(appSysBotInfo.getBotAppId(), appSysBotInfo.getBotBizId()));
        });
        appInfoBo.setChannels(new ArrayList<>(map.values()));

        List<DeptAppSysInfo> deptAppSysInfos =
                deptAppSysInfoService.lambdaQuery().eq(DeptAppSysInfo::getAppSysId, id).list();
        appInfoBo.setDepts(MapperUtil.INSTANCE.mapToDeptBo(deptAppSysInfos));

        return appInfoBo;
    }

    /**
     * handleAppSysInfo
     *
     * @param appInfoBo
     * @return
     */
    private AppSysInfo handleAppSysInfo(AppInfoBo appInfoBo) {
        AppSysInfo appSysInfo = null;
        if (appInfoBo.getId() > 0) {
            appSysInfo = getById(appInfoBo.getId());
            if (appSysInfo == null) {
                throw new BizException(ResponseCode.PARAM_ERROR);
            }
            appSysInfo.setUpdateName(appInfoBo.getOperatorName());
            appSysInfo.setUpdateUsername(appInfoBo.getOperatorUsername());
            // 如果原状态是通过状态就不走审核
            if (!AppSysStatus.PASS.getCode().equals(appSysInfo.getAppSysStatus())) {
                appSysInfo.setAppSysStatus(AppSysStatus.REVIEW.getCode());
            }
        } else {
            appSysInfo = new AppSysInfo();
            RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConst.APP_ID_COUNT_KEY);
            long incr = atomicLong.incrementAndGet();
            String appId = String.format("S%05d", incr);
            String appSecret = HmacUtil.getHmacSha256(appId, "appSecret");
            appSysInfo.setAppId(appId);
            appSysInfo.setAppSecret(appSecret);
            appSysInfo.setCreateUsername(appInfoBo.getOperatorUsername());
            appSysInfo.setCreateName(appInfoBo.getOperatorName());
            appSysInfo.setAppSysStatus(AppSysStatus.REVIEW.getCode());
        }
        appSysInfo.setQps(appInfoBo.getQps());
        appSysInfo.setAppName(appInfoBo.getAppName());
        appSysInfo.setAlarmStatus((byte) appInfoBo.getAlarmStatus());
        // 最低两次
        if (appInfoBo.getAlarmThreshold() < 2) {
            appSysInfo.setAlarmThreshold(2);
        } else {
            appSysInfo.setAlarmThreshold(appInfoBo.getAlarmThreshold());
        }
        appSysInfo.setDeduplicateStatus((byte) appInfoBo.getDeduplicateStatus());

        saveOrUpdate(appSysInfo);
        return appSysInfo;
    }

    /**
     * manager
     *
     * @param appInfoBo
     * @param appSysInfo
     */
    private void handlerManager(AppInfoBo appInfoBo, AppSysInfo appSysInfo) {
        List<AppManagerBo> appManagers = appInfoBo.getAppManagers();
        Map<String, AppManagerBo> newMap = new HashMap<>(appManagers.size());
        List<UserAppSysInfo> saveOrUpdateList = new LinkedList<>();

        appManagers.forEach(appManagerBo -> newMap.put(appManagerBo.getUsername(), appManagerBo));
        // remove
        userAppSysInfoService.remove(new LambdaQueryWrapper<UserAppSysInfo>()
                .eq(UserAppSysInfo::getAppSysId, appSysInfo.getId())
                .eq(UserAppSysInfo::getAppId, appSysInfo.getAppId())
                .notIn(UserAppSysInfo::getUsername, newMap.keySet()));

        List<UserAppSysInfo> updateList = userAppSysInfoService.lambdaQuery()
                .eq(UserAppSysInfo::getAppSysId, appSysInfo.getId())
                .eq(UserAppSysInfo::getAppId, appSysInfo.getAppId())
                .in(UserAppSysInfo::getUsername, newMap.keySet()).list();

        for (UserAppSysInfo userAppSysInfo : updateList) {
            AppManagerBo remove = newMap.remove(userAppSysInfo.getUsername());
            if (remove != null) {
                userAppSysInfo.setName(remove.getName());
                saveOrUpdateList.add(userAppSysInfo);
            }
        }
        for (Map.Entry<String, AppManagerBo> entry : newMap.entrySet()) {
            AppManagerBo value = entry.getValue();
            UserAppSysInfo info = new UserAppSysInfo();
            info.setName(value.getName());
            info.setUsername(value.getUsername());
            info.setAppId(appSysInfo.getAppId());
            info.setAppSysId(appSysInfo.getId());
            // 默认为负责人
            info.setUserType((byte) 1);
            info.setCreateTime(System.currentTimeMillis());
            info.setUpdateTime(System.currentTimeMillis());
            saveOrUpdateList.add(info);
        }
        userAppSysInfoService.saveOrUpdateBatch(saveOrUpdateList);
    }

    // dept
    private void handlerDept(AppInfoBo appInfoBo, AppSysInfo appSysInfo) {
        List<DeptBo> depts = appInfoBo.getDepts();
        List<DeptAppSysInfo> saveOrUpdateList = new LinkedList<>();

        Map<String, DeptBo> newMap = new HashMap<>(depts.size());
        depts.forEach(deptBo -> newMap.put(deptBo.getDeptId(), deptBo));
        // remove
        deptAppSysInfoService.remove(new LambdaQueryWrapper<DeptAppSysInfo>()
                .eq(DeptAppSysInfo::getAppId, appSysInfo.getAppId())
                .notIn(DeptAppSysInfo::getDeptId, newMap.keySet()));

        // update
        List<DeptAppSysInfo> updateList = deptAppSysInfoService.lambdaQuery()
                .eq(DeptAppSysInfo::getAppId, appSysInfo.getAppId())
                .in(DeptAppSysInfo::getDeptId, newMap.keySet())
                .list();

        for (DeptAppSysInfo deptAppSysInfo : updateList) {
            DeptBo deptBo = newMap.remove(deptAppSysInfo.getDeptId());
            if (deptBo != null) {
                // 只允许修改部门名称 其他信息不允许修改 可以删除
                deptAppSysInfo.setDeptName(deptBo.getDeptName());
                saveOrUpdateList.add(deptAppSysInfo);
            }
        }
        for (Map.Entry<String, DeptBo> entry : newMap.entrySet()) {
            DeptBo value = entry.getValue();
            DeptAppSysInfo deptAppSysInfo = new DeptAppSysInfo();
            deptAppSysInfo.setAppSysId(appSysInfo.getId());
            deptAppSysInfo.setAppId(appSysInfo.getAppId());
            deptAppSysInfo.setDeptId(value.getDeptId());
            deptAppSysInfo.setDeptName(value.getDeptName());
            deptAppSysInfo.setCreateTime(System.currentTimeMillis());
            deptAppSysInfo.setUpdateTime(System.currentTimeMillis());
            saveOrUpdateList.add(deptAppSysInfo);
        }
        deptAppSysInfoService.saveOrUpdateBatch(saveOrUpdateList);
    }

    private void addAppSysBotInfo(AppSysInfo appSysInfo, List<AppSysBotInfo> appSysBotInfos, MessageChannelBo channel,
                                  MessageChannelBo.BotInfoDesc botInfoDesc) {
        AppSysBotInfo appSysBotInfo = new AppSysBotInfo();
        appSysBotInfo.setAppId(appSysInfo.getAppId());
        appSysBotInfo.setIsOutSend((byte) channel.getIsOutSend());
        appSysBotInfo.setAppSysId(appSysInfo.getId());
        appSysBotInfo.setBotAppId(botInfoDesc.getBotAppId());
        appSysBotInfo.setBotBizId(botInfoDesc.getBotBizId());
        appSysBotInfo.setChannel((byte) channel.getChannel());
        appSysBotInfo.setCreateTime(System.currentTimeMillis());
        appSysBotInfo.setUpdateTime(System.currentTimeMillis());
        appSysBotInfos.add(appSysBotInfo);
    }

    /**
     * bots
     *
     * @param appSysInfo
     * @param channels
     */
    private void handlerBots(AppSysInfo appSysInfo, List<MessageChannelBo> channels) {
        List<AppSysBotInfo> appSysBotInfos = new LinkedList<>();
        Map<String, MessageChannelBo.BotInfoPair> botAppMap = new HashMap<>();

        for (MessageChannelBo channel : channels) {
            List<MessageChannelBo.BotInfoDesc> botInfoDescs = channel.getBotAppIds();
            botInfoDescs.forEach(botInfoDesc -> botAppMap.put(botInfoDesc.getBotAppId(),
                    new MessageChannelBo.BotInfoPair(botInfoDesc, channel)));
        }

        // 清理
        appSysBotInfoService.remove(new LambdaQueryWrapper<AppSysBotInfo>()
                .eq(AppSysBotInfo::getAppSysId, appSysInfo.getId())
                .notIn(AppSysBotInfo::getBotAppId, botAppMap.keySet()));
        // 查找待更新
        List<AppSysBotInfo> updateList = appSysBotInfoService.lambdaQuery()
                .eq(AppSysBotInfo::getAppSysId, appSysInfo.getId())
                .in(AppSysBotInfo::getBotAppId, botAppMap.keySet())
                .list();

        for (AppSysBotInfo appSysBotInfo : updateList) {
            MessageChannelBo.BotInfoPair pair = botAppMap.remove(appSysBotInfo.getBotAppId());
            appSysBotInfo.setIsOutSend((byte) pair.getChannelBo().getIsOutSend());
            appSysBotInfos.add(appSysBotInfo);
        }

        for (Map.Entry<String, MessageChannelBo.BotInfoPair> entry : botAppMap.entrySet()) {
            MessageChannelBo.BotInfoPair pair = entry.getValue();
            addAppSysBotInfo(appSysInfo, appSysBotInfos, pair.getChannelBo(), pair);
        }

        appSysBotInfoService.saveOrUpdateBatch(appSysBotInfos);
    }

    @Override
    public void updateStatus(Long id, int status) {
        AppSysInfo info = getById(id);
        if (info == null) {
            return;
        }
        // 清除缓存
        info.setAppSysStatus((byte) status);
        updateById(info);
        redissonClient.getBucket(String.format("ums:open:server:app_info::%s", info.getAppId())).delete();
    }

    @Override
    public boolean updateWhiteStatus(Long id, int whiteStatus) {
        AppSysInfo info = getById(id);
        if (info == null) {
            return false;
        }
        // 清除缓存
        info.setWhiteStatus((byte) whiteStatus);
        updateById(info);
        //  redissonClient.getBucket(String.format("ums:open:server:app_info::%s", info.getAppId())).delete();
        return true;
    }

    @Override
    public AppInfoBo queryByAppId(String appId) {
        List<AppSysInfo> appList = list(new LambdaQueryWrapper<AppSysInfo>()
                .eq(AppSysInfo::getAppId, appId));
        if (CollectionUtils.isEmpty(appList)) {
            return null;
        }
        AppSysInfo info = appList.get(0);
        Long id = info.getId();
        AppInfoBo appInfoBo = new AppInfoBo();
        appInfoBo.setAppName(info.getAppName());
        appInfoBo.setId(id);
        appInfoBo.setQps(info.getQps());
        appInfoBo.setAppId(info.getAppId());
        appInfoBo.setAppSecret(info.getAppSecret());
        appInfoBo.setAppSysStatus(info.getAppSysStatus());
        appInfoBo.setCreateTime(info.getCreateTime());
        appInfoBo.setAlarmStatus(info.getAlarmStatus());
        appInfoBo.setAlarmThreshold(info.getAlarmThreshold());
        appInfoBo.setDeduplicateStatus(info.getDeduplicateStatus());
        appInfoBo.setWhiteStatus(info.getWhiteStatus());
        List<UserAppSysInfo> list = userAppSysInfoService.lambdaQuery().eq(UserAppSysInfo::getAppSysId, id).list();
        appInfoBo.setAppManagers(MapperUtil.INSTANCE.mapToAppManagerBo(list));

        Map<Byte, MessageChannelBo> map = new HashMap<>();
        List<AppSysBotInfo> botList = appSysBotInfoService.lambdaQuery()
                .eq(AppSysBotInfo::getAppSysId, id)
                .list();
        botList.forEach(appSysBotInfo -> {
            MessageChannelBo bo = map.computeIfAbsent(appSysBotInfo.getChannel(), k -> {
                MessageChannelBo messageChannelBo = new MessageChannelBo();
                messageChannelBo.setBotAppIds(new LinkedList<>());
                messageChannelBo.setChannel(k);
                messageChannelBo.setIsOutSend(appSysBotInfo.getIsOutSend());
                return messageChannelBo;
            });
            bo.getBotAppIds()
                    .add(new MessageChannelBo.BotInfoDesc(appSysBotInfo.getBotAppId(), appSysBotInfo.getBotBizId()));
        });
        appInfoBo.setChannels(new ArrayList<>(map.values()));

        List<DeptAppSysInfo> deptAppSysInfos =
                deptAppSysInfoService.lambdaQuery().eq(DeptAppSysInfo::getAppSysId, id).list();
        appInfoBo.setDepts(MapperUtil.INSTANCE.mapToDeptBo(deptAppSysInfos));

        return appInfoBo;
    }

}
