package com.mioffice.ums.open.server.rpc;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.server.bo.AppTaskLogBo;
import com.mioffice.ums.open.server.bo.PushSummaryBo;
import com.mioffice.ums.open.server.bo.UserRoleBo;
import com.mioffice.ums.open.server.entity.bo.AppChannelCountBo;
import com.mioffice.ums.open.server.entity.bo.AppMessageCountBo;
import com.mioffice.ums.open.server.service.PushSummaryService;
import com.mioffice.ums.open.server.service.UserRoleService;
import com.mioffice.ums.open.server.utils.MapperUtil;
import com.xiaomi.info.grpc.server.annotation.RpcServer;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.*;
import io.grpc.stub.StreamObserver;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * grpc 推送概览
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/23 3:14 下午
 */
@RpcServer
public class PushSummaryGrpcService extends AppPushSummaryServiceGrpc.AppPushSummaryServiceImplBase {

    @Autowired
    private PushSummaryService pushSummaryService;
    @Autowired
    private UserRoleService userRoleService;

    @Override
    public void getAppPushSummary(AppPushSummaryRequest request, StreamObserver<AppPushSummaryResponse> responseObserver) {
        List<Long> systemIdList = request.getSystemIdList();
        String startTime  = request.getStartTime();
        String endTime    = request.getEndTime();
        PushSummaryBo pushSummaryBo = pushSummaryService.getUseSummary(systemIdList, startTime, endTime);
        // build appPushSummary
        AppPushSummaryInfo.Builder appPushSummaryInfoBuilder = AppPushSummaryInfo.newBuilder();
        // setXAix
        appPushSummaryInfoBuilder.addAllXAxis(pushSummaryBo.getXAxis());
        // setYAix
        PushDetail.Builder pushDetailBuilder = PushDetail.newBuilder();
        pushDetailBuilder.addAllEmail(pushSummaryBo.getYAxis().getEmail());
        pushDetailBuilder.addAllMiWork(pushSummaryBo.getYAxis().getMiWork());
        pushDetailBuilder.addAllSms(pushSummaryBo.getYAxis().getSms());
        appPushSummaryInfoBuilder.setYAxis(pushDetailBuilder);
        // build response
        AppPushSummaryResponse.Builder appPushLogResponseBuilder = AppPushSummaryResponse.newBuilder();
        appPushLogResponseBuilder.setCode(ResponseCode.SUCCESS.getCode());
        appPushLogResponseBuilder.setDesc(ResponseCode.SUCCESS.getMsg());
        appPushLogResponseBuilder.setAppPushSummaryInfo(appPushSummaryInfoBuilder);
        // response
        responseObserver.onNext(appPushLogResponseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAppPushLog(AppPushLogRequest request, StreamObserver<AppPushLogResponse> responseObserver) {
        Integer page = request.getPage();
        Integer size = request.getSize();
        IPage<AppTaskLogBo> appTaskLogBoPage = pushSummaryService.getAppTaskLog(request.getExtraId(), request.getSystemId(), getChannelList(request.getChannels()), page, size);
        // build page
        PushLogPage.Builder pushLogPageBuilder = PushLogPage.newBuilder();
        pushLogPageBuilder.setCurrent(appTaskLogBoPage.getCurrent());
        pushLogPageBuilder.setPages(appTaskLogBoPage.getPages());
        pushLogPageBuilder.setTotal(appTaskLogBoPage.getTotal());
        pushLogPageBuilder.setSize(appTaskLogBoPage.getSize());
        pushLogPageBuilder.addAllRecords(appTaskLogBoPage.getRecords().stream().map(MapperUtil.INSTANCE::mapToPushLog).collect(Collectors.toList()));
        // build response
        AppPushLogResponse.Builder appPushResponseBuilder = AppPushLogResponse.newBuilder();
        appPushResponseBuilder.setCode(ResponseCode.SUCCESS.getCode());
        appPushResponseBuilder.setDesc(ResponseCode.SUCCESS.getMsg());
        appPushResponseBuilder.setPushLogPage(pushLogPageBuilder);
        // response
        responseObserver.onNext(appPushResponseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getUserRole(AppUserRoleRequest request, StreamObserver<AppUserRoleResponse> responseObserver) {
        String username = request.getUsername();
        Long systemId = request.getSystemId();
        UserRoleBo userRoleBo = userRoleService.getUserRole(username, systemId);
        // map UserRoleBo to AppUserRole
        AppUserRole appUserRole = MapperUtil.INSTANCE.mapToAppUserRole(userRoleBo);
        // build response
        AppUserRoleResponse.Builder appUserRoleResponseBuilder = AppUserRoleResponse.newBuilder();
        appUserRoleResponseBuilder.setCode(ResponseCode.SUCCESS.getCode());
        appUserRoleResponseBuilder.setDesc(ResponseCode.SUCCESS.getMsg());
        appUserRoleResponseBuilder.setAppUserRole(appUserRole);
        // response
        responseObserver.onNext(appUserRoleResponseBuilder.build());
        responseObserver.onCompleted();
    }

    private List<Byte> getChannelList(String channels) {
        List<Byte> channelList = new ArrayList<>();
        if (StringUtils.isBlank(channels)) {
            return channelList;
        }
        String[] channelArray = channels.split(",");
        for (String channel : channelArray) {
            channelList.add(Byte.valueOf(channel));
        }
        return channelList;
    }

    @Override
    public void getTopAppMsgList(TopRequest request, StreamObserver<AppMessageCountResponse> responseObserver) {

        long beginTime = request.getBeginTime();
        long endTime = request.getEndTime();
        int topCount = request.getCount();

        List<AppMessageCountBo> list = pushSummaryService.getTopAppMsgList(topCount, beginTime, endTime);
        AppMessageCountResponse.Builder builder = AppMessageCountResponse.newBuilder()
                .addAllAppMsgCountInfo(MapperUtil.INSTANCE.mapToAppMsgCountInfoList(list))
                .setCode(ResponseCode.SUCCESS.getCode())
                .setMessage(ResponseCode.SUCCESS.getMsg());
        // response
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAppMsgChannelCount(AppChannelRequest request, StreamObserver<AppChannelCountResponse> responseObserver) {
        List<Long> appSysIdList = request.getAppSysIdList();
        long beginTime = request.getBeginTime();
        long endTime = request.getEndTime();

        List<AppChannelCountBo> appChannelCountBoList = pushSummaryService.getAppMsgChannelCount(appSysIdList, beginTime, endTime);
        List<AppChannelCount> appChannelCountList = MapperUtil.INSTANCE.mapToAppChannelCountList(appChannelCountBoList);
        AppChannelCountResponse.Builder builder = AppChannelCountResponse.newBuilder()
                .addAllAppChannelCount(appChannelCountList)
                .setCode(ResponseCode.SUCCESS.getCode())
                .setMessage(ResponseCode.SUCCESS.getMsg());

        // response
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void flushAppMonitor(AppMonitorRequest request, StreamObserver<AppMonitorResponse> responseObserver) {
        Long startTime = request.getStartTime();
        Long endTime   = request.getEndTime();
        pushSummaryService.syncAppPushRecordsWithStartEndTime(startTime, endTime);
        AppMonitorResponse.Builder builder = AppMonitorResponse.newBuilder()
                .setCode(ResponseCode.SUCCESS.getCode())
                .setMessage(ResponseCode.SUCCESS.getMsg());
        // response
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
