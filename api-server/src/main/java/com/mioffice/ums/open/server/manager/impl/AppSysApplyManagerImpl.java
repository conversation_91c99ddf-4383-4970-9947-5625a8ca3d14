package com.mioffice.ums.open.server.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo;
import com.mioffice.ums.open.server.enums.AppRecordValidStatusEnum;
import com.mioffice.ums.open.server.enums.AppSysStatusEnum;
import com.mioffice.ums.open.server.manager.AppSysApplyManager;
import com.mioffice.ums.open.server.mapper.AppSysApplyRecordInfoMapper;
import org.springframework.stereotype.Component;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/23 11:31 上午
 * version: 1.0.0
 */
@Component
public class AppSysApplyManagerImpl implements AppSysApplyManager {

    private final AppSysApplyRecordInfoMapper appSysApplyRecordInfoMapper;

    public AppSysApplyManagerImpl(AppSysApplyRecordInfoMapper appSysApplyRecordInfoMapper) {
        this.appSysApplyRecordInfoMapper = appSysApplyRecordInfoMapper;
    }

    @Override
    public void createAppSysApplyRecordInfo(String applyUserName, String applyUserUsername, Long appSysId) {
        AppSysApplyRecordInfo appSysApplyRecordInfo = AppSysApplyRecordInfo.newCreateAndUpdateTimeInstant();
        appSysApplyRecordInfo.setApplyUserName(applyUserName);
        appSysApplyRecordInfo.setApplyUserUsername(applyUserUsername);
        appSysApplyRecordInfo.setAppSysId(appSysId);
        appSysApplyRecordInfo.setApplyStatus(AppSysStatusEnum.APPROVING.getCode());
        appSysApplyRecordInfo.setValid(AppRecordValidStatusEnum.VALID_STATUS_ENUM.getCode());
        appSysApplyRecordInfoMapper.insert(appSysApplyRecordInfo);
    }

    @Override
    public void terminateAppSysApplication(String applyUserName, String applyUserUsername, Long appSysId) {
        AppSysApplyRecordInfo appSysApplyRecordInfo = AppSysApplyRecordInfo.newUpdateTimeInstant();
        appSysApplyRecordInfo.setValid(AppRecordValidStatusEnum.UN_VALID_STATUS_ENUM.getCode());
        appSysApplyRecordInfoMapper.update(
                appSysApplyRecordInfo,
                Wrappers.<AppSysApplyRecordInfo>lambdaQuery()
                        .eq(AppSysApplyRecordInfo::getAppSysId, appSysId)
                        .eq(AppSysApplyRecordInfo::getValid, AppRecordValidStatusEnum.VALID_STATUS_ENUM.getCode())
        );
    }
}
