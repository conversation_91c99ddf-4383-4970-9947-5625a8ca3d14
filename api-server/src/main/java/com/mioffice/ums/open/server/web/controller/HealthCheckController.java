package com.mioffice.ums.open.server.web.controller;

import com.xiaomi.info.infra.soa.SoaUtil;
import com.xiaomi.info.infra.soa.pojo.IpPort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * @since 2020/11/25 下午2:46
 * version: 1.0.0
 */
@RestController
public class HealthCheckController {
    @GetMapping("/health")
    public int health() {

        int resultCode = -1;
        List<IpPort> addressesNoCache = SoaUtil.getAddressesNoCache("ums_engine", "info-application", "ums_open_api");
        if (!addressesNoCache.isEmpty()) {
            resultCode = 0;
        }
        return resultCode;

    }

}
