package com.mioffice.ums.open.server.task;

import cn.hutool.core.thread.ThreadUtil;
import com.mioffice.ums.open.server.service.AppMsgRepeatInfoService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @since 2021.09.22
 */
@Slf4j
@PlanTask(name = "DeleteExpiredRepeatInfoTask", quartzCron = "0 0 2 * * ?", description = "删除过期的消息重复推送记录")
public class DeleteExpiredRepeatInfoTask implements PlanExecutor {

    private final AppMsgRepeatInfoService appMsgRepeatInfoService;

    public DeleteExpiredRepeatInfoTask(AppMsgRepeatInfoService appMsgRepeatInfoService) {
        this.appMsgRepeatInfoService = appMsgRepeatInfoService;
    }

    @Override
    public void execute() {
        String taskData = PlanThreadLocal.getRequestData();
        long timeMills;
        if (StringUtils.isNotBlank(taskData)) {
            timeMills = Long.parseLong(taskData);
        } else {
            timeMills = 0;
        }
        ThreadUtil.execute(
                () -> {
                    log.info("删除过期的消息重复推送记录任务开始前");
                    appMsgRepeatInfoService.deleteExpiredRepeatInfo(timeMills);
                    log.info("删除过期的消息重复推送记录任务结束");
                }
        );
    }
}
