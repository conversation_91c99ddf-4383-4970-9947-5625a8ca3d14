package com.mioffice.ums.open.server.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.open.common.message.BatchMessage;
import com.mioffice.ums.open.common.message.MsgUser;
import com.mioffice.ums.open.server.entity.bo.*;
import com.mioffice.ums.open.server.bo.AppTaskLogBo;
import com.mioffice.ums.open.server.bo.PushSummaryBo;
import com.mioffice.ums.open.server.bo.UserRoleBo;
import com.mioffice.ums.open.server.entity.bo.AppInfoBo;
import com.mioffice.ums.open.server.entity.bo.AppManagerBo;
import com.mioffice.ums.open.server.entity.bo.DeptBo;
import com.mioffice.ums.open.server.entity.bo.MessageChannelBo;
import com.mioffice.ums.open.server.bo.*;
import com.mioffice.ums.open.server.entity.bo.*;
import com.mioffice.ums.open.server.entity.info.AppMsgRuleInfo;
import com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo;
import com.mioffice.ums.open.server.entity.info.AppWhiteListInfo;
import com.mioffice.ums.open.server.entity.info.DeptAppSysInfo;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.*;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  2020.09.11
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MapperUtil {

    MapperUtil INSTANCE = Mappers.getMapper(MapperUtil.class);

    /**
     * MsgUser ->MessageUser
     *
     * @param msgUser msgUser
     * @return MessageUser
     */
    MessageUser mapToMessageUser(MsgUser msgUser);

    /**
     * appInfo -> AppInfoBo
     *
     * @param appInfo appInfo
     * @return AppInfoBo
     */
    @Mapping(source = "channelsList", target = "channels")
    @Mapping(source = "appManagersList", target = "appManagers")
    @Mapping(source = "deptsList", target = "depts")
    AppInfoBo mapToAppInfoBo(AppInfo appInfo);

    @Mapping(target = "channelsList", source = "channels")
    @Mapping(target = "appManagersList", source = "appManagers")
    @Mapping(target = "deptsList", source = "depts")
    AppInfo mapToAppInfo(AppInfoBo bo);

    Dept mapToDept(DeptBo deptBo);

    /**
     * @param bo appManagerBo
     * @return AppManager
     */
    AppManager mapToAppManager(AppManagerBo bo);

    /**
     * @param channelBo channelBo
     * @return MessageChannel
     */
    @Mapping(source = "botAppIds", target = "botInfosList")
    MessageChannel mapToMessageChannel(MessageChannelBo channelBo);

    /**
     * @param channel channel
     * @return MessageChannelBo
     */
    @Mapping(target = "botAppIds", source = "botInfosList")
    MessageChannelBo mapToMessageChannel(MessageChannel channel);

    /**
     * botInfo -> BotInfoDesc
     *
     * @param botInfo
     * @return
     */
    MessageChannelBo.BotInfoDesc mapToBotInfoDesc(BotInfo botInfo);

    /**
     * botInfo -> BotInfoDesc
     *
     * @param botInfoDesc
     * @return
     */
    BotInfo mapToBotInfo(MessageChannelBo.BotInfoDesc botInfoDesc);

    /**
     * mapToAppManagerBo
     *
     * @param appSysInfo appSysInfo
     * @return List<AppManagerBo>
     */
    List<AppManagerBo> mapToAppManagerBo(List<UserAppSysInfo> appSysInfo);


    /**
     * mapToDeptBo
     *
     * @param deptAppSysInfos deptAppSysInfos
     * @return List<DeptBo>
     */
    List<DeptBo> mapToDeptBo(List<DeptAppSysInfo> deptAppSysInfos);

    /**
     * mapToAppListQueryInfoBO
     *
     * @param appListQueryInfo appListQueryInfo
     * @return AppListQueryInfoBO
     */
    AppListQueryInfoBO mapToAppListQueryInfoBO(AppListQueryInfo appListQueryInfo);

    /**
     * mapToAppListRecordInfo
     *
     * @param appListRecordInfoBO appListRecordInfoBO
     * @return AppListRecordInfo
     */
    AppListRecordInfo mapToAppListRecordInfo(AppListRecordInfoBO appListRecordInfoBO);

    /**
     * mapToUsernameAndName
     *
     * @param usernameAndNameBO usernameAndNameBO
     * @return UsernameAndName
     */
    UsernameAndName mapToUsernameAndName(UsernameAndNameBO usernameAndNameBO);

    List<UsernameAndName> mapToUsernameAndNameList(List<UsernameAndNameBO> usernameAndNameBOList);

    /**
     * mapToAppApprovalListQueryInfoBO
     *
     * @param appApprovalListQueryInfo appApprovalListQueryInfo
     * @return AppApprovalListQueryInfoBO
     */
    AppApprovalListQueryInfoBO mapToAppApprovalListQueryInfoBO(AppApprovalListQueryInfo appApprovalListQueryInfo);

    @Mapping(source = "templateChannel", target = "channel")
    MessageTemplateDetail mapToMessageTemplateDetail(AppSysTemplateInfo appSysTemplateInfo);

    GetTemplatePageRequestBO mapToGetTemplatePageRequestBO(GetTemplatePageRequest getTemplatePageRequest);

    @Mapping(source = "records", target = "recordsList")
    TemplatePage mapToTemplatePage(TemplateListBO templatePageBO);

    TemplatePageRecord mapToTemplatePageRecord(TemplateListRecord templateListRecord);

    @Mapping(source = "botBizIdList", target = "botBizIds")
    AddTemplateRequestBO mapToAddTemplateRequestBO(AddTemplateRequest addTemplateRequest);


    GetMsgRulePageRequestBO mapToGetMsgRulePageRequestBO(GetMessageRulePageRequest getMessageRulePageRequest);

    @Mapping(source = "roleList",target = "role")
    AddMsgRuleInfoRequestBO mapToAddMsgRuleRequestBO(AddMessageRuleRequest addMessageRuleRequest);


    MessageRuleDetail mapToMessageRuleDetail(AppMsgRuleInfo appMsgRuleInfo);

    AppTopicInfo map2AppTopicInfo(com.mioffice.ums.open.server.entity.info.AppTopicInfo appTopicInfo);

    RobotAppTopicInfo map2RobotTopicListBO(RobotTopicListBO robotTopicListBO);




    /**
     * mapToPushLog
     *
     * @param appTaskLogBo 推送日志
     * @return List<PushLog>
     */
    PushLog mapToPushLog(AppTaskLogBo appTaskLogBo);

    /**
     * AppUserRole
     *
     * @param userRoleBo 用户角色
     * @return AppUserRole
     */
    AppUserRole mapToAppUserRole(UserRoleBo userRoleBo);

    /**
     * mapToPushDetail
     *
     * @param pushDetail 推送详情
     * @return PushDetail
     */
    PushDetail mapToPushDetail(PushSummaryBo.PushDetail pushDetail);

    /**
     * mapToAppMsgCountInfoList
     *
     * @param list
     * @return
     */
    List<AppMsgCountInfo> mapToAppMsgCountInfoList(List<AppMessageCountBo> list);

    /**
     * mapToAppChannelCountList
     *
     * @param appChannelCountBoList
     * @return
     */
    List<AppChannelCount> mapToAppChannelCountList(List<AppChannelCountBo> appChannelCountBoList);

    /**
     * mapToAppSummaryList
     * @param appSummaryBOList
     * @return
     */
    List<AppSummary> mapToAppSummaryList(List<AppSummaryBO> appSummaryBOList);

    /**
     * mapToAppSummaryList
     * @param appWhiteListInfoIPage
     * @return
     */
    @Mapping(target = "recordsList",source = "records")
    WhiteListPage mapToQueryWhiteListResponse(IPage<AppWhiteListInfo> appWhiteListInfoIPage);

    WhiteList mapToWhiteList(AppWhiteListInfo appWhiteListInfo);

    @Mapping(target = "userList",source = "userListList")
    BatchMessage mapToBatchMessage(SendMessageBatchRequest sendMessageBatchRequest);
}
