package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 应用表
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_sys_info")
public class AppSysInfo extends BaseEntity {
    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 应用secret
     */
    @TableField(value = "app_secret")
    private String appSecret;

    /**
     * 应用name
     */
    @TableField(value = "app_name")
    private String appName;

    /**
     * 预估qps
     */
    @TableField(value = "qps")
    private Integer qps;

    /**
     * 应用状态（1 审核中，2 审核通过，3 审核驳回， 4 已取消，5 已停用）
     */
    @TableField(value = "app_sys_status")
    private Byte appSysStatus;

    /**
     * 停用前状态
     */
    @TableField(value = "old_app_sys_status")
    private Byte oldAppSysStatus;

    /**
     * 更新者username
     */
    @TableField(value = "update_username")
    private String updateUsername;

    /**
     * 更新者name
     */
    @TableField(value = "update_name")
    private String updateName;

    /**
     * 创建者name
     */
    @TableField(value = "create_name")
    private String createName;

    /**
     * 创建者username
     */
    @TableField(value = "create_username")
    private String createUsername;

    /**
     * 报警状态（0关闭，1开启）
     */
    @TableField(value = "alarm_status")
    private Byte alarmStatus;

    /**
     * 报警阈值
     */
    @TableField(value = "alarm_threshold")
    private Integer alarmThreshold;

    /**
     * 去重状态（0关闭，1开启）
     */
    @TableField(value = "deduplicate_status")
    private Byte deduplicateStatus;

    /**
     * 白名单 状态
     */
    @TableField(value = "white_status")
    private Byte whiteStatus;

    public static AppSysInfo newCreateAndUpdateTimeInstant() {
        AppSysInfo appSysInfo = new AppSysInfo();
        appSysInfo.setCreateTime(System.currentTimeMillis());
        appSysInfo.setUpdateTime(System.currentTimeMillis());
        return appSysInfo;
    }

    public static AppSysInfo newUpdateTimeInstant() {
        AppSysInfo appSysInfo = new AppSysInfo();
        appSysInfo.setUpdateTime(System.currentTimeMillis());
        return appSysInfo;
    }
}
