package com.mioffice.ums.open.server.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;

/**
 * <p>
 * local date time 工具类
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/22 9:31 上午
 */
@Slf4j
public class LocalDateTimeUtil {

    /**
     *  时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取本月开始
     */
    public static long getMonthStart(){
        LocalDate localDate = LocalDate.now();
        LocalDate monthStartDay = localDate.with(TemporalAdjusters.firstDayOfMonth());
        return LocalDateTimeUtil.getLongTimeWithDateTime(LocalDateTime.of(monthStartDay, LocalTime.MIN));
    }

    /**
     * 获取本月结束
     */
    public static long getMonthEnd(){
        LocalDate localDate = LocalDate.now();
        LocalDate monthEndDay = localDate.with(TemporalAdjusters.lastDayOfMonth());
        return LocalDateTimeUtil.getLongTimeWithDateTime(LocalDateTime.of(monthEndDay, LocalTime.MAX));
    }


    /**
     * 获取
     */
    public static long getSevenDaysAgo(){
        LocalDate localDate = LocalDate.now();
        LocalDate monthEndDay = localDate.minusDays(7);
        return LocalDateTimeUtil.getLongTimeWithDateTime(LocalDateTime.of(monthEndDay, LocalTime.MAX));
    }

    /**
     * 获取昨天开始时, "yyyy-MM-dd HH:mm:ss";
     */
    public static String getYesterdayBegin(){
        LocalDate localDate = LocalDate.now().minusDays(1);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
        return dateTimeFormatter.format(LocalDateTime.of(localDate, LocalTime.MIN));
    }

    /**
     * 获取昨天结束时间,格式yyyy-MM-dd HH:mm:ss
     */
    public static String getYesterdayEnd(){
        LocalDate localDate = LocalDate.now().minusDays(1);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
        return dateTimeFormatter.format(LocalDateTime.of(localDate, LocalTime.MAX));
    }

    /**
     * 获取昨天开始时 - long;
     */
    public static Long getYesterdayBeginLong(){
        LocalDate localDate = LocalDate.now().minusDays(1);
        return getLongTimeWithDateTime(LocalDateTime.of(localDate, LocalTime.MIN));
    }

    /**
     * 获取昨天结束时间 - long
     */
    public static Long getYesterdayEndLong(){
        LocalDate localDate = LocalDate.now().minusDays(1);
        return getLongTimeWithDateTime(LocalDateTime.of(localDate, LocalTime.MAX));
    }

    public static Long getTodayBeginLong(){
        LocalDate localDate = LocalDate.now();
        return getLongTimeWithDateTime(LocalDateTime.of(localDate, LocalTime.MIN));
    }

    public static Long getTodayEndLong(){
        LocalDate localDate = LocalDate.now();
        return getLongTimeWithDateTime(LocalDateTime.of(localDate, LocalTime.MAX));
    }

    public static Long getDayBeginLong(String timeStr){
        LocalDate localDate = LocalDate.parse(timeStr);
        return getLongTimeWithDateTime(LocalDateTime.of(localDate, LocalTime.MIN));
    }

    public static Long getDayEndLong(String timeStr){
        LocalDate localDate = LocalDate.parse(timeStr);
        return getLongTimeWithDateTime(LocalDateTime.of(localDate, LocalTime.MAX));
    }

    /**
     * LocalDateTime,获取根据字符串时间和格式,获取Long 时间
     */
    public static Long getLongTimeWithDateTime (LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)){
            return 0L;
        }
        try {
            OffsetDateTime offsetDateTime = OffsetDateTime.now();
            Instant instantFromDateTime = localDateTime.toInstant(offsetDateTime.getOffset());
            return instantFromDateTime.toEpochMilli();
        }catch (Exception e){
            log.error("解析LocalDateTime异常");
            return 0L;
        }
    }

    /**
     * 根据字符串时间和格式,获取Long 时间
     */
    public static Long getLongTime (String dateStr, String pattern) {
        if (StringUtils.isEmpty(dateStr)) {
            return 0L;
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(pattern);
            return formatter.parse(dateStr).getTime();
        } catch (ParseException e) {
            log.error("解析时间字符串异常");
            return 0L;
        }
    }
}
