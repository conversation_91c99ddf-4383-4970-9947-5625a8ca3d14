package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用白名单表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_white_list_info")
public class AppWhiteListInfo extends BaseEntity {
    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)
     */
    @TableField(value = "channel")
    private Byte channel;

    /**
     * 白名单id
     */
    @TableField(value = "white_id")
    private String whiteId;

    public static com.mioffice.ums.open.server.entity.info.AppWhiteListInfo newCreateAndUpdateTimeInstant() {
        com.mioffice.ums.open.server.entity.info.AppWhiteListInfo appWhiteListInfo = new com.mioffice.ums.open.server.entity.info.AppWhiteListInfo();
        appWhiteListInfo.setCreateTime(System.currentTimeMillis());
        appWhiteListInfo.setUpdateTime(System.currentTimeMillis());
        return appWhiteListInfo;
    }

    public static com.mioffice.ums.open.server.entity.info.AppWhiteListInfo newUpdateTimeInstant() {
        com.mioffice.ums.open.server.entity.info.AppWhiteListInfo appWhiteListInfo = new com.mioffice.ums.open.server.entity.info.AppWhiteListInfo();
        appWhiteListInfo.setUpdateTime(System.currentTimeMillis());
        return appWhiteListInfo;
    }
}