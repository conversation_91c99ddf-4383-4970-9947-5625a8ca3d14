package com.mioffice.ums.open.server.manager;

import cn.hutool.core.date.DateTime;
import com.mioffice.ums.open.server.entity.bo.AppReportBO;
import com.mioffice.ums.open.server.entity.bo.MsgMd5RepeatBO;
import com.mioffice.ums.open.server.entity.info.AppSysMonitorInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.25
 */
public interface SendMessage {

    void sendRepeatAlarmMsg(Map<String, List<MsgMd5RepeatBO>> appIdMap, Map<String, Integer> appIdMaxMap);

    void sendAppDailyReport(List<AppReportBO> appSysMonitorInfoList);
}
