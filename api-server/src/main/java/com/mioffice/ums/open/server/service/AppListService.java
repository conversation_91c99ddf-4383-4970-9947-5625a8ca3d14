package com.mioffice.ums.open.server.service;

import com.mioffice.ums.open.server.entity.bo.AppSummaryBO;
import com.mioffice.ums.open.server.entity.bo.CommonResultBO;
import com.mioffice.ums.open.server.entity.bo.UsernameAndNameBO;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListInfo;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/19 2:38 下午
 * version: 1.0.0
 */
public interface AppListService {

    AppListInfo getAppList(String loginName, String loginUsername, Long page, Long size, String appName, String managerUsernameList, String applyUsername, String channel, String appSysStatus, String beginDate, String endDate);

    CommonResultBO appStart(Long id, String username, String name, List<String> userTypeList);

    CommonResultBO appStop(Long id, String username, String name, List<String> userTypeList);

    boolean appUseApply(Long id, String applyName, String applyUsername);

    List<UsernameAndNameBO> getManager(String searchWord);

    List<UsernameAndNameBO> getAppApplyUsers(String searchWord);

    List<AppSummaryBO> getSysSummary();
}
