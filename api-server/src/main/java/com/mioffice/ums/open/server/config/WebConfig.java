package com.mioffice.ums.open.server.config;

import com.mioffice.ums.open.server.web.filter.AuthFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Configuration
public class WebConfig {

    @Bean
    public FilterRegistrationBean<AuthFilter> authFilterFilterRegistrationBean(AuthFilter authFilter) {
        FilterRegistrationBean<AuthFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(authFilter);
        registrationBean.setOrder(1);
        registrationBean.setUrlPatterns(Collections.singletonList("/*"));
        registrationBean.setName("auth");
        return registrationBean;
    }
}
