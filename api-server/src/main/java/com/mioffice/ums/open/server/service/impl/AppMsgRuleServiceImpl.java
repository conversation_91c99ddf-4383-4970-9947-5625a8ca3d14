package com.mioffice.ums.open.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.util.JsonUtils;
import com.mioffice.ums.open.server.bo.AppMsgRuleListBO;
import com.mioffice.ums.open.server.bo.GetMsgRulePageRequestBO;
import com.mioffice.ums.open.server.entity.bo.AddMsgRuleInfoRequestBO;
import com.mioffice.ums.open.server.entity.bo.AddMsgRuleInfoResultBO;
import com.mioffice.ums.open.server.entity.bo.AppMsgRuleListRecord;
import com.mioffice.ums.open.server.entity.bo.CommonResultBO;
import com.mioffice.ums.open.server.entity.bo.UpdateMsgRuleRequestBO;
import com.mioffice.ums.open.server.entity.info.AppMsgRuleInfo;
import com.mioffice.ums.open.server.mapper.AppMsgRuleInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.mapper.UserAppSysInfoMapper;
import com.mioffice.ums.open.server.service.AppMsgRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 12 02,2022
 */
@Slf4j
@Service
public class AppMsgRuleServiceImpl implements AppMsgRuleService {

    private AppMsgRuleInfoMapper appMsgRuleInfoMapper;
    private AppSysInfoMapper appSysInfoMapper;
    private UserAppSysInfoMapper userAppSysInfoMapper;

    @Autowired
    public AppMsgRuleServiceImpl(AppMsgRuleInfoMapper appMsgRuleInfoMapper,
                                 AppSysInfoMapper appSysInfoMapper,
                                 UserAppSysInfoMapper userAppSysInfoMapper){
        this.appMsgRuleInfoMapper = appMsgRuleInfoMapper;
        this.appSysInfoMapper = appSysInfoMapper;
        this.userAppSysInfoMapper = userAppSysInfoMapper;
    }

    @Override
    public AppMsgRuleListBO messageRulePage(GetMsgRulePageRequestBO getMsgRulePageRequestBO) {
        Long appSysId = getMsgRulePageRequestBO.getAppSysId();
        Long page = getMsgRulePageRequestBO.getPage();
        Long size = getMsgRulePageRequestBO.getSize();
        String ruleName = getMsgRulePageRequestBO.getRuleName();
        Page<AppMsgRuleInfo> appSysTemplateInfoPage =getAppMsgRuleInfoPage(appSysId, page, size, ruleName);

        List<AppMsgRuleInfo> records = appSysTemplateInfoPage.getRecords();
        if (records.isEmpty()) {
            return AppMsgRuleListBO.newEmptyTemplateListBO(page, size);
        }

        AppMsgRuleListBO appMsgRuleListRecord=new AppMsgRuleListBO();
        List<AppMsgRuleListRecord> appMsgRuleListRecords = new ArrayList<>();
        records.forEach(
                record -> {
                    AppMsgRuleListRecord item = new AppMsgRuleListRecord();
                    item.setId(record.getId());
                    item.setAppSysId(record.getAppSysId());
                    item.setMessageRuleName(record.getRuleName());
                    item.setCallback(record.getCallback());
                    item.setPlaceholder(record.getPlaceholder());
                    item.setCreateName(record.getCreateUsername());
                    item.setCreateTime(record.getCreateTime());
                    item.setCreateUsername(record.getCreateUsername());
                    appMsgRuleListRecords.add(item);
                }
        );
        appMsgRuleListRecord.setSearchCount(appSysTemplateInfoPage.isSearchCount());
        appMsgRuleListRecord.setCurrent(appSysTemplateInfoPage.getCurrent());
        appMsgRuleListRecord.setPages(appSysTemplateInfoPage.getPages());
        appMsgRuleListRecord.setSize(appSysTemplateInfoPage.getSize());
        appMsgRuleListRecord.setTotal(appSysTemplateInfoPage.getTotal());
        appMsgRuleListRecord.setRecords(appMsgRuleListRecords);
        return appMsgRuleListRecord;
    }

    @Override
    public AddMsgRuleInfoResultBO addMessageRule(AddMsgRuleInfoRequestBO addMsgRuleInfoRequestBO) {
        AddMsgRuleInfoResultBO addMsgRuleInfoResultBO=new AddMsgRuleInfoResultBO();
        String ruleName =  addMsgRuleInfoRequestBO.getRuleName();
        String callback =  addMsgRuleInfoRequestBO.getCallback();
        Long appSysId = addMsgRuleInfoRequestBO.getAppSysId();
        String appId = addMsgRuleInfoRequestBO.getAppId();
        String placeholder = addMsgRuleInfoRequestBO.getPlaceholder();
        String username = addMsgRuleInfoRequestBO.getUsername();

        List<String> roles = addMsgRuleInfoRequestBO.getRole();


        //暂时不做这些校验
        /*
        AppSysInfo appSysInfo = appSysInfoMapper.selectById(appSysId);
        if (!roles.contains("ROLE_SYS_SUPER_ADMIN") && !roles.contains("ROLE_SYS_ADMIN")) {
            List<UserAppSysInfo> userAppSysInfoList = userAppSysInfoMapper.selectList(
                    Wrappers.<UserAppSysInfo>lambdaQuery()
                            .eq(UserAppSysInfo::getAppSysId, appSysId)
                            .eq(UserAppSysInfo::getUserType, AppUserRoleEnum.APP_USER_ROLE_CHARGE.getCode())
            );
            boolean isHaveAccess = false;
            for (UserAppSysInfo userAppSysInfo : userAppSysInfoList) {
                if (username.equals(userAppSysInfo.getUsername())) {
                    isHaveAccess = true;
                    break;
                }
            }
            if (!isHaveAccess) {
                addMsgRuleInfoResultBO.setAdd(false);
                addMsgRuleInfoResultBO.setCode(ResponseCode.AUTH_ERROR.getCode());
                addMsgRuleInfoResultBO.setMessage("用户没有权限访问此接口");
                return addMsgRuleInfoResultBO;
            }
        }

        if (appSysInfo.getAppSysStatus() != AppSysStatusEnum.APPROVED.getCode()) {
            addMsgRuleInfoResultBO.setAdd(false);
            addMsgRuleInfoResultBO.setMessage("只有审核通过的系统才可创建模板");
            return addMsgRuleInfoResultBO;
        }*/
        AppMsgRuleInfo appMsgRuleInfo=new AppMsgRuleInfo();
        appMsgRuleInfo.setRuleName(ruleName);
        appMsgRuleInfo.setAppSysId(appSysId);
        appMsgRuleInfo.setAppId(appId);
        appMsgRuleInfo.setCallback(callback);
        appMsgRuleInfo.setPlaceholder(placeholder);
        Long now=System.currentTimeMillis();
        appMsgRuleInfo.setCreateTime(now);
        appMsgRuleInfo.setCreateUsername(username);
        appMsgRuleInfo.setUpdateTime(now);
        appMsgRuleInfo.setUpdateUsername(username);
        appMsgRuleInfoMapper.insert(appMsgRuleInfo);

        addMsgRuleInfoResultBO.setAdd(true);
        addMsgRuleInfoResultBO.setMessage("ok");
        addMsgRuleInfoResultBO.setId(appMsgRuleInfo.getId());
        return addMsgRuleInfoResultBO;
    }

    @Override
    public ImmutablePair<ResponseCode,String> updateMessageRule(UpdateMsgRuleRequestBO updateMsgRuleRequestBO) {
        Long id = updateMsgRuleRequestBO.getId();
        AppMsgRuleInfo appMsgRuleInfo=appMsgRuleInfoMapper.selectById(id);

        if (Objects.isNull(appMsgRuleInfo)) {
            log.error("更新规则失败，规则不存在 id = [{}]",id);
            return ImmutablePair.of(ResponseCode.INTERNAL_ERROR,"规则没有找到");
        }
        String ruleName = StringUtils.isBlank(updateMsgRuleRequestBO.getRuleName()) ? appMsgRuleInfo.getRuleName() : updateMsgRuleRequestBO.getRuleName();
        String callback = StringUtils.isBlank(updateMsgRuleRequestBO.getCallback()) ? appMsgRuleInfo.getCallback() : updateMsgRuleRequestBO.getCallback();
        String placeholder = StringUtils.isBlank(updateMsgRuleRequestBO.getPlaceholder()) ? appMsgRuleInfo.getPlaceholder() : updateMsgRuleRequestBO.getPlaceholder();
        appMsgRuleInfo.setRuleName(ruleName);
        appMsgRuleInfo.setCallback(callback);
        appMsgRuleInfo.setPlaceholder(placeholder);
        appMsgRuleInfo.setUpdateTime(System.currentTimeMillis());
        appMsgRuleInfo.setUpdateUsername(updateMsgRuleRequestBO.getUsername());
        appMsgRuleInfoMapper.updateById(appMsgRuleInfo);
        return ImmutablePair.of(ResponseCode.SUCCESS,"");
    }

    @Override
    public AppMsgRuleInfo getMessageRuleDetail(Long id) {
        AppMsgRuleInfo appMsgRuleInfo = appMsgRuleInfoMapper.selectById(id);
        if (Objects.isNull(appMsgRuleInfo)) {
            log.error("id = [{}]的消息规则不存在", id);
        }
        return appMsgRuleInfo;
    }

    @Override
    public CommonResultBO deleteMessageRule(List<Long> idsList, String name, String username, List<String> roleList) {
        CommonResultBO commonResultBO = new CommonResultBO();
        try {
            List<AppMsgRuleInfo> appMsgRuleInfos = appMsgRuleInfoMapper.selectBatchIds(idsList);
            if(CollectionUtils.isEmpty(appMsgRuleInfos)){
                log.error("idsList = [{}],not find rules", JsonUtils.toJson(idsList));
                commonResultBO.setIsSuccess(false);
                return commonResultBO;
            }

/*            Map<Long,List<AppMsgRuleInfo>> appMsgRuleMap = appMsgRuleInfos.stream().collect(Collectors.groupingBy(AppMsgRuleInfo::getAppSysId));


            List<Long> templateIdList = new ArrayList<>();

            if (!roleList.contains("ROLE_SYS_SUPER_ADMIN") && !roleList.contains("ROLE_SYS_ADMIN")) {
                List<UserAppSysInfo> userAppSysInfoList = userAppSysInfoMapper.selectList(
                        Wrappers.<UserAppSysInfo>lambdaQuery()
                                .in(UserAppSysInfo::getAppSysId, appMsgRuleMap.keySet())
                                .eq(UserAppSysInfo::getUserType, AppUserRoleEnum.APP_USER_ROLE_CHARGE.getCode())
                );
                Map<Long, List<UserAppSysInfo>> appSysIdAndListMap = userAppSysInfoList.stream().collect(Collectors.groupingBy(UserAppSysInfo::getAppSysId));
                idsList.forEach(
                        id -> {
                            if (Objects.nonNull(appMsgRuleMap.get(id))) {
                                List<UserAppSysInfo> userAppSysInfos = appSysIdAndListMap.get(appMsgRuleMap.get(id));
                                boolean isHaveAccess = false;
                                for (UserAppSysInfo userAppSysInfo : userAppSysInfos) {
                                    if (username.equals(userAppSysInfo.getUsername())) {
                                        isHaveAccess = true;
                                        break;
                                    }
                                }
                                if (!isHaveAccess) {
                                    templateIdList.add(id);
                                }
                            }
                        }
                );
            }
            if (!templateIdList.isEmpty()) {
                commonResultBO.setCode(ResponseCode.AUTH_ERROR.getCode());
                commonResultBO.setIsSuccess(false);
                commonResultBO.setMessage("您没有id为".concat(JsonUtils.toJson(templateIdList)).concat("的模板删除权限"));
            } else {
            }*/

            appMsgRuleInfoMapper.deleteBatchIds(idsList);
            commonResultBO.setIsSuccess(true);
            return commonResultBO;
        } catch (Exception e) {
            log.error("idsList = [{}]", JsonUtils.toJson(idsList));
            commonResultBO.setIsSuccess(false);
            return commonResultBO;
        }
    }

    private Page<AppMsgRuleInfo> getAppMsgRuleInfoPage(Long appSysId, Long page, Long size, String ruleName){
        Page<AppMsgRuleInfo> msgRulePage = new Page<>(page, size);
        LambdaQueryWrapper<AppMsgRuleInfo> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(AppMsgRuleInfo::getAppSysId,appSysId);
        if (StringUtils.isNotBlank(ruleName)) {
            lambdaQuery.and(
                    wrapper -> wrapper.like(AppMsgRuleInfo::getRuleName, ruleName)
            );
        }
        lambdaQuery.orderByDesc(AppMsgRuleInfo::getId);
        return appMsgRuleInfoMapper.selectPage(msgRulePage, lambdaQuery);
    }

}
