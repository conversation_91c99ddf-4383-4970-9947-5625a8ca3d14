package com.mioffice.ums.open.server.utils;

import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.constats.MessageStatusEnum;
import com.mioffice.ums.open.common.message.MsgUser;
import com.mioffice.ums.open.common.util.JsonUtils;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.25
 */
public class MessageUtil {

    public static MessageUser toConvert(String botAppId, String groupId, Byte contentFlag, MsgUser msgUser,
                                        String sysId) {
        return toConvertBuilder(botAppId, groupId, contentFlag, msgUser, sysId).build();
    }

    public static MessageUser toDeduplicateConvert(String botAppId, String groupId, MsgUser msgUser, Byte contentFlag,
                                                   Collection<String> contentMd5Collections, String sysId) {
        MessageUser.Builder builder = toConvertBuilder(botAppId, groupId, contentFlag, msgUser, sysId);
        Set<String> contentMd5Set = new HashSet<>(contentMd5Collections);
        if (contentMd5Set.contains(msgUser.getContentMd5())) {
            builder.setMessageStatue(MessageStatusEnum.SEND_INTERRUPT.getStatus());
            builder.setErrorLog("重复消息，中断处理 contentMd5 = " + msgUser.getContentMd5());
        }
        return builder.build();
    }

    public static MessageUser.Builder toConvertBuilder(String botAppId, String groupId, Byte contentFlag,
                                                       MsgUser msgUser, String sysId) {
        MessageUser messageUser = MapperUtil.INSTANCE.mapToMessageUser(msgUser);
        MessageUser.Builder builder = messageUser.toBuilder();
        builder.setAppId(botAppId);
        builder.setExtraId(groupId);
        builder.setPlaceholderContent(JsonUtils.toJson(msgUser.getParams()));
        builder.setSysId(sysId);
        if (Objects.nonNull(msgUser.getCcEmails()) && !msgUser.getCcEmails().isEmpty()) {
            builder.setCcEmail(String.join(";", msgUser.getCcEmails()));
        }
        if (Objects.nonNull(msgUser.getAttachUrls()) && !msgUser.getAttachUrls().isEmpty()) {
            builder.setAttachEmailUrl(JsonUtils.toJson(msgUser.getAttachUrls()));
        }
        if (Objects.nonNull(contentFlag)) {
            builder.setContentFlag(contentFlag);
        }

        return builder;
    }

    public static List<String> parseSendingId(Byte channel, MsgUser msgUser) {
        if (channel.equals(MessageChannelEnum.EMAIL.getType())) {
            List<String> emailList = new ArrayList<>();
            emailList.add(msgUser.getEmail());
            // 抄送人不是必传
            if (Objects.nonNull(msgUser.getCcEmails()) && !msgUser.getCcEmails().isEmpty()) {
                emailList.addAll(msgUser.getCcEmails());
            }
            return emailList;
        } else if (channel.equals(MessageChannelEnum.SMS.getType())) {
            return Collections.singletonList(msgUser.getPhone());
        } else {
            return Collections.singletonList(
                    StringUtils.isNotBlank(msgUser.getUsername()) ? msgUser.getUsername() : msgUser.getEmail());
        }
    }

    public static List<MessageUser> toWhiteConvert(String botAppId, String groupId, Byte contentFlag, Byte channel,
                                                   List<MsgUser> userList, Set<String> whiteSendIdSet, String sysId) {

        List<MessageUser> messageUserList = new ArrayList<>();
        for (MsgUser msgUser : userList) {
            MessageUser.Builder builder = toConvertBuilder(botAppId, groupId, contentFlag, msgUser, sysId);
            List<String> sendIdList = parseSendingId(channel, msgUser);
            // 非白名单内，直接中断 发送
            if (!whiteSendIdSet.containsAll(sendIdList)) {
                builder.setMessageStatue(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                builder.setErrorLog("非白名单人员，中断发送 sendIdList = " + sendIdList);
            }
            messageUserList.add(builder.build());
        }

        return messageUserList;
    }
}
