package com.mioffice.ums.open.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mioffice.ums.open.server.bo.BotAppIdAndBotBizIdBO;
import com.mioffice.ums.open.server.bo.GetTemplatePageRequestBO;
import com.mioffice.ums.open.server.bo.TemplateListBO;
import com.mioffice.ums.open.server.entity.bo.AddTemplateRequestBO;
import com.mioffice.ums.open.server.entity.bo.AddTemplateResultBO;
import com.mioffice.ums.open.server.entity.bo.CommonResultBO;
import com.mioffice.ums.open.server.entity.bo.UpdateTemplateRequestBO;
import com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/22 5:21 下午
 * version: 1.0.0
 */
public interface AppSysTemplateService extends IService<AppSysTemplateInfo> {

    AppSysTemplateInfo getMessageTemplateDetail(Long id);

    CommonResultBO deleteMessageTemplate(List<Long> idsList, String name, String username, List<String> roleList);

    TemplateListBO getTemplatePage(GetTemplatePageRequestBO getTemplatePageRequestBO);

    AddTemplateResultBO addTemplate(AddTemplateRequestBO addTemplateRequestBO);

    boolean updateTemplate(UpdateTemplateRequestBO updateTemplateRequestBO);

    List<BotAppIdAndBotBizIdBO> getSysBotList(long appSysId, int channel);
}
