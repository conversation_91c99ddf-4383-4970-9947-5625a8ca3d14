package com.mioffice.ums.open.server.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.server.entity.bo.AppListRecordInfoBO;
import com.mioffice.ums.open.server.entity.bo.AppSummaryBO;
import com.mioffice.ums.open.server.entity.bo.CommonResultBO;
import com.mioffice.ums.open.server.entity.bo.UsernameAndNameBO;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.mioffice.ums.open.server.enums.AppSysStatusEnum;
import com.mioffice.ums.open.server.enums.AppUserRoleEnum;
import com.mioffice.ums.open.server.manager.AppSysApplyManager;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.mapper.UserAppSysInfoMapper;
import com.mioffice.ums.open.server.service.AppListService;
import com.mioffice.ums.open.server.utils.MapperUtil;
import com.mioffice.ums.open.server.utils.TypeConvertUtil;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListRecordInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/19 2:39 下午
 * version: 1.0.0
 */
@Slf4j
@Service
public class AppListServiceImpl implements AppListService {

    public static final String DATE_PATTERN = "yyyy-MM-dd";

    private final AppSysInfoMapper appSysInfoMapper;

    private final UserAppSysInfoMapper userAppSysInfoMapper;

    private final AppSysApplyManager appSysApplyManager;

    private final RedissonClient redissonClient;

    public AppListServiceImpl(AppSysInfoMapper appSysInfoMapper, UserAppSysInfoMapper userAppSysInfoMapper,
                              AppSysApplyManager appSysApplyManager, RedissonClient redissonClient) {
        this.appSysInfoMapper = appSysInfoMapper;
        this.userAppSysInfoMapper = userAppSysInfoMapper;
        this.appSysApplyManager = appSysApplyManager;
        this.redissonClient = redissonClient;
    }

    @Override
    public AppListInfo getAppList(String loginName, String loginUsername, Long page, Long size, String appName,
                                  String managerUsernameList, String applyUsername, String channel, String appSysStatus,
                                  String beginDate, String endDate) {
        Page<AppListRecordInfoBO> dataPage = new Page<>(page, size);
        AppListInfo.Builder appListInfoBuilder = AppListInfo.newBuilder();
        List<String> managerList = new ArrayList<>();
        if (StringUtils.isNotBlank(managerUsernameList)) {
            managerList = Arrays.asList(managerUsernameList.split(","));
        }
        List<String> applyUsernameList = new ArrayList<>();
        if (StringUtils.isNotBlank(applyUsername)) {
            applyUsernameList = Arrays.asList(applyUsername.split(","));
        }
        List<Byte> channelList = new ArrayList<>();
        if (StringUtils.isNotBlank(channel)) {
            channelList = TypeConvertUtil.stringToByteList(channel);
        }
        List<Byte> appSysStatusList = new ArrayList<>();
        if (StringUtils.isNotBlank(appSysStatus)) {
            appSysStatusList = TypeConvertUtil.stringToByteList(appSysStatus);
        }
        long beginTime = 0;
        if (StringUtils.isNotBlank(beginDate)) {
            beginTime = DateUtil.beginOfDay(DateUtil.parse(beginDate, DATE_PATTERN)).getTime();
        }
        long endTime = 0;
        if (StringUtils.isNotBlank(endDate)) {
            endTime = DateUtil.endOfDay(DateUtil.parse(endDate, DATE_PATTERN)).getTime();
        }

        log.info("appPage请求查询数据库时间 = [{}]", System.currentTimeMillis());
        List<AppListRecordInfo> appListRecordInfoList = new ArrayList<>();
        List<AppListRecordInfoBO> appListRecordInfoBOS =
                appSysInfoMapper.selectAppListPage(dataPage, loginUsername, appName, managerList, applyUsernameList,
                        channelList, appSysStatusList, beginTime, endTime);
        appListInfoBuilder.setCurrent(page);
        appListInfoBuilder.setSize(size);
        if (appListRecordInfoBOS.isEmpty()) {
            appListInfoBuilder.setPages(0L);
            appListInfoBuilder.setTotal(0L);
            appListInfoBuilder.setSearchCount(true);
            appListInfoBuilder.addAllRecords(Collections.emptyList());
            return appListInfoBuilder.build();
        }
        appListRecordInfoBOS.forEach(
                appListRecordInfoBO -> {
                    AppListRecordInfo.Builder appListRecordInfoBuilder = AppListRecordInfo.newBuilder();
                    appListRecordInfoBuilder
                            .setId(appListRecordInfoBO.getId())
                            .setAppId(appListRecordInfoBO.getAppId())
                            .setAppName(appListRecordInfoBO.getAppName())
                            .setAppSysStatus((int) appListRecordInfoBO.getAppSysStatus())
                            .setPushCount(appListRecordInfoBO.getPushCount())
                            .setCreateTime(appListRecordInfoBO.getCreateTime())
                            .setTopicStatus(appListRecordInfoBO.getTopicStatus())
                            .setCreateUser(
                                    MapperUtil.INSTANCE.mapToUsernameAndName(appListRecordInfoBO.getCreateUser()))
                            .addAllChannel(appListRecordInfoBO.getChannel())
                            .addAllManager(
                                    MapperUtil.INSTANCE.mapToUsernameAndNameList(appListRecordInfoBO.getManager()));
                    AppListRecordInfo appListRecordInfo = appListRecordInfoBuilder.build();
                    appListRecordInfoList.add(appListRecordInfo);
                }
        );
        log.info("appPage请求查询数据库结束时间 = [{}]", System.currentTimeMillis());
        appListInfoBuilder.setPages(dataPage.getPages());
        appListInfoBuilder.setTotal(dataPage.getTotal());
        appListInfoBuilder.setSearchCount(true);
        appListInfoBuilder.addAllRecords(appListRecordInfoList);
        return appListInfoBuilder.build();
    }

    @Override
    public CommonResultBO appStart(Long id, String username, String name, List<String> roleList) {
        CommonResultBO commonResultBO = new CommonResultBO();
        try {
            AppSysInfo appSysInfo = appSysInfoMapper.selectById(id);
            if (notHaveAccess(id, username, roleList, commonResultBO)) {
                return commonResultBO;
            }
            AppSysInfo appSysInfoUpdate = AppSysInfo.newUpdateTimeInstant();
            appSysInfoUpdate.setId(id);
            appSysInfoUpdate.setAppSysStatus(AppSysStatusEnum.APPROVED.getCode());
            appSysInfoMapper.updateById(appSysInfoUpdate);
            commonResultBO.setIsSuccess(true);
            // 清除缓存
            redissonClient.getBucket(String.format("ums:open:server:app_info::%s", appSysInfo.getAppId())).delete();
            return commonResultBO;
        } catch (Exception e) {
            log.error("id = [{}]的系统启用失败", id, e);
            commonResultBO.setMessage("");
            commonResultBO.setIsSuccess(false);
            return commonResultBO;
        }
    }

    @Override
    public CommonResultBO appStop(Long id, String username, String name, List<String> roleList) {
        CommonResultBO commonResultBO = new CommonResultBO();
        try {
            AppSysInfo appSysInfo = appSysInfoMapper.selectById(id);
            if (notHaveAccess(id, username, roleList, commonResultBO)) {
                return commonResultBO;
            }
            AppSysInfo appSysInfoUpdate = AppSysInfo.newUpdateTimeInstant();
            appSysInfoUpdate.setId(id);
            appSysInfoUpdate.setAppSysStatus(AppSysStatusEnum.STOP.getCode());
            appSysInfoMapper.updateById(appSysInfoUpdate);
            commonResultBO.setIsSuccess(true);
            // 清除缓存
            redissonClient.getBucket(String.format("ums:open:server:app_info::%s", appSysInfo.getAppId())).delete();
            return commonResultBO;
        } catch (Exception e) {
            log.error("id = [{}]的系统停用失败", id, e);
            commonResultBO.setMessage("");
            commonResultBO.setIsSuccess(false);
            return commonResultBO;
        }
    }

    private boolean notHaveAccess(Long id, String username, List<String> roleList, CommonResultBO commonResultBO) {
        if (!roleList.contains("ROLE_SYS_SUPER_ADMIN") && !roleList.contains("ROLE_SYS_ADMIN")) {
            List<UserAppSysInfo> userAppSysInfoList = userAppSysInfoMapper.selectList(
                    Wrappers.<UserAppSysInfo>lambdaQuery()
                            .eq(UserAppSysInfo::getAppSysId, id)
                            .eq(UserAppSysInfo::getUserType, AppUserRoleEnum.APP_USER_ROLE_CHARGE.getCode())
            );
            boolean isHaveAccess = false;
            for (UserAppSysInfo userAppSysInfo : userAppSysInfoList) {
                if (username.equals(userAppSysInfo.getUsername())) {
                    isHaveAccess = true;
                    break;
                }
            }
            if (!isHaveAccess) {
                commonResultBO.setIsSuccess(false);
                commonResultBO.setCode(ResponseCode.AUTH_ERROR.getCode());
                commonResultBO.setMessage("用户没有权限访问此接口");
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean appUseApply(Long id, String applyName, String applyUsername) {
        try {
            AppSysInfo appSysInfoUpdate = AppSysInfo.newUpdateTimeInstant();
            appSysInfoUpdate.setId(id);
            appSysInfoUpdate.setUpdateName(applyName);
            appSysInfoUpdate.setUpdateUsername(applyUsername);
            appSysInfoUpdate.setAppSysStatus(AppSysStatusEnum.APPROVING.getCode());
            appSysInfoMapper.updateById(appSysInfoUpdate);
            appSysApplyManager.createAppSysApplyRecordInfo(applyName, applyUsername, id);
            return true;
        } catch (Exception e) {
            log.error("id = [{}]的系统申请使用失败", id, e);
            return false;
        }
    }

    @Override
    public List<UsernameAndNameBO> getManager(String searchWord) {
        List<UserAppSysInfo> userAppSysInfoList = userAppSysInfoMapper.selectList(
                Wrappers.<UserAppSysInfo>lambdaQuery().eq(UserAppSysInfo::getUserType, 1)
                        .and(wrapper -> wrapper.like(UserAppSysInfo::getName, searchWord)
                                .or()
                                .likeRight(UserAppSysInfo::getUsername, searchWord))
        );
        if (userAppSysInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        List<UsernameAndNameBO> managerList = new ArrayList<>();
        userAppSysInfoList.forEach(
                userAppSysInfo -> {
                    UsernameAndNameBO usernameAndNameBO =
                            new UsernameAndNameBO(userAppSysInfo.getUsername(), userAppSysInfo.getName());
                    managerList.add(usernameAndNameBO);
                }
        );
        return managerList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<UsernameAndNameBO> getAppApplyUsers(String searchWord) {
        List<AppSysInfo> appSysInfoList = appSysInfoMapper.selectList(
                Wrappers.<AppSysInfo>lambdaQuery()
                        .like(AppSysInfo::getCreateName, searchWord)
                        .or()
                        .likeRight(AppSysInfo::getCreateUsername, searchWord)
        );
        if (appSysInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        List<UsernameAndNameBO> appApplyUsers = new ArrayList<>();
        appSysInfoList.forEach(
                appSysInfo -> {
                    UsernameAndNameBO usernameAndNameBO =
                            new UsernameAndNameBO(appSysInfo.getCreateUsername(), appSysInfo.getCreateName());
                    appApplyUsers.add(usernameAndNameBO);
                }
        );
        return appApplyUsers.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<AppSummaryBO> getSysSummary() {
        List<AppSysInfo> appSysInfoList = appSysInfoMapper.selectList(Wrappers.lambdaQuery());
        return appSysInfoList.stream().map(AppSummaryBO::new).collect(Collectors.toList());
    }
}
