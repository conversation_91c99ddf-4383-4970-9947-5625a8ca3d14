package com.mioffice.ums.open.server.service;

import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.message.*;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.11
 */
public interface MessageService {

    /**
     * 批量发送消息
     *
     * @param batchMessage
     * @param sysId 系统id
     * @return
     */
    UmsResponse<MsgResult> sendBatch(BatchMessage batchMessage, String sysId);

    /**
     * 批量发送消息 v2 接口，主要使用 botBizId 发送消息
     *
     * @param batchMessage
     * @param sysId
     * @return
     */
    UmsResponse<MsgResult> sendBatchV2(BatchMessage batchMessage, String sysId, String extraId);

    /**
     * 上传图片到 lark
     *
     * @param imageUrl
     * @return
     */
    UmsResponse<ImageResult> uploadLarkImage(ImageUrl imageUrl, String appId);

    /**
     * 根据groupId获取推送日志
     *
     * @param groupId 推送结果请求参数体
     * @param appId
     * @return 推送结果
     */
    UmsResponse<AppPushResult> pushLogResult(String groupId, String appId);

    /**
     * 获取消息的原始体
     *
     * @param groupId
     * @param username
     * @return
     */
    UmsResponse<List<FinalMsgResult>> getFinalMessage(String groupId, String username);

    ImmutablePair<Boolean, String> precheckBotAndTemplate(String appId, Integer channel, String botBizId,
                                                          String templateBizId);
}
