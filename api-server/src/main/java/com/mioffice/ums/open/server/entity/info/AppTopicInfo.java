package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 系统队列表
 *
 * @TableName app_topic_info
 */
@TableName(value = "app_topic_info")
@Data
public class AppTopicInfo extends BaseEntity {

    /**
     * queue topic
     */
    private String topic;

    /**
     * 应用业务id
     */
    private String appId;

    /**
     * 创建人cas账号
     */
    private String createBy;

    /**
     * 更新人cas账号
     */
    private String updateBy;

    /**
     * 0审核中1审核通过2审核不通过3取消申请
     */
    private Integer status;

    /**
     * BPM流程实例ID
     */
    private String bpmInstanceId;

    public static AppTopicInfo newCreateAndUpdateTimeInstant() {
        AppTopicInfo appTopicInfo = new AppTopicInfo();
        appTopicInfo.setCreateTime(System.currentTimeMillis());
        appTopicInfo.setUpdateTime(System.currentTimeMillis());
        return appTopicInfo;
    }

    public static AppTopicInfo newUpdateTimeInstant() {
        AppTopicInfo appTopicInfo = new AppTopicInfo();
        appTopicInfo.setUpdateTime(System.currentTimeMillis());
        return appTopicInfo;
    }
}
