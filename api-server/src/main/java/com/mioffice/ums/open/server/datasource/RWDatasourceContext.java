package com.mioffice.ums.open.server.datasource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.11
 */
public class RWDatasourceContext {

    public static final RWDatasourceContext INSTANCE = new RWDatasourceContext();

    public static final String MASTER = "master";
    public static final String SLAVE = "slave";

    private ThreadLocal<String> threadLocal = new ThreadLocal<>();

    public void master() {
        threadLocal.set(MASTER);
    }

    public void slave() {
        threadLocal.set(SLAVE);
    }

    public String get() {
        return threadLocal.get();
    }

}
