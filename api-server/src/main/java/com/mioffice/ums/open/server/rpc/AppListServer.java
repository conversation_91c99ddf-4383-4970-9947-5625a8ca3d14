package com.mioffice.ums.open.server.rpc;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.protobuf.Empty;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.exception.BizException;
import com.mioffice.ums.open.server.bo.AppMsgRuleListBO;
import com.mioffice.ums.open.server.bo.BotAppIdAndBotBizIdBO;
import com.mioffice.ums.open.server.bo.GetMsgRulePageRequestBO;
import com.mioffice.ums.open.server.bo.GetTemplatePageRequestBO;
import com.mioffice.ums.open.server.bo.TemplateListBO;
import com.mioffice.ums.open.server.entity.bo.*;
import com.mioffice.ums.open.server.entity.info.AppMsgRuleInfo;
import com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo;
import com.mioffice.ums.open.server.entity.info.AppTopicInfo;
import com.mioffice.ums.open.server.service.AppApprovalListService;
import com.mioffice.ums.open.server.service.AppListService;
import com.mioffice.ums.open.server.service.AppMsgRuleService;
import com.mioffice.ums.open.server.service.AppSysTemplateService;
import com.mioffice.ums.open.server.service.AppTopicInfoService;
import com.mioffice.ums.open.server.utils.MapperUtil;
import com.xiaomi.info.grpc.server.annotation.RpcServer;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/19 12:28 下午
 * version: 1.0.0
 */
@Slf4j
@RpcServer
public class AppListServer extends AppListServiceGrpc.AppListServiceImplBase {

    private final AppListService appListService;

    private final AppApprovalListService appApprovalListService;

    private final AppSysTemplateService appSysTemplateService;

    private final AppMsgRuleService appMsgRuleService;

    private final AppTopicInfoService appTopicInfoService;

    public AppListServer(AppListService appListService, AppApprovalListService appApprovalListService,
                         AppSysTemplateService appSysTemplateService, AppMsgRuleService appMsgRuleService,
                         AppTopicInfoService appTopicInfoService) {
        this.appListService = appListService;
        this.appApprovalListService = appApprovalListService;
        this.appSysTemplateService = appSysTemplateService;
        this.appMsgRuleService = appMsgRuleService;
        this.appTopicInfoService = appTopicInfoService;
    }

    @Override
    public void appListQuery(AppListQueryInfo request, StreamObserver<AppListQueryInfoResponse> responseObserver) {
        AppListQueryInfoBO appListQueryInfoBO = MapperUtil.INSTANCE.mapToAppListQueryInfoBO(request);
        log.info("appPage请求打入时间 = [{}]", System.currentTimeMillis());
        AppListInfo appListInfo = appListService.getAppList(
                appListQueryInfoBO.getLoginName(),
                appListQueryInfoBO.getLoginUsername(),
                appListQueryInfoBO.getPage(),
                appListQueryInfoBO.getSize(),
                appListQueryInfoBO.getAppName(),
                appListQueryInfoBO.getAppManagers(),
                appListQueryInfoBO.getApplyUsername(),
                appListQueryInfoBO.getChannel(),
                appListQueryInfoBO.getAppSysStatus(),
                appListQueryInfoBO.getBeginDate(), appListQueryInfoBO.getEndDate());
        log.info("appPage请求返回时间 = [{}]", System.currentTimeMillis());
        AppListQueryInfoResponse response = AppListQueryInfoResponse.newBuilder()
                .setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg()).setAppListInfo(appListInfo)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void appStart(AppStartOrStopQueryInfo request,
                         StreamObserver<AppStartOrStopQueryInfoResponse> responseObserver) {
        long id = request.getId();
        String username = request.getUsername();
        String name = request.getName();
        List<String> roleList = request.getRoleList();
        CommonResultBO start = appListService.appStart(id, username, name, roleList);
        setAppStartOrStopResponse(responseObserver, start);
    }

    @Override
    public void appStop(AppStartOrStopQueryInfo request,
                        StreamObserver<AppStartOrStopQueryInfoResponse> responseObserver) {
        long id = request.getId();
        String username = request.getUsername();
        String name = request.getName();
        List<String> roleList = request.getRoleList();
        CommonResultBO stop = appListService.appStop(id, username, name, roleList);
        setAppStartOrStopResponse(responseObserver, stop);
    }

    private void setAppStartOrStopResponse(StreamObserver<AppStartOrStopQueryInfoResponse> responseObserver,
                                           CommonResultBO commonResult) {
        AppStartOrStopQueryInfoResponse response;
        boolean isStartOrStop = commonResult.getIsSuccess();
        Integer code = commonResult.getCode();
        String message = commonResult.getMessage();
        if (isStartOrStop) {
            response = AppStartOrStopQueryInfoResponse.newBuilder()
                    .setCode(ResponseCode.SUCCESS.getCode())
                    .setDesc(ResponseCode.SUCCESS.getMsg()).build();
        } else {
            if (Optional.ofNullable(code).orElse(0).equals(ResponseCode.AUTH_ERROR.getCode())) {
                response = AppStartOrStopQueryInfoResponse.newBuilder()
                        .setCode(ResponseCode.AUTH_ERROR.getCode())
                        .setDesc(message).build();
            } else {
                response = AppStartOrStopQueryInfoResponse.newBuilder()
                        .setCode(ResponseCode.INTERNAL_ERROR.getCode())
                        .setDesc(ResponseCode.INTERNAL_ERROR.getMsg()).build();
            }

        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void appUseApply(AppUseApplyQueryInfo request,
                            StreamObserver<AppUseApplyQueryInfoResponse> responseObserver) {
        long id = request.getId();
        String applyName = request.getApplyName();
        String applyUsername = request.getApplyUsername();
        boolean isSuccess = appListService.appUseApply(id, applyName, applyUsername);
        AppUseApplyQueryInfoResponse response;
        if (isSuccess) {
            response = AppUseApplyQueryInfoResponse.newBuilder()
                    .setCode(ResponseCode.SUCCESS.getCode())
                    .setDesc(ResponseCode.SUCCESS.getMsg()).build();
        } else {
            response = AppUseApplyQueryInfoResponse.newBuilder()
                    .setCode(ResponseCode.INTERNAL_ERROR.getCode())
                    .setDesc(ResponseCode.INTERNAL_ERROR.getMsg()).build();
        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void appManagerSearch(AppUserSearchQueryInfo request,
                                 StreamObserver<AppManagerSearchQueryInfoResponse> responseObserver) {
        String searchWord = request.getSearchWord();
        List<UsernameAndNameBO> manager = appListService.getManager(searchWord);
        List<UsernameAndName> usernameAndNames = MapperUtil.INSTANCE.mapToUsernameAndNameList(manager);
        AppManagerSearchQueryInfoResponse response = AppManagerSearchQueryInfoResponse.newBuilder()
                .setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg())
                .addAllManagers(usernameAndNames).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void appApplyUserSearch(AppUserSearchQueryInfo request,
                                   StreamObserver<AppApplyUserSearchQueryInfoResponse> responseObserver) {
        String searchWord = request.getSearchWord();
        List<UsernameAndNameBO> appApplyUsers = appListService.getAppApplyUsers(searchWord);
        List<UsernameAndName> usernameAndNames = MapperUtil.INSTANCE.mapToUsernameAndNameList(appApplyUsers);
        AppApplyUserSearchQueryInfoResponse response = AppApplyUserSearchQueryInfoResponse.newBuilder()
                .setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg())
                .addAllAppApplyUsers(usernameAndNames).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void appApprovalListQuery(AppApprovalListQueryInfo request,
                                     StreamObserver<AppApprovalListQueryInfoResponse> responseObserver) {
        AppApprovalListQueryInfoBO appApprovalListQueryInfoBO =
                MapperUtil.INSTANCE.mapToAppApprovalListQueryInfoBO(request);
        AppApprovalListInfo appApprovalListInfo = appApprovalListService.getAppApprovalList(
                appApprovalListQueryInfoBO.getUsername(),
                appApprovalListQueryInfoBO.getPage(),
                appApprovalListQueryInfoBO.getSize(),
                appApprovalListQueryInfoBO.getAppName(),
                appApprovalListQueryInfoBO.getAppManagers(),
                appApprovalListQueryInfoBO.getChannel(),
                appApprovalListQueryInfoBO.getBeginDate(),
                appApprovalListQueryInfoBO.getEndDate());
        AppApprovalListQueryInfoResponse response = AppApprovalListQueryInfoResponse.newBuilder()
                .setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg()).setAppApprovalListInfo(appApprovalListInfo)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void appApprovalPass(AppApprovalPassOrRejectQueryInfo request,
                                StreamObserver<AppApprovalPassOrRejectQueryInfoResponse> responseObserver) {
        String username = request.getUsername();
        String name = request.getName();
        long id = request.getId();
        ApprovalResultBO isPass = appApprovalListService.appApprovalPass(username, name, id);
        setAppApprovalPassOrRejectResponse(responseObserver, isPass);
    }

    @Override
    public void appApprovalReject(AppApprovalPassOrRejectQueryInfo request,
                                  StreamObserver<AppApprovalPassOrRejectQueryInfoResponse> responseObserver) {
        String username = request.getUsername();
        String name = request.getName();
        String reason = request.getReason();
        long id = request.getId();
        ApprovalResultBO isReject = appApprovalListService.appApprovalReject(username, name, reason, id);
        setAppApprovalPassOrRejectResponse(responseObserver, isReject);
    }

    @Override
    public void getMessageTemplateDetail(MessageTemplateDetailRequest request,
                                         StreamObserver<MessageTemplateDetailResponse> responseObserver) {
        long messageTemplateId = request.getMessageTemplateId();
        String messageTemplateBizId = request.getMessageTemplateBizId();
        AppSysTemplateInfo appSysTemplateInfo;
        if (messageTemplateId > 0L) {
            appSysTemplateInfo = appSysTemplateService.getMessageTemplateDetail(messageTemplateId);
        } else {
            appSysTemplateInfo = appSysTemplateService.getOne(
                    Wrappers.<AppSysTemplateInfo>lambdaQuery()
                            .eq(AppSysTemplateInfo::getBizId, messageTemplateBizId)
                            .last("limit 1"));
        }
        MessageTemplateDetailResponse.Builder builder = MessageTemplateDetailResponse.newBuilder();
        if (Objects.isNull(appSysTemplateInfo)) {
            builder.setCode(ResponseCode.INTERNAL_ERROR.getCode())
                    .setMessage(ResponseCode.INTERNAL_ERROR.getMsg());
        } else {
            MessageTemplateDetail messageTemplateDetail =
                    MapperUtil.INSTANCE.mapToMessageTemplateDetail(appSysTemplateInfo);
            builder.setCode(ResponseCode.SUCCESS.getCode())
                    .setMessage(ResponseCode.SUCCESS.getMsg())
                    .setMessageTemplateDetail(messageTemplateDetail)
                    .build();
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteMessageTemplate(DeleteMessageTemplateRequest request,
                                      StreamObserver<DeleteMessageTemplateResponse> responseObserver) {
        List<Long> idsList = request.getIdsList();
        String name = request.getName();
        String username = request.getUsername();
        List<String> roleList = request.getRoleList();
        DeleteMessageTemplateResponse.Builder builder = DeleteMessageTemplateResponse.newBuilder();
        CommonResultBO delete = appSysTemplateService.deleteMessageTemplate(idsList, name, username, roleList);
        Integer code = delete.getCode();
        boolean isSuccess = delete.getIsSuccess();
        String message = delete.getMessage();
        if (isSuccess) {
            builder.setCode(ResponseCode.SUCCESS.getCode())
                    .setMessage(ResponseCode.SUCCESS.getMsg())
                    .build();
        } else {
            if (Optional.ofNullable(code).orElse(0).equals(ResponseCode.AUTH_ERROR.getCode())) {
                builder.setCode(ResponseCode.AUTH_ERROR.getCode())
                        .setMessage(message)
                        .build();
            } else {
                builder.setCode(ResponseCode.INTERNAL_ERROR.getCode())
                        .setMessage(ResponseCode.INTERNAL_ERROR.getMsg())

                        .build();
            }
            builder.setCode(ResponseCode.INTERNAL_ERROR.getCode())
                    .setMessage(ResponseCode.INTERNAL_ERROR.getMsg());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getTemplatePage(GetTemplatePageRequest request,
                                StreamObserver<GetTemplatePageResponse> responseObserver) {
        GetTemplatePageRequestBO getTemplatePageRequestBO = MapperUtil.INSTANCE.mapToGetTemplatePageRequestBO(request);
        TemplateListBO templatePageBO = appSysTemplateService.getTemplatePage(getTemplatePageRequestBO);
        TemplatePage.Builder templatePageBuilder = TemplatePage.newBuilder();
        templatePageBuilder.setTotal(templatePageBO.getTotal());
        templatePageBuilder.setPages(templatePageBO.getPages());
        templatePageBuilder.setCurrent(templatePageBO.getCurrent());
        templatePageBuilder.setSize(templatePageBO.getSize());
        templatePageBuilder.setSearchCount(templatePageBO.getSearchCount());

        List<TemplateListRecord> records = templatePageBO.getRecords();
        List<TemplatePageRecord> recordList = new ArrayList<>();
        if (!records.isEmpty()) {
            records.forEach(
                    record -> {
                        TemplatePageRecord.Builder templatePageRecordBuilder = TemplatePageRecord.newBuilder();
                        templatePageRecordBuilder.setBizId(record.getBizId());
                        templatePageRecordBuilder.setBotBizId(record.getBotBizId());
                        templatePageRecordBuilder.setBotAppId(record.getBotAppId());
                        templatePageRecordBuilder.setCreateName(record.getCreateName());
                        templatePageRecordBuilder.setCreateUsername(record.getCreateUsername());
                        templatePageRecordBuilder.setId(record.getId());
                        templatePageRecordBuilder.setCreateTime(record.getCreateTime());
                        templatePageRecordBuilder.setTemplateName(record.getTemplateName());
                        recordList.add(templatePageRecordBuilder.build());
                    }
            );
        }
        templatePageBuilder.addAllRecords(recordList);
        GetTemplatePageResponse.Builder builder = GetTemplatePageResponse.newBuilder();
        builder.setCode(ResponseCode.SUCCESS.getCode())
                .setMessage(ResponseCode.SUCCESS.getMsg())
                .setTemplatePage(templatePageBuilder.build());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void addTemplate(AddTemplateRequest request, StreamObserver<AddTemplateResponse> responseObserver) {
        AddTemplateRequestBO addTemplateRequestBO = MapperUtil.INSTANCE.mapToAddTemplateRequestBO(request);
        AddTemplateResultBO addTemplateResultBO = appSysTemplateService.addTemplate(addTemplateRequestBO);
        AddTemplateResponse.Builder builder = AddTemplateResponse.newBuilder();
        if (addTemplateResultBO.isAdd()) {
            builder.setCode(ResponseCode.SUCCESS.getCode())
                    .setMessage(ResponseCode.SUCCESS.getMsg())
                    .setBizId(addTemplateResultBO.getBizId())
                    .build();
        } else {
            if (ResponseCode.AUTH_ERROR.getCode() == Optional.ofNullable(addTemplateResultBO.getCode()).orElse(0)) {
                builder.setCode(ResponseCode.AUTH_ERROR.getCode())
                        .setMessage("用户无权限在此系统添加模板");
            } else {
                builder.setCode(ResponseCode.INTERNAL_ERROR.getCode())
                        .setMessage(addTemplateResultBO.getMessage());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateTemplate(UpdateTemplateRequest request, StreamObserver<UpdateTemplateResponse> responseObserver) {

        UpdateTemplateRequestBO updateTemplateRequestBO = new UpdateTemplateRequestBO();
        updateTemplateRequestBO.setId(request.getId());
        updateTemplateRequestBO.setBotBizId(request.getBotBizId());
        updateTemplateRequestBO.setBotKey(request.getBotKey());
        updateTemplateRequestBO.setTemplateName(request.getTemplateName());
        updateTemplateRequestBO.setTemplateContent(request.getTemplateContent());
        UpdateTemplateResponse.Builder builder = UpdateTemplateResponse.newBuilder();
        try {
            boolean isUpdate = appSysTemplateService.updateTemplate(updateTemplateRequestBO);

            if (isUpdate) {
                builder.setCode(ResponseCode.SUCCESS.getCode());
                builder.setMessage(ResponseCode.SUCCESS.getMsg());
            } else {
                builder.setCode(ResponseCode.INTERNAL_ERROR.getCode());
                builder.setMessage(ResponseCode.INTERNAL_ERROR.getMsg());
            }
        } catch (Exception e) {
            builder.setCode(ResponseCode.INTERNAL_ERROR.getCode());
            builder.setMessage(e.getMessage());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getSysBotList(GetSysBotListRequest request, StreamObserver<GetSysBotListResponse> responseObserver) {
        long appSysId = request.getAppSysId();
        int channel = request.getChannel();
        List<BotAppIdAndBotBizIdBO> botList = appSysTemplateService.getSysBotList(appSysId, channel);
        List<String> botAppIdList =
                botList.stream().map(BotAppIdAndBotBizIdBO::getBotAppId).collect(Collectors.toList());
        List<String> botBizIdList =
                botList.stream().map(BotAppIdAndBotBizIdBO::getBotBizId).collect(Collectors.toList());
        GetSysBotListResponse.Builder builder = GetSysBotListResponse.newBuilder();
        builder.setCode(ResponseCode.SUCCESS.getCode()).setMessage(ResponseCode.SUCCESS.getMsg())
                .addAllBotAppId(botAppIdList).addAllBotBizId(botBizIdList);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void appApplyCancel(AppApplyCancelQueryInfo request,
                               StreamObserver<AppApplyCancelQueryInfoResponse> responseObserver) {
        long id = request.getId();
        String name = request.getName();
        String username = request.getUsername();
        List<String> roleList = request.getRoleList();
        CommonResultBO cancel = appApprovalListService.appApplyCancel(username, name, id, roleList);
        Integer code = cancel.getCode();
        boolean isSuccess = cancel.getIsSuccess();
        String message = cancel.getMessage();
        AppApplyCancelQueryInfoResponse response;
        if (isSuccess) {
            response = AppApplyCancelQueryInfoResponse.newBuilder()
                    .setCode(ResponseCode.SUCCESS.getCode())
                    .setDesc(ResponseCode.SUCCESS.getMsg()).build();
        } else {
            if (Optional.ofNullable(code).orElse(0).equals(ResponseCode.AUTH_ERROR.getCode())) {
                response = AppApplyCancelQueryInfoResponse.newBuilder()
                        .setCode(ResponseCode.AUTH_ERROR.getCode())
                        .setDesc(message).build();
            } else {
                response = AppApplyCancelQueryInfoResponse.newBuilder()
                        .setCode(ResponseCode.INTERNAL_ERROR.getCode())
                        .setDesc(ResponseCode.INTERNAL_ERROR.getMsg()).build();
            }
        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    private void setAppApprovalPassOrRejectResponse(
            StreamObserver<AppApprovalPassOrRejectQueryInfoResponse> responseObserver,
            ApprovalResultBO isPassOrReject) {
        AppApprovalPassOrRejectQueryInfoResponse response;
        if (isPassOrReject.isApprovalResult()) {
            response = AppApprovalPassOrRejectQueryInfoResponse.newBuilder()
                    .setCode(ResponseCode.SUCCESS.getCode())
                    .setDesc(ResponseCode.SUCCESS.getMsg()).build();
        } else {
            response = AppApprovalPassOrRejectQueryInfoResponse.newBuilder()
                    .setCode(ResponseCode.INTERNAL_ERROR.getCode())
                    .setDesc(isPassOrReject.getResultMessage()).build();
        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void getSysSummaryList(Empty request, StreamObserver<SysSummaryResponse> responseObserver) {

        List<AppSummaryBO> appSummaryBOList = appListService.getSysSummary();
        List<AppSummary> appSummaryList = MapperUtil.INSTANCE.mapToAppSummaryList(appSummaryBOList);

        SysSummaryResponse.Builder builder = SysSummaryResponse.newBuilder();
        builder.setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg())
                .addAllAppSummary(appSummaryList);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    @Override
    public void getMessageRulePage(com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetMessageRulePageRequest request,
                                   io.grpc.stub.StreamObserver<com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetMessageRulePageResponse> responseObserver) {
        GetMsgRulePageRequestBO getMsgRulePageRequestBO = MapperUtil.INSTANCE.mapToGetMsgRulePageRequestBO(request);
        AppMsgRuleListBO appMsgRuleListBO = appMsgRuleService.messageRulePage(getMsgRulePageRequestBO);

        MessageRulePage.Builder templatePageBuilder = MessageRulePage.newBuilder();
        templatePageBuilder.setTotal(appMsgRuleListBO.getTotal());
        templatePageBuilder.setPages(appMsgRuleListBO.getPages());
        templatePageBuilder.setCurrent(appMsgRuleListBO.getCurrent());
        templatePageBuilder.setSize(appMsgRuleListBO.getSize());
        templatePageBuilder.setSearchCount(appMsgRuleListBO.getSearchCount());

        List<AppMsgRuleListRecord> records = appMsgRuleListBO.getRecords();
        List<MessageRulePageRecord> recordList = new ArrayList<>();
        if (!records.isEmpty()) {
            records.forEach(
                    record -> {
                        MessageRulePageRecord.Builder builder = MessageRulePageRecord.newBuilder();
                        builder.setId(record.getId());
                        builder.setMessageRuleName(record.getMessageRuleName());
                        builder.setCallback(record.getCallback());
                        builder.setAppSysId(record.getAppSysId());
                        builder.setPlaceholder(record.getPlaceholder());
                        builder.setCreateName(record.getCreateName());
                        builder.setCreateUsername(record.getCreateUsername());
                        builder.setCreateTime(record.getCreateTime());
                        recordList.add(builder.build());
                    }
            );
        }
        templatePageBuilder.addAllRecords(recordList);
        GetMessageRulePageResponse.Builder builder = GetMessageRulePageResponse.newBuilder();
        builder.setCode(ResponseCode.SUCCESS.getCode())
                .setMessage(ResponseCode.SUCCESS.getMsg())
                .setMessageRulePage(templatePageBuilder.build());

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void addMessageRule(AddMessageRuleRequest request, StreamObserver<AddMessageRuleResponse> responseObserver) {
        AddMsgRuleInfoRequestBO addMsgRuleInfoRequestBO = MapperUtil.INSTANCE.mapToAddMsgRuleRequestBO(request);
        AddMsgRuleInfoResultBO addMsgRuleInfoResultBO = appMsgRuleService.addMessageRule(addMsgRuleInfoRequestBO);
        AddMessageRuleResponse.Builder builder = AddMessageRuleResponse.newBuilder();
        if (addMsgRuleInfoResultBO.isAdd()) {
            builder.setCode(ResponseCode.SUCCESS.getCode())
                    .setMessage(ResponseCode.SUCCESS.getMsg())
                    .setRuleId(addMsgRuleInfoResultBO.getId())
                    .build();
        } else {
            if (ResponseCode.AUTH_ERROR.getCode() == Optional.ofNullable(addMsgRuleInfoResultBO.getCode()).orElse(0)) {
                builder.setCode(ResponseCode.AUTH_ERROR.getCode())
                        .setMessage("用户无权限在此系统添加规则");
            } else {
                builder.setCode(ResponseCode.INTERNAL_ERROR.getCode())
                        .setMessage(addMsgRuleInfoResultBO.getMessage());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getMessageRuleDetail(com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetMessageRuleDetailRequest request,
                                     io.grpc.stub.StreamObserver<com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetMessageRuleDetailResponse> responseObserver) {
        long ruleId = request.getMessageRuleId();
        AppMsgRuleInfo appMsgRuleInfo = appMsgRuleService.getMessageRuleDetail(ruleId);

        GetMessageRuleDetailResponse.Builder builder = GetMessageRuleDetailResponse.newBuilder();

        if (Objects.isNull(appMsgRuleInfo)) {
            builder.setCode(ResponseCode.INTERNAL_ERROR.getCode())
                    .setMessage(ResponseCode.INTERNAL_ERROR.getMsg());
        } else {
            MessageRuleDetail messageRuleDetail = MapperUtil.INSTANCE.mapToMessageRuleDetail(appMsgRuleInfo);
            builder.setCode(ResponseCode.SUCCESS.getCode())
                    .setMessage(ResponseCode.SUCCESS.getMsg())
                    .setMessageRuleDetail(messageRuleDetail)
                    .build();
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateMessageRule(com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateMessageRuleRequest request,
                                  io.grpc.stub.StreamObserver<com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateMessageRuleResponse> responseObserver) {
        UpdateMessageRuleResponse.Builder builder = UpdateMessageRuleResponse.newBuilder();
        UpdateMsgRuleRequestBO updateMsgRuleRequestBO = new UpdateMsgRuleRequestBO();
        updateMsgRuleRequestBO.setId(request.getId());
        updateMsgRuleRequestBO.setRuleName(request.getRuleName());
        updateMsgRuleRequestBO.setCallback(request.getCallback());
        updateMsgRuleRequestBO.setPlaceholder(request.getPlaceholder());
        updateMsgRuleRequestBO.setUsername(request.getUsername());
        ImmutablePair<ResponseCode, String> pair = appMsgRuleService.updateMessageRule(updateMsgRuleRequestBO);
        if (ResponseCode.SUCCESS.equals(pair.getLeft())) {
            builder.setCode(ResponseCode.SUCCESS.getCode())
                    .setMessage(ResponseCode.SUCCESS.getMsg())
                    .setRuleId(request.getId());
        } else {
            builder.setCode(ResponseCode.INTERNAL_ERROR.getCode());
            builder.setMessage(pair.getRight());
            builder.setRuleId(request.getId());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteMessageRule(com.xiaomi.info.pb.app.mioffice.ums.open.v1.DeleteMessageRuleRequest request,
                                  io.grpc.stub.StreamObserver<com.xiaomi.info.pb.app.mioffice.ums.open.v1.DeleteMessageRuleResponse> responseObserver) {
        List<Long> idsList = request.getIdsList();
        String name = request.getName();
        String username = request.getUsername();
        List<String> roleList = request.getRoleList();
        DeleteMessageRuleResponse.Builder builder = DeleteMessageRuleResponse.newBuilder();

        CommonResultBO delete = appMsgRuleService.deleteMessageRule(idsList, name, username, roleList);
        Integer code = delete.getCode();
        boolean isSuccess = delete.getIsSuccess();
        String message = delete.getMessage();
        if (isSuccess) {
            builder.setCode(ResponseCode.SUCCESS.getCode())
                    .setMessage(ResponseCode.SUCCESS.getMsg())
                    .build();
        } else {
            if (Optional.ofNullable(code).orElse(0).equals(ResponseCode.AUTH_ERROR.getCode())) {
                builder.setCode(ResponseCode.AUTH_ERROR.getCode())
                        .setMessage(message)
                        .build();
            } else {
                builder.setCode(ResponseCode.INTERNAL_ERROR.getCode())
                        .setMessage(ResponseCode.INTERNAL_ERROR.getMsg())

                        .build();
            }
            builder.setCode(ResponseCode.INTERNAL_ERROR.getCode())
                    .setMessage(ResponseCode.INTERNAL_ERROR.getMsg());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAvailableRobotTopicList(QueryRobotAppTopicRequest request,
                                           StreamObserver<QueryRobotAppTopicResponse> responseObserver) {

        RobotTopicListBO robotTopicListBO = appTopicInfoService.getAvailableRobotTopicList(request.getRobotId());

        RobotAppTopicInfo robotAppTopicInfo = MapperUtil.INSTANCE.map2RobotTopicListBO(robotTopicListBO);

        QueryRobotAppTopicResponse.Builder builder = QueryRobotAppTopicResponse.newBuilder();
        builder.setCode(ResponseCode.SUCCESS.getCode()).setDesc(ResponseCode.SUCCESS.getMsg())
                .setRobotAppTopicInfo(robotAppTopicInfo);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    @Override
    public void createAppTopic(CreateAppTopicRequest request, StreamObserver<CreateAppTopicResponse> responseObserver) {
        int code = ResponseCode.SUCCESS.getCode();
        String msg = StringUtils.EMPTY;
        try {
            appTopicInfoService.createAppTopic(request.getName(),
                    request.getAppId(),
                    request.getCreateBy(),
                    request.getUntilLevel2Leader(),
                    request.getItOperators(),
                    request.getDesc());
        } catch (BizException ex) {
            code = ex.getCode().getCode();
            msg = ex.getMessage();
        } catch (Throwable ex) {
            code = ResponseCode.INTERNAL_ERROR.getCode();
            msg = ex.getMessage();
        } finally {
            CreateAppTopicResponse.Builder builder = CreateAppTopicResponse.newBuilder();
            builder.setCode(code)
                    .setDesc(msg);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void cancelAppTopic(CancelAppTopicRequest request, StreamObserver<CancelAppTopicResponse> responseObserver) {
        int code = ResponseCode.SUCCESS.getCode();
        String msg = StringUtils.EMPTY;
        try {
            appTopicInfoService.cancelAppTopic(request.getAppId(), request.getUpdateBy());
        } catch (BizException ex) {
            code = ex.getCode().getCode();
            msg = ex.getMessage();
        } catch (Throwable ex) {
            code = ResponseCode.INTERNAL_ERROR.getCode();
            msg = ex.getMessage();
        } finally {
            CancelAppTopicResponse.Builder builder = CancelAppTopicResponse.newBuilder();
            builder.setCode(code)
                    .setDesc(msg);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getAppTopic(QueryAppTopicRequest request, StreamObserver<QueryAppTopicResponse> responseObserver) {
        int code = ResponseCode.SUCCESS.getCode();
        String msg = StringUtils.EMPTY;
        com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppTopicInfo appTopicInfo = null;
        try {
            AppTopicInfo appTopicInfoLocal = appTopicInfoService.getAppTopic(request.getAppId());
            appTopicInfo = MapperUtil.INSTANCE.map2AppTopicInfo(appTopicInfoLocal);
        } catch (BizException ex) {
            code = ex.getCode().getCode();
            msg = ex.getMessage();
        } catch (Throwable ex) {
            code = ResponseCode.INTERNAL_ERROR.getCode();
            msg = ex.getMessage();
        } finally {
            QueryAppTopicResponse.Builder builder = QueryAppTopicResponse.newBuilder();
            builder.setCode(code)
                    .setDesc(msg);
            if (Objects.nonNull(appTopicInfo)) {
                builder.setAppTopicInfo(appTopicInfo);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }
}
