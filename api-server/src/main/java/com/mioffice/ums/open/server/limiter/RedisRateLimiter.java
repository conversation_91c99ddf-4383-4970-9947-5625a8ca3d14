package com.mioffice.ums.open.server.limiter;

import com.mioffice.ums.open.common.constats.RedisConstants;
import com.mioffice.ums.open.common.exception.LimitException;
import com.mioffice.ums.open.server.entity.bo.RateConfigBO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * <AUTHOR>
 * @since 2020/8/27
 */
@Slf4j
public class RedisRateLimiter implements DistributedRateLimiter {

    private final RedissonClient redissonClient;
    private final List<Object> keys = new ArrayList<>(16);
    private final String rateLimitKey;
    private final String script;
    private final String rateConfField;
    private final String rateMsConfField;
    private final RateConfigBO rateConfigBO;

    public RedisRateLimiter(RedissonClient redissonClient, String rateLimitKey, String script, RateConfigBO rateConfigBO) {
        this.redissonClient = redissonClient;
        this.rateLimitKey = rateLimitKey;
        this.script = script;
        this.rateConfField = String.format("%s-%s", rateLimitKey, RedisConstants.RATE_LIMIT_FIELD);
        this.rateMsConfField = String.format("%s-%s", rateLimitKey, RedisConstants.RATE_MS_FIELD);
        this.rateConfigBO = rateConfigBO;
        start();
    }

    @Override
    public void acquire(long timeout) throws LimitException {
        if (timeout < 0) {
            timeout = 0;
        }
        long ms = redissonClient.getScript(StringCodec.INSTANCE).eval(RScript.Mode.READ_WRITE, script,
                RScript.ReturnType.INTEGER, keys,
                System.currentTimeMillis(), getDefault(), timeout);
        if (timeout > 0 && ms > timeout) {
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(timeout));
            throw new LimitException(String.format("timeout: %d target: %d", timeout, ms));
        }
        if (ms > 0) {
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(ms));
        }
        log.debug("等待令牌桶的时间: {} ms, rateLimitKey: {}", ms, rateLimitKey);
    }

    /**
     * interval
     *
     * @return
     */
    public int getDefault() {
        return RedisConstants.RATE_MS / RedisConstants.DEFAULT_RATE;
    }

    @Override
    public void start() {
        keys.add(RedisConstants.RATE_LIMIT_KEY);
        keys.add(rateLimitKey);
        keys.add(rateConfField);
        keys.add(rateMsConfField);

        RMap<String, String> map = redissonClient.getMap(RedisConstants.RATE_LIMIT_KEY, StringCodec.INSTANCE);
        if (!map.containsKey(RedisConstants.RATE_LIMIT_FIELD)) {
            if (Objects.nonNull(rateConfigBO) && rateConfigBO.getRate() > 0) {
                this.setRate(rateConfigBO);
            } else {
                map.put(rateConfField, String.valueOf(RedisConstants.DEFAULT_RATE));
            }

        }
    }

    @Override
    public void setRate(RateConfigBO rateConf) {
        if (rateConf == null) {
            return;
        }
        RMap<String, String> map = redissonClient.getMap(RedisConstants.RATE_LIMIT_KEY, StringCodec.INSTANCE);
        if (rateConf.getRate() > 0) {
            map.put(rateConfField, String.valueOf(rateConf.getRate()));
        }
        if (rateConf.getRateMs() > 0) {
            map.put(rateMsConfField, String.valueOf(rateConf.getRateMs()));
        }
    }
}
