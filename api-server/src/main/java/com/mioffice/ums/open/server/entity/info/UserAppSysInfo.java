package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.16
 */

/**
 * 应用用户表
 */
@Data
@TableName(value = "user_app_sys_info")
public class UserAppSysInfo extends BaseEntity {

    /**
     * app_sys_info 外键id
     */
    @TableField(value = "app_sys_id")
    private Long appSysId;

    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 用户username
     */
    @TableField(value = "username")
    private String username;

    /**
     * 用户name
     */
    @TableField(value = "name")
    private String name;

    /**
     * 用户类型(1：负责人)
     */
    @TableField(value = "user_type")
    private Byte userType;

}