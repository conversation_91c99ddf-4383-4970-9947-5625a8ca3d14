package com.mioffice.ums.open.server.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.server.entity.bo.AppApprovalListRecordInfoBO;
import com.mioffice.ums.open.server.entity.bo.ApprovalResultBO;
import com.mioffice.ums.open.server.entity.bo.CommonResultBO;
import com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.mioffice.ums.open.server.enums.AppRecordValidStatusEnum;
import com.mioffice.ums.open.server.enums.AppSysStatusEnum;
import com.mioffice.ums.open.server.enums.AppUserRoleEnum;
import com.mioffice.ums.open.server.mapper.AppSysApplyRecordInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.mapper.UserAppSysInfoMapper;
import com.mioffice.ums.open.server.service.AppApprovalListService;
import com.mioffice.ums.open.server.utils.MapperUtil;
import com.mioffice.ums.open.server.utils.TypeConvertUtil;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalListInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalListRecordInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/21 3:59 上午
 * version: 1.0.0
 */
@Slf4j
@Service
public class AppApprovalListServiceImpl implements AppApprovalListService {

    private final AppSysInfoMapper appSysInfoMapper;

    private final AppSysApplyRecordInfoMapper appSysApplyRecordInfoMapper;

    private final UserAppSysInfoMapper userAppSysInfoMapper;

    public AppApprovalListServiceImpl(AppSysInfoMapper appSysInfoMapper, AppSysApplyRecordInfoMapper appSysApplyRecordInfoMapper, UserAppSysInfoMapper userAppSysInfoMapper) {
        this.appSysInfoMapper = appSysInfoMapper;
        this.appSysApplyRecordInfoMapper = appSysApplyRecordInfoMapper;
        this.userAppSysInfoMapper = userAppSysInfoMapper;
    }

    @Override
    public AppApprovalListInfo getAppApprovalList(String username, Long page, Long size, String appName, String managerUsernameList, String channel, String beginDate, String endDate) {
        Page<AppApprovalListRecordInfoBO> dataPage = new Page<>(page, size);
        AppApprovalListInfo.Builder appApprovalListInfoBuilder = AppApprovalListInfo.newBuilder();
        List<String> managerList = new ArrayList<>();
        if (StringUtils.isNotBlank(managerUsernameList)) {
            managerList = Arrays.asList(managerUsernameList.split(","));
        }
        List<Byte> channelList = new ArrayList<>();
        if (StringUtils.isNotBlank(channel)) {
            channelList = TypeConvertUtil.stringToByteList(channel);
        }
        long beginTime = 0;
        if (StringUtils.isNotBlank(beginDate)) {
            beginTime = DateUtil.beginOfDay(DateUtil.parseDateTime(beginDate)).getTime();
        }
        long endTime = 0;
        if (StringUtils.isNotBlank(endDate)) {
            endTime = DateUtil.endOfDay(DateUtil.parseDateTime(endDate)).getTime();
        }

        List<AppApprovalListRecordInfo> appApprovalListRecordInfoList = new ArrayList<>();
        List<AppApprovalListRecordInfoBO> appApprovalListRecordInfoBOS = appSysInfoMapper.selectAppApprovalListPage(dataPage, appName, managerList, channelList, beginTime, endTime);
        appApprovalListInfoBuilder.setCurrent(page);
        appApprovalListInfoBuilder.setSize(size);
        if (appApprovalListRecordInfoBOS.isEmpty()) {
            appApprovalListInfoBuilder.setPages(0L);
            appApprovalListInfoBuilder.setTotal(0L);
            appApprovalListInfoBuilder.setSearchCount(true);
            appApprovalListInfoBuilder.addAllRecords(Collections.emptyList());
            return appApprovalListInfoBuilder.build();
        }
        appApprovalListRecordInfoBOS.forEach(
                appApprovalListRecordInfoBO -> {
                    AppApprovalListRecordInfo.Builder appApprovalListRecordInfoBuilder = AppApprovalListRecordInfo.newBuilder();
                    appApprovalListRecordInfoBuilder
                            .setId(appApprovalListRecordInfoBO.getId())
                            .setAppId(appApprovalListRecordInfoBO.getAppId())
                            .setAppName(appApprovalListRecordInfoBO.getAppName())
                            .setCreateTime(appApprovalListRecordInfoBO.getCreateTime())
                            .addAllIsOutSend(appApprovalListRecordInfoBO.getIsOutSend())
                            .setCreateUser(MapperUtil.INSTANCE.mapToUsernameAndName(appApprovalListRecordInfoBO.getCreateUser()))
                            .addAllChannel(appApprovalListRecordInfoBO.getChannel())
                            .addAllManager(MapperUtil.INSTANCE.mapToUsernameAndNameList(appApprovalListRecordInfoBO.getManager()));
                    AppApprovalListRecordInfo appApprovalListRecordInfo = appApprovalListRecordInfoBuilder.build();
                    appApprovalListRecordInfoList.add(appApprovalListRecordInfo);
                }
        );
        appApprovalListInfoBuilder.setPages(dataPage.getPages());
        appApprovalListInfoBuilder.setTotal(dataPage.getTotal());
        appApprovalListInfoBuilder.setSearchCount(true);
        appApprovalListInfoBuilder.addAllRecords(appApprovalListRecordInfoList);
        return appApprovalListInfoBuilder.build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResultBO appApprovalPass(String username, String name, Long id) {
        ApprovalResultBO approvalResultBO = new ApprovalResultBO();
        AppSysApplyRecordInfo appSysApplyRecordInfo = appSysApplyRecordInfoMapper.selectOne(
                Wrappers.<AppSysApplyRecordInfo>lambdaQuery()
                        .eq(AppSysApplyRecordInfo::getAppSysId, id)
                        .eq(AppSysApplyRecordInfo::getValid, AppRecordValidStatusEnum.VALID_STATUS_ENUM.getCode())
        );
        if (Objects.isNull(appSysApplyRecordInfo)) {
            AppSysInfo appSysInfo = appSysInfoMapper.selectById(id);
            if (appSysInfo.getAppSysStatus() != AppSysStatusEnum.APPROVING.getCode()) {
                approvalResultBO.setApprovalResult(false);
                approvalResultBO.setResultMessage("该审核已经被处理, 请刷新待审核列表页");
            } else {
                approvalResultBO.setApprovalResult(false);
                approvalResultBO.setResultMessage("该审核不存在");
            }
            return approvalResultBO;
        }
        if (StringUtils.isNotBlank(appSysApplyRecordInfo.getApprovalUsername())) {
            approvalResultBO.setApprovalResult(false);
            approvalResultBO.setResultMessage("已经被审核");
            return approvalResultBO;
        }
        AppSysInfo appSysInfo = AppSysInfo.newUpdateTimeInstant();
        appSysInfo.setAppSysStatus(AppSysStatusEnum.APPROVED.getCode());
        appSysInfo.setId(id);
        appSysInfoMapper.updateById(appSysInfo);
        AppSysApplyRecordInfo appSysApplyRecordInfoUpdate = AppSysApplyRecordInfo.newUpdateTimeInstant();
        appSysApplyRecordInfoUpdate.setId(appSysApplyRecordInfo.getId());
        appSysApplyRecordInfoUpdate.setApplyStatus(AppSysStatusEnum.APPROVED.getCode());
        appSysApplyRecordInfoUpdate.setApprovalUsername(username);
        appSysApplyRecordInfoUpdate.setApprovalName(name);
        appSysApplyRecordInfoUpdate.setValid(AppRecordValidStatusEnum.UN_VALID_STATUS_ENUM.getCode());
        appSysApplyRecordInfoMapper.updateById(appSysApplyRecordInfoUpdate);

        approvalResultBO.setApprovalResult(true);
        approvalResultBO.setResultMessage("审核通过");
        return approvalResultBO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResultBO appApprovalReject(String username, String name, String reason, Long id) {
        ApprovalResultBO approvalResultBO = new ApprovalResultBO();
        AppSysApplyRecordInfo appSysApplyRecordInfo = appSysApplyRecordInfoMapper.selectOne(
                Wrappers.<AppSysApplyRecordInfo>lambdaQuery()
                        .eq(AppSysApplyRecordInfo::getAppSysId, id)
                        .eq(AppSysApplyRecordInfo::getValid, AppRecordValidStatusEnum.VALID_STATUS_ENUM.getCode())
        );
        if (Objects.isNull(appSysApplyRecordInfo)) {
            AppSysInfo appSysInfo = appSysInfoMapper.selectById(id);
            if (appSysInfo.getAppSysStatus() != AppSysStatusEnum.APPROVING.getCode()) {
                approvalResultBO.setApprovalResult(false);
                approvalResultBO.setResultMessage("该审核已经被处理, 请刷新待审核列表页");
            } else {
                approvalResultBO.setApprovalResult(false);
                approvalResultBO.setResultMessage("该审核不存在");
            }
            return approvalResultBO;
        }
        if (StringUtils.isNotBlank(appSysApplyRecordInfo.getApprovalUsername())) {
            approvalResultBO.setApprovalResult(false);
            approvalResultBO.setResultMessage("已经被审核");
            return approvalResultBO;
        }
        AppSysInfo appSysInfo = AppSysInfo.newUpdateTimeInstant();
        appSysInfo.setId(id);
        appSysInfo.setUpdateName(name);
        appSysInfo.setUpdateUsername(username);
        appSysInfo.setAppSysStatus(AppSysStatusEnum.APPROVE_BACK.getCode());
        appSysInfoMapper.updateById(appSysInfo);
        AppSysApplyRecordInfo appSysApplyRecordInfoUpdate = AppSysApplyRecordInfo.newUpdateTimeInstant();
        appSysApplyRecordInfoUpdate.setId(appSysApplyRecordInfo.getId());
        appSysApplyRecordInfoUpdate.setApplyStatus(AppSysStatusEnum.APPROVE_BACK.getCode());
        appSysApplyRecordInfoUpdate.setApprovalUsername(username);
        appSysApplyRecordInfoUpdate.setApprovalName(name);
        appSysApplyRecordInfoUpdate.setApprovalDesc(reason);
        appSysApplyRecordInfoUpdate.setValid(AppRecordValidStatusEnum.UN_VALID_STATUS_ENUM.getCode());
        appSysApplyRecordInfoMapper.updateById(appSysApplyRecordInfoUpdate);
        approvalResultBO.setApprovalResult(true);
        approvalResultBO.setResultMessage("审核拒绝");
        return approvalResultBO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResultBO appApplyCancel(String username, String name, Long id, List<String> roleList) {
        CommonResultBO commonResultBO = new CommonResultBO();
        AppSysInfo appSysInfo = appSysInfoMapper.selectById(id);
        if (notHaveAccess(id, username, roleList, commonResultBO)) return commonResultBO;
        appSysInfo.setId(id);
        appSysInfo.setUpdateName(name);
        appSysInfo.setUpdateUsername(username);
        appSysInfo.setAppSysStatus(AppSysStatusEnum.CANCEL.getCode());
        appSysInfoMapper.updateById(appSysInfo);
        AppSysApplyRecordInfo appSysApplyRecordInfo = appSysApplyRecordInfoMapper.selectOne(
                Wrappers.<AppSysApplyRecordInfo>lambdaQuery().eq(AppSysApplyRecordInfo::getAppSysId, id).eq(AppSysApplyRecordInfo::getValid, AppRecordValidStatusEnum.VALID_STATUS_ENUM.getCode())
        );
        AppSysApplyRecordInfo appSysApplyRecordInfoUpdate = AppSysApplyRecordInfo.newUpdateTimeInstant();
        appSysApplyRecordInfoUpdate.setId(appSysApplyRecordInfo.getId());
        appSysApplyRecordInfoUpdate.setApplyStatus(AppSysStatusEnum.CANCEL.getCode());
        appSysApplyRecordInfoUpdate.setApplyUserName(username);
        appSysApplyRecordInfoUpdate.setApprovalName(name);
        appSysApplyRecordInfoUpdate.setValid(AppRecordValidStatusEnum.UN_VALID_STATUS_ENUM.getCode());
        appSysApplyRecordInfoMapper.updateById(appSysApplyRecordInfoUpdate);
        commonResultBO.setIsSuccess(true);
        return commonResultBO;
    }

    private boolean notHaveAccess(Long id, String username, List<String> roleList, CommonResultBO commonResultBO) {
        if (!roleList.contains("ROLE_SYS_SUPER_ADMIN") && !roleList.contains("ROLE_SYS_ADMIN")) {
            List<UserAppSysInfo> userAppSysInfoList = userAppSysInfoMapper.selectList(
                    Wrappers.<UserAppSysInfo>lambdaQuery()
                            .eq(UserAppSysInfo::getAppSysId, id)
                            .eq(UserAppSysInfo::getUserType, AppUserRoleEnum.APP_USER_ROLE_CHARGE.getCode())
            );
            boolean isHaveAccess = false;
            for (UserAppSysInfo userAppSysInfo:userAppSysInfoList) {
                if (username.equals(userAppSysInfo.getUsername())) {
                    isHaveAccess = true;
                    break;
                }
            }
            if (!isHaveAccess) {
                commonResultBO.setIsSuccess(false);
                commonResultBO.setCode(ResponseCode.AUTH_ERROR.getCode());
                commonResultBO.setMessage("用户没有权限访问此接口");
                return true;
            }
        }
        return false;
    }
}
