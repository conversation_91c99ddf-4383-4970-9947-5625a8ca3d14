package com.mioffice.ums.open.server.service.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import static com.mi.oa.infra.oaucf.bpm.enums.ProcessInstanceStatus.RUNNING;
import com.mi.oa.infra.oaucf.bpm.rep.CreateProcInstResp;
import com.mi.oa.infra.oaucf.bpm.rep.QueryProcInstResp;
import com.mi.oa.infra.oaucf.bpm.req.CreateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.req.TerminateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.service.ProcessInstanceService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.exception.BizException;
import com.mioffice.ums.open.server.entity.bo.RobotTopicListBO;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.entity.info.AppTopicInfo;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.mioffice.ums.open.server.enums.AppSysStatusEnum;
import com.mioffice.ums.open.server.enums.AppTopicStatusEnum;
import com.mioffice.ums.open.server.mapper.AppTopicInfoMapper;
import com.mioffice.ums.open.server.service.AppInfoService;
import com.mioffice.ums.open.server.service.AppTopicInfoService;
import com.mioffice.ums.open.server.service.UserAppSysInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【app_topic_info(系统队列表)】的数据库操作Service实现
 * @createDate 2024-01-29 20:18:19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AppTopicInfoServiceImpl extends ServiceImpl<AppTopicInfoMapper, AppTopicInfo>
        implements AppTopicInfoService {

    @NacosValue(value = "${ums.open.topic-apply-bpmn-id:}", autoRefreshed = true)
    private String topicApplyBpmnId;

    @NacosValue(value = "${ums.admin.topic-maker:}", autoRefreshed = true)
    private String topicMaker;

    @Autowired
    private AppTopicInfoMapper appTopicInfoMapper;

    @Autowired
    private AppInfoService appInfoService;

    @Autowired
    private UserAppSysInfoService userAppSysInfoService;

    @Resource
    private ProcessInstanceService processInstanceService;

    @Override
    public RobotTopicListBO getAvailableRobotTopicList(String robotId) {
        RobotTopicListBO robotTopicListBO = new RobotTopicListBO();
        robotTopicListBO.setRobotId(robotId);
        List<AppTopicInfo> appTopicInfoList = appTopicInfoMapper.getAvailableByRobotId(robotId);
        robotTopicListBO.setAppTopicInfoList(appTopicInfoList);
        return robotTopicListBO;
    }

    @Override
    public Boolean createAppTopic(String topic,
                                  String appId,
                                  String createBy,
                                  String untilLevel2Leader,
                                  String itOperators,
                                  String desc) {

        AppSysInfo appSysInfo = appInfoService.getOne(Wrappers.<AppSysInfo>lambdaQuery().eq(AppSysInfo::getAppId,
                appId).eq(AppSysInfo::getAppSysStatus, AppSysStatusEnum.APPROVED.getCode()).last(
                "LIMIT 1"));

        if (Objects.isNull(appSysInfo)) {
            throw new BizException(ResponseCode.APP_NOT_EXISTS);
        }

        int sameTopicCount = count(Wrappers.<AppTopicInfo>lambdaQuery().eq(AppTopicInfo::getTopic,topic).in(AppTopicInfo::getStatus,AppTopicStatusEnum.UNDER_REVIEW.getCode(),AppTopicStatusEnum.READY.getCode()));
        if(sameTopicCount>0){
            throw new BizException(ResponseCode.APP_TOPIC_DUPLICATE);
        }

        AppTopicInfo appTopicInfo = getAppTopic(appId);

        if (Objects.nonNull(appTopicInfo)) {
            if (appTopicInfo.getStatus() == AppTopicStatusEnum.READY.getCode()) {
                throw new BizException(ResponseCode.APP_HAS_TOPIC);
            }

            if (appTopicInfo.getStatus() == AppTopicStatusEnum.UNDER_REVIEW.getCode()) {
                throw new BizException(ResponseCode.APP_TOPIC_UNDER_REVIEW);
            }
            appTopicInfo.setUpdateTime(System.currentTimeMillis());
        } else {
            appTopicInfo = AppTopicInfo.newCreateAndUpdateTimeInstant();
        }

        List<String> sysManagerUsernameList =
                userAppSysInfoService.list(Wrappers.<UserAppSysInfo>lambdaQuery().eq(UserAppSysInfo::getAppId, appId))
                        .stream()
                        .map(UserAppSysInfo::getUsername)
                        .distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sysManagerUsernameList)) {
            throw new BizException(ResponseCode.APP_NO_MANAGER);
        }

        String sysManager = String.join(",", sysManagerUsernameList);

        appTopicInfo.setTopic(topic);
        appTopicInfo.setAppId(appId);
        appTopicInfo.setCreateBy(createBy);
        appTopicInfo.setUpdateBy(createBy);
        appTopicInfo.setStatus(AppTopicStatusEnum.UNDER_REVIEW.getCode());
        appTopicInfo.setBpmInstanceId(UUID.randomUUID().toString().replace("-", ""));
        saveOrUpdate(appTopicInfo);

        // 流程变量
        Map<String, Object> variables = new LinkedHashMap<>();
        variables.put("level2_leader", untilLevel2Leader);
        variables.put("it_operators", itOperators);
        variables.put("sys_manager", sysManager);
        variables.put("topic_maker", topicMaker);

        // 表单变量
        Map<String, Object> formData = new LinkedHashMap<>();
        formData.put("input_6a1dc11c9c02", appSysInfo.getAppId());
        formData.put("input_febfc657af18", appSysInfo.getAppName());
        formData.put("input_2611e107f85a", topic);
        formData.put("textarea_4b9f955ebd5e", desc);

        // 流程发起
        CreateProcInstReq createProcInstReq = CreateProcInstReq.builder()
                .modelCode(topicApplyBpmnId)
                .processInstanceName("ums专用队列审批")
                .variables(variables)
                .businessKey(appTopicInfo.getBpmInstanceId())
                .startUserId(createBy)
                .formData(formData)
                .build();

        BaseResp<CreateProcInstResp> resp = processInstanceService.create(createProcInstReq);

        return resp.getCode() == 0;
    }

    @Override
    public Boolean cancelAppTopic(String appId, String cancelBy) {
        AppTopicInfo appTopicInfo = getAppTopic(appId);
        if (Objects.isNull(appTopicInfo)) {
            throw new BizException(ResponseCode.APP_NO_TOPIC);
        }
        appTopicInfo.setUpdateBy(cancelBy);
        appTopicInfo.setUpdateTime(System.currentTimeMillis());
        appTopicInfo.setStatus(AppTopicStatusEnum.CANCEL.getCode());

        // 如果审批中，则终止流程
        BaseResp<QueryProcInstResp> resp = processInstanceService.get(appTopicInfo.getBpmInstanceId(), null);
        if (resp.getCode() == 0 && Objects.nonNull(resp.getData()) &&
                resp.getData().getProcessInstanceStatus() == RUNNING) {
            TerminateProcInstReq terminateProcInstReq = TerminateProcInstReq.builder()
                    .businessKey(appTopicInfo.getBpmInstanceId())
                    .operator(cancelBy)
                    .comment(cancelBy + "主动终止流程")
                    .build();
            processInstanceService.terminate(terminateProcInstReq);
        }
        return updateById(appTopicInfo);
    }

    @Override
    public AppTopicInfo getAppTopic(String appId) {
        return getOne(Wrappers.<AppTopicInfo>lambdaQuery().eq(AppTopicInfo::getAppId, appId).last("LIMIT 1"));
    }
}




