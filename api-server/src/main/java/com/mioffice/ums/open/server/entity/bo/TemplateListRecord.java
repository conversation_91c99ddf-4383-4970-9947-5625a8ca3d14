package com.mioffice.ums.open.server.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/23 6:25 下午
 * version: 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplateListRecord {
    private Long id;
    private String bizId;
    private String templateName;
    private String botBizId;
    private String botAppId;
    private String createUsername;
    private String createName;
    private Long createTime;
}
