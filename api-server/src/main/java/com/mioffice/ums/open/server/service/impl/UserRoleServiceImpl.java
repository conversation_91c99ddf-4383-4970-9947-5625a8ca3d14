package com.mioffice.ums.open.server.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.open.server.bo.UserRoleBo;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.mioffice.ums.open.server.enums.AppUserRoleEnum;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.mapper.UserAppSysInfoMapper;
import com.mioffice.ums.open.server.service.UserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * APP用户角色
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/23 1:04 上午
 */
@Service
public class UserRoleServiceImpl implements UserRoleService {

    @Autowired
    private UserAppSysInfoMapper userAppSysInfoMapper;
    @Autowired
    private AppSysInfoMapper appSysInfoMapper;

    @Override
    public UserRoleBo getUserRole(String username, Long systemId) {
        List<AppSysInfo> applicantList = appSysInfoMapper.selectList(
                Wrappers.<AppSysInfo>lambdaQuery()
                        .eq(AppSysInfo::getCreateUsername, username)
                        .eq(AppSysInfo::getId, systemId)
        );
        List<UserAppSysInfo> inChargeUserList = userAppSysInfoMapper.selectList(
                Wrappers.<UserAppSysInfo>lambdaQuery()
                        .eq(UserAppSysInfo::getUsername, username)
                        .eq(UserAppSysInfo::getAppSysId, systemId)
                        .eq(UserAppSysInfo::getUserType, AppUserRoleEnum.APP_USER_ROLE_CHARGE.getCode())
        );
        UserRoleBo userRoleBo  = new UserRoleBo();
        userRoleBo.setApplicant(!applicantList.isEmpty());
        userRoleBo.setInChargeUser(!inChargeUserList.isEmpty());
        return userRoleBo;
    }
}
