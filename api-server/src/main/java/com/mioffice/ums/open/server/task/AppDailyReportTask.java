package com.mioffice.ums.open.server.task;

import com.mioffice.ums.open.server.service.AppMsgRepeatInfoService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.30
 */
@Slf4j
@PlanTask(name = "AppDailyReportTask", quartzCron = "0 0 8 * * ?", description = "消息app日报统计发送")
public class AppDailyReportTask implements PlanExecutor {

    @Autowired
    private AppMsgRepeatInfoService appMsgRepeatInfoService;

    @Override
    public void execute() {
        appMsgRepeatInfoService.reportAppByDay();
    }
}
