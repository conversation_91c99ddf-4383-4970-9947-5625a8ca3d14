package com.mioffice.ums.open.server.web.filter;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
public class DelegateInputStream extends ServletInputStream {
    private boolean finished = false;
    InputStream sourceStream;

    DelegateInputStream(InputStream sourceStream) {
        this.sourceStream = sourceStream;
    }


    @Override
    public boolean isFinished() {
        return finished;
    }

    @Override
    public boolean isReady() {
        return true;
    }

    @Override
    public void setReadListener(ReadListener listener) {
        // not support
    }

    @Override
    public int read() throws IOException {
        int data = this.sourceStream.read();
        if (data == -1) {
            this.finished = true;
        }
        return data;
    }
}
