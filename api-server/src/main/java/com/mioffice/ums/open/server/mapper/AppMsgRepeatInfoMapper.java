package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.open.server.entity.bo.MsgMd5RepeatBO;import com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.09
 */
public interface AppMsgRepeatInfoMapper extends BaseMapper<AppMsgRepeatInfo> {
    int updateBatch(List<AppMsgRepeatInfo> list);

    int updateBatchSelective(List<AppMsgRepeatInfo> list);

    int batchInsert(@Param("list") List<AppMsgRepeatInfo> list);

    int insertOrUpdate(AppMsgRepeatInfo record);

    int insertOrUpdateSelective(AppMsgRepeatInfo record);

    List<MsgMd5RepeatBO> selectContentMd5CountByRecentTime(@Param("beginTime") Long beginTime, @Param("endTime") Long endTime);
}