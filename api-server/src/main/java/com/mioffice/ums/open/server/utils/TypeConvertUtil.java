package com.mioffice.ums.open.server.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/20 11:41 上午
 * version: 1.0.0
 */
public class TypeConvertUtil {

    private TypeConvertUtil() {
    }

    public static List<Byte> stringToByteList(String str) {
        List<Byte> byteList = new ArrayList<>();
        if (StringUtils.isNotBlank(str)) {
            String[] strSplit = str.split(",");
            for (String subStr: strSplit) {
                byteList.add(Byte.valueOf(subStr));
            }
        }
        return byteList;
    }
}
