package com.mioffice.ums.open.server.entity.bo;

import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.27
 */
@Data
@NoArgsConstructor
public class AppSummaryBO {

    private String appId;
    private Long appSysId;
    private String appName;
    private Long createTime;
    private Long updateTime;

    public AppSummaryBO(AppSysInfo appSysInfo) {
        this.appId = appSysInfo.getAppId();
        this.appSysId = appSysInfo.getId();
        this.appName = appSysInfo.getAppName();
        this.createTime = appSysInfo.getCreateTime();
        this.updateTime = appSysInfo.getUpdateTime();
    }
}
