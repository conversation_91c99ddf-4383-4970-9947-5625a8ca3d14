package com.mioffice.ums.open.server.remote;

import com.mioffice.ums.open.common.constats.ResponseCode;
import com.xiaomi.info.grpc.client.annotation.RpcClientAutowired;
import com.xiaomi.info.pb.app.mioffice.ums.admin.v1.EmployeeInfo;
import com.xiaomi.info.pb.app.mioffice.ums.admin.v1.EmployeeInfoServiceBlockingClient;
import com.xiaomi.info.pb.app.mioffice.ums.admin.v1.GetEmployeeInfoRequest;
import com.xiaomi.info.pb.app.mioffice.ums.admin.v1.GetEmployeeInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.03
 */
@Slf4j
@Component
public class UmsAdminRpcClient {

    @RpcClientAutowired("ums-admin-server")
    private EmployeeInfoServiceBlockingClient employeeInfoServiceBlockingClient;

    public List<EmployeeInfo> getEmpList(List<String> usernameList) {
        GetEmployeeInfoRequest.Builder builder = GetEmployeeInfoRequest.newBuilder();
        builder.addAllUsername(usernameList);
        GetEmployeeInfoResponse employeeInfoResponse = employeeInfoServiceBlockingClient.getEmployeeInfoList(builder.build());
        if (employeeInfoResponse.getCode() != ResponseCode.SUCCESS.getCode()) {
            log.warn("获取员工信息失败 code = [{}], message = [{}]", employeeInfoResponse.getCode(), employeeInfoResponse.getDesc());
            return Collections.emptyList();
        } else {
            return employeeInfoResponse.getEmployeeInfoListList();
        }
    }
}
