package com.mioffice.ums.open.server.task;

import cn.hutool.core.thread.ThreadUtil;
import com.mioffice.ums.open.server.service.PushSummaryService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 同步今天的数据
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/25 3:03 下午
 */
@Slf4j
@PlanTask(name = "AppTodayPushRecordTask", quartzCron = "0 0 0/1 * * ?", description = "同步今天推送日志的数据")
public class AppTodayPushRecordTask implements PlanExecutor {
    private final PushSummaryService pushSummaryService;

    public AppTodayPushRecordTask(PushSummaryService pushSummaryService) {
        this.pushSummaryService = pushSummaryService;
    }

    @Override
    public void execute() {
        ThreadUtil.execute(()->{
            pushSummaryService.syncTodayAppPushRecords(null, null);
            log.info("同步今天推送日志的数据，执行成功");
        });
    }
}
