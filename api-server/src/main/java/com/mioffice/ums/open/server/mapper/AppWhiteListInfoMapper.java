package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.open.server.entity.info.AppWhiteListInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AppWhiteListInfoMapper extends BaseMapper<AppWhiteListInfo> {
    int updateBatch(List<AppWhiteListInfo> list);

    int updateBatchSelective(List<AppWhiteListInfo> list);

    int batchInsert(@Param("list") List<AppWhiteListInfo> list);

    int insertOrUpdate(AppWhiteListInfo record);

    int insertOrUpdateSelective(AppWhiteListInfo record);

    void batchInsertUpdate(@Param("list") List<AppWhiteListInfo> list, @Param("appWhiteListInfo") AppWhiteListInfo appWhiteListInfo);
}