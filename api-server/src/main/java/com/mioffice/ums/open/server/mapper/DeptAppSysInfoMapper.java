package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.open.server.entity.info.DeptAppSysInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.09.16 
 */
public interface DeptAppSysInfoMapper extends BaseMapper<DeptAppSysInfo> {
    int updateBatch(List<DeptAppSysInfo> list);

    int batchInsert(@Param("list") List<DeptAppSysInfo> list);

    int insertOrUpdate(DeptAppSysInfo record);

    int insertOrUpdateSelective(DeptAppSysInfo record);
}