package com.mioffice.ums.open.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * APP 用户角色
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/23 10:17 上午
 */
@Getter
@AllArgsConstructor
public enum  AppUserRoleEnum {

    /**
     * app用户角色
     */
    UNKNOWN("初始角色", (byte) 0),
    APP_USER_ROLE_CHARGE("负责人", (byte) 1);

    private final String msg;

    private final byte code;

}
