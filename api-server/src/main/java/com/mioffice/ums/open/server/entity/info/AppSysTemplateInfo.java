package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * Created by IntelliJ IDEA.
 * <AUTHOR>
 * description:
 * date: 2020/10/13 8:45 上午
 * version: 1.0.0
 */

/**
 * 应用模板表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_sys_template_info")
public class AppSysTemplateInfo extends BaseEntity {
    /**
     * 模板业务id
     */
    @TableField(value = "biz_id")
    private String bizId;

    /**
     * app_sys_info 外键id
     */
    @TableField(value = "app_sys_id")
    private Long appSysId;

    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 模板主题
     */
    @TableField(value = "template_name")
    private String templateName;

    /**
     * bot对应的appId
     */
    @TableField(value = "bot_app_id")
    private String botAppId;

    /**
     * 模板内容
     */
    @TableField(value = "template_content")
    private String templateContent;

    /**
     * 引擎模板id
     */
    @TableField(value = "template_id")
    private Long templateId;

    /**
     * 更新者username
     */
    @TableField(value = "update_username")
    private String updateUsername;

    /**
     * 创建者username
     */
    @TableField(value = "create_username")
    private String createUsername;

    /**
     * 更新者name
     */
    @TableField(value = "update_name")
    private String updateName;

    /**
     * 创建者name
     */
    @TableField(value = "create_name")
    private String createName;

    /**
     * 模板渠道
     */
    @TableField(value = "template_channel")
    private Byte templateChannel;

    /**
     * 机器人业务id
     */
    @TableField(value = "bot_biz_id")
    private String botBizId;
}