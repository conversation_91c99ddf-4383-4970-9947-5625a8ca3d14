package com.mioffice.ums.open.server.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 12 04,2022
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppMsgRuleListRecord {
    /**
     * 规则ID
     * */
    private Long id;

    /**
     * 规则名称
     * */
    private String messageRuleName;

    /**
     * 回调
     * */
    private String callback ;

    /**
     * 创建人账号
     * */
    private String createUsername;
    /**
     * 创建人姓名
     * */
    private String createName;

    /**
     * 创建时间
     * */
    private Long createTime;

    /**
     * 系统ID
     * */
    private Long appSysId;

    /**
     * 占位符
     * */
    private String placeholder;

}
