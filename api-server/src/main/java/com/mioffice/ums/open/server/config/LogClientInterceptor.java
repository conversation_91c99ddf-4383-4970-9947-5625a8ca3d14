package com.mioffice.ums.open.server.config;

import io.grpc.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 设置rpc 超时限制
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class Log<PERSON>lientInterceptor implements ClientInterceptor {

    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(MethodDescriptor<ReqT, RespT> method, CallOptions callOptions, Channel next) {
        return next.newCall(method, callOptions.withDeadline(Deadline.after(30000, TimeUnit.MILLISECONDS)));
    }
}