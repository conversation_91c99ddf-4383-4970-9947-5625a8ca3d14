package com.mioffice.ums.open.server.task;

import com.mioffice.ums.open.server.service.AppMsgRepeatInfoService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.25
 */
@Slf4j
@PlanTask(name = "AppRepeatMsgScanTask", quartzCron = "0 */1 * * * ?", description = "检测消息重复推送预警")
public class AppRepeatMsgScanTask implements PlanExecutor {

    @Autowired
    private AppMsgRepeatInfoService appMsgRepeatInfoService;


    @Override
    public void execute() {
        appMsgRepeatInfoService.scanRepeatMsg();
    }
}
