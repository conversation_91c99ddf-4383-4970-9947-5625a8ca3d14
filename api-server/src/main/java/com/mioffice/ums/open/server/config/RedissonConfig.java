package com.mioffice.ums.open.server.config;

import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.07.21
 */
//@Configuration
public class RedissonConfig {

//    @Value("${spring.redis.host}")
//    private String host;
//
//    @Value("${spring.redis.port}")
//    private String port;
//
//    @Value("${spring.redis.password:}")
//    private String password;
//
//    @Value("${spring.redis.jedis.pool.max-active}")
//    private Integer maxPoolSize;
//
//    @Bean
//    public RedissonClient getRedisson() {
//        Config config = new Config();
//        config.useSingleServer().setAddress("redis://" + host + ":" + port);
//        if (StringUtils.isNotEmpty(password)) {
//            config.useSingleServer().setPassword(password);
//        }
//        config.useSingleServer().setConnectionPoolSize(maxPoolSize);
//        return Redisson.create(config);
//    }
}
