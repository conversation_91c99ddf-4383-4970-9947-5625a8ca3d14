package com.mioffice.ums.open.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mioffice.ums.open.server.bo.AppTaskLogBo;
import com.mioffice.ums.open.server.bo.PushSummaryBo;
import com.mioffice.ums.open.server.entity.bo.AppChannelCountBo;
import com.mioffice.ums.open.server.entity.bo.AppMessageCountBo;

import java.util.List;

/**
 * <p>
 * 推送消息概览
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/18 11:41 上午
 */
public interface PushSummaryService {


    /**
     * 获取使用概览
     * @param systemIdList 系统ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return PushSummaryBo
     */
    PushSummaryBo getUseSummary(List<Long> systemIdList, String startTime, String endTime);

    /**
     * 获取使用概览
     * @param extraId 组ID
     * @param channels 渠道
     * @param systemId 系统ID
     * @param page 页码
     * @param size 页size
     * @return IPage AppTaskLogBo
     */
    IPage<AppTaskLogBo> getAppTaskLog(String extraId, Long systemId, List<Byte> channels, Integer page, Integer size);

    /**
     * 获取 top count 的消息量，正序排列
     * @param topCount
     * @param beginTime
     * @param endTime
     * @return
     */
    List<AppMessageCountBo> getTopAppMsgList(int topCount, long beginTime, long endTime);

    /**
     * 获取一定时间段内的，对应系统的消息渠道的消息数量
     * @param appSysIdList  可能为空
     * @param beginTime
     * @param endTime
     * @return
     */
    List<AppChannelCountBo> getAppMsgChannelCount(List<Long> appSysIdList, long beginTime, long endTime);
    /**
     * 同步昨天推送数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void syncYesterdayAppPushRecords(Long startTime, Long endTime);

    /**
     * 同步今天推送数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void syncTodayAppPushRecords(Long startTime, Long endTime);

    /**
     * 同步之前的推送数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void syncAppPushRecordsWithStartEndTime(Long startTime, Long endTime);

    /**
     * 删除过期的消息
     * 凌晨2点
     * @param timeMills
     */
    void deleteExpiredPushRecord(Long timeMills);

}
