package com.mioffice.ums.open.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mioffice.ums.open.server.entity.bo.AppInfoBo;
import com.mioffice.ums.open.server.entity.bo.AppInfoStatusBo;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
public interface AppInfoService extends IService<AppSysInfo> {
    /**
     * app secret
     * cache
     *
     * @param appId
     * @return
     */
    String getSecret(String appId);

    /**
     * saveOrUpdate
     *
     * @param appInfoBo
     */
    AppInfoStatusBo saveOrUpdate(AppInfoBo appInfoBo, List<String> roleList);

    /**
     * query by id
     *
     * @param id
     * @return
     */
    AppInfoBo queryById(Long id,String userName,List<String> roleList);

    /**
     * update status
     *
     * @param id
     * @param status
     */
    void updateStatus(Long id, int status);


    /**
     * updateWhiteStatus
     * @param id
     * @param whiteStatus
     */
    boolean updateWhiteStatus(Long id,int whiteStatus);

    /**
     * query by appId
     *
     * @param appId
     * @return
     */
    AppInfoBo queryByAppId(String appId);

}
