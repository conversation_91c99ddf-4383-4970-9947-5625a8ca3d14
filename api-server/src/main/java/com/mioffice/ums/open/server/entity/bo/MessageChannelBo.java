package com.mioffice.ums.open.server.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/16
 */
@Data
public class MessageChannelBo {
    private int channel;
    private List<BotInfoDesc> botAppIds;
    private int isOutSend;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    public static class BotInfoDesc {
        @EqualsAndHashCode.Include
        private String botAppId;
        @EqualsAndHashCode.Include
        private String botBizId;
    }

    @Data
    public static class BotInfoPair extends BotInfoDesc {
        private MessageChannelBo channelBo;

        public BotInfoPair(BotInfoDesc botInfoDesc, MessageChannelBo messageChannelBo) {
            super(botInfoDesc.getBotAppId(), botInfoDesc.getBotBizId());
            this.channelBo = messageChannelBo;
        }
    }
}
