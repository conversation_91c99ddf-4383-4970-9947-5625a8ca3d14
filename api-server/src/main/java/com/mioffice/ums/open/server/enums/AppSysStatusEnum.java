package com.mioffice.ums.open.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 系统状态
 * </p>
 *
 * <AUTHOR>
 * @since 2020/9/21 2:36
 */
@Getter
@AllArgsConstructor
public enum AppSysStatusEnum {
    /**
     * 系统状态
     */
    NO("非法状态", (byte) 0),
    APPROVING("审核中", (byte) 1),
    APPROVED("审核通过", (byte) 2),
    APPROVE_BACK("审核驳回", (byte) 3),
    CANCEL("已取消", (byte) 4),
    STOP("已停用", (byte) 5);


    private final String msg;

    private final byte code;

    public static String getMsgByCode(byte code) {
        AppSysStatusEnum[] values = AppSysStatusEnum.values();
        for (AppSysStatusEnum appSysStatusEnum : values) {
            if (appSysStatusEnum.getCode() == code) {
                return appSysStatusEnum.getMsg();
            }
        }
        return "";
    }

    public static AppSysStatusEnum getByCode(byte code) {
        for (AppSysStatusEnum appSysStatusEnum : AppSysStatusEnum.values()) {
            if (appSysStatusEnum.getCode() == code) {
                return appSysStatusEnum;
            }
        }
        return NO;
    }

}
