package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.12.09 
 */

/**
 * 消息重复推送记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_msg_repeat_info")
public class AppMsgRepeatInfo extends BaseEntity {
    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 机器人业务id
     */
    @TableField(value = "bot_biz_id")
    private String botBizId;

    /**
     * 内容MD5
     */
    @TableField(value = "content_md5")
    private String contentMd5;

    /**
     * 模板用户md5
     */
    @TableField(value = "temp_user_md5")
    private String tempUserMd5;

    /**
     * username/email/phone
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 消息渠道
     */
    @TableField(value = "channel")
    private Byte channel;

    /**
     * 模板biz_id
     */
    @TableField(value = "template_biz_id")
    private String templateBizId;

    public static com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo newCreateAndUpdateTimeInstant() {
        com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo appMsgRepeatInfo = new com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo();
        appMsgRepeatInfo.setCreateTime(System.currentTimeMillis());
        appMsgRepeatInfo.setUpdateTime(System.currentTimeMillis());
        return appMsgRepeatInfo;
    }

    public static com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo newUpdateTimeInstant() {
        com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo appMsgRepeatInfo = new com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo();
        appMsgRepeatInfo.setUpdateTime(System.currentTimeMillis());
        return appMsgRepeatInfo;
    }
}