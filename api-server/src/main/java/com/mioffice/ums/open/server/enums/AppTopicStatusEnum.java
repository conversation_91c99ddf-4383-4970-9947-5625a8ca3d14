package com.mioffice.ums.open.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum AppTopicStatusEnum {

    NONE("未申请", 0),
    UNDER_REVIEW("审核中", 1),
    READY("通过", 2),
    DENY("拒绝", 3),
    CANCEL("取消申请或停用", 4);

    private final String msg;

    private final int code;

    public static String getMsgByCode(int code) {
        AppTopicStatusEnum[] values = AppTopicStatusEnum.values();
        for (AppTopicStatusEnum appTopicStatusEnum : values) {
            if (appTopicStatusEnum.getCode() == code) {
                return appTopicStatusEnum.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }

    public static AppTopicStatusEnum getByCode(int code) {
        for (AppTopicStatusEnum appTopicStatusEnum : AppTopicStatusEnum.values()) {
            if (appTopicStatusEnum.getCode() == code) {
                return appTopicStatusEnum;
            }
        }
        return NONE;
    }
}
