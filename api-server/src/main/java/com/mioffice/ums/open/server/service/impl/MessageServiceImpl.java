package com.mioffice.ums.open.server.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.mioffice.ums.open.common.constats.AppSysStatus;
import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.constats.MessageStatusEnum;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.exception.LimitException;
import com.mioffice.ums.open.common.exception.BizException;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.message.*;
import com.mioffice.ums.open.common.util.JsonUtils;
import com.mioffice.ums.open.server.cache.AppSysTemplateCache;
import com.mioffice.ums.open.server.entity.bo.RateConfigBO;
import com.mioffice.ums.open.server.entity.info.*;
import com.mioffice.ums.open.server.limiter.LimiterManager;
import com.mioffice.ums.open.server.mapper.AppMsgRepeatInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysBotInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysPushRecordInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysTemplateInfoMapper;
import com.mioffice.ums.open.server.mapper.AppWhiteListInfoMapper;
import com.mioffice.ums.open.server.remote.MessageRpcClient;
import com.mioffice.ums.open.server.service.AppInfoService;
import com.mioffice.ums.open.server.service.AppMsgRepeatInfoService;
import com.mioffice.ums.open.server.service.AppSysBotInfoService;
import com.mioffice.ums.open.server.service.MessageService;
import com.mioffice.ums.open.server.utils.AesUtils;
import com.mioffice.ums.open.server.utils.IdUtil;
import com.mioffice.ums.open.server.utils.MessageUtil;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.11
 */
@Slf4j
@Service
public class MessageServiceImpl implements MessageService {

    @Value("${ums.open.bot-id}")
    private String rootBotAppId;

    @Autowired
    private MessageRpcClient messageRpcClient;
    @Autowired
    AppSysBotInfoService appSysBotInfoService;
    @Autowired
    LimiterManager limiterManager;

    @Autowired
    private AppSysBotInfoMapper appSysBotInfoMapper;

    @Autowired
    private AppSysPushRecordInfoMapper appSysPushRecordInfoMapper;

    @Autowired
    private AppSysTemplateCache appSysTemplateCache;

    @Autowired
    private AppMsgRepeatInfoMapper appMsgRepeatInfoMapper;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    private AppInfoService appInfoService;

    @Autowired
    private AppWhiteListInfoMapper appWhiteListInfoMapper;

    @Autowired
    private AppSysTemplateInfoMapper appSysTemplateInfoMapper;

    @Autowired
    private Environment env;

    @Override
    public UmsResponse<MsgResult> sendBatch(BatchMessage batchMessage, String sysId) {

        try {
            limiterManager.getLimiter(sysId).acquire(1100);
        } catch (LimitException e) {
            throw new BizException(ResponseCode.LIMIT_EXCEED_ERROR);
        }

        AppSysInfo appSysInfo = appInfoService.getOne(Wrappers.<AppSysInfo>lambdaQuery().eq(AppSysInfo::getAppId, sysId).last("LIMIT 1"));
        if (Objects.isNull(appSysInfo) || AppSysStatus.PASS.getCode() != appSysInfo.getAppSysStatus()) {
            throw new BizException(ResponseCode.APP_NOT_EXISTS);
        }

        checkHadBot(sysId, batchMessage.getBotAppId());
        String extraId = IdUtil.createUUID();
        log.info("发送消息 templateBizId = [{}], extraId = [{}]", batchMessage.getTemplateBizId(), extraId);
        List<MessageUser> messageUserList = batchMessage.getUserList()
                .stream()
                .map(p -> MessageUtil.toConvert(batchMessage.getBotAppId(), extraId, batchMessage.getContentFlag(), p
                        , sysId))
                .collect(Collectors.toList());

        AppSysTemplateInfo appSysTemplateInfo = appSysTemplateCache.get(batchMessage.getTemplateBizId());
        Preconditions.checkNotNull(appSysTemplateInfo,
                String.format("模板不存在 templateBizId = [%s]", batchMessage.getTemplateBizId()));

        MessageUserResponse messageUserResponse =
                messageRpcClient.sendMessageBatch(messageUserList, appSysTemplateInfo.getTemplateId());
        if (messageUserResponse.getCode() == ResponseCode.SUCCESS.getCode()) {

            AppSysPushRecordInfo appSysPushRecordInfo =
                    toAppSysPushRecordInfo(sysId, (long) batchMessage.getUserList().size(), batchMessage.getChannel(),
                            extraId, batchMessage.getBotAppId(), null, appSysTemplateInfo);
            appSysPushRecordInfoMapper.insert(appSysPushRecordInfo);

            MsgResult msgResult = new MsgResult();
            msgResult.setGroupId(extraId);
            return UmsResponse.valueOf(msgResult);
        } else {
            int code = messageUserResponse.getCode();
            String message = messageUserResponse.getMessage();
            log.warn("发送消息失败 appId = [{}], extraId = [{}], code = [{}], msg = [{}]", batchMessage.getBotAppId(),
                    extraId, code, message);
            return UmsResponse.valueOf(code, message);
        }
    }

    @Override
    public UmsResponse<MsgResult> sendBatchV2(BatchMessage batchMessage, String sysId, String extraId) {

        long t1 = System.currentTimeMillis();

        boolean isTest = Arrays.stream(env.getActiveProfiles()).anyMatch(
                env -> (env.equalsIgnoreCase("test")));

        // 测试环境需开启白名单
        if (isTest) {
            AppSysInfo info = appInfoService.getOne(Wrappers.<AppSysInfo>lambdaQuery().eq(AppSysInfo::getAppId, sysId)
                    .eq(AppSysInfo::getAppSysStatus, AppSysStatus.PASS.getCode()).last("LIMIT 1"));

            if (info.getWhiteStatus() != 1) {
                throw new IllegalArgumentException("测试环境需开启并设置白名单");
            }
        }

        String botBizId = batchMessage.getBotBizId();
        try {
            limiterManager.getLimiter(botBizId).acquire(1100);
        } catch (LimitException e) {
            throw new BizException(ResponseCode.LIMIT_EXCEED_ERROR);
        }

        long t2 = System.currentTimeMillis();
        log.info("sendBatchV2 t1 -- t2: {}", (t2 - t1));

        AppSysInfo appSysInfo = appInfoService.getOne(Wrappers.<AppSysInfo>lambdaQuery().eq(AppSysInfo::getAppId, sysId).last("LIMIT 1"));
        if (Objects.isNull(appSysInfo) || AppSysStatus.PASS.getCode() != appSysInfo.getAppSysStatus()) {
            throw new BizException(ResponseCode.APP_NOT_EXISTS);
        }

        AppSysBotInfo sysBotInfo = this.getBot(sysId, botBizId, batchMessage.getChannel());
        Preconditions.checkNotNull(sysBotInfo, "该渠道不存在机器人");

        // 邮件发送范围检查
        this.checkEmailRange(batchMessage, sysBotInfo);

//        String extraId = IdUtil.createUUID();
        log.info("发送消息 templateBizId = [{}], extraId = [{}]", batchMessage.getTemplateBizId(), extraId);

        AppSysTemplateInfo appSysTemplateInfo = appSysTemplateCache.get(batchMessage.getTemplateBizId());

        Preconditions.checkNotNull(appSysTemplateInfo,
                String.format("模板不存在 templateBizId = [%s]", batchMessage.getTemplateBizId()));
        Preconditions.checkState(rootBotAppId.equals(appSysTemplateInfo.getBotAppId()) ||
                        appSysTemplateInfo.getBotAppId().equals(sysBotInfo.getBotAppId()),
                "模板不属于该机器人");

        long t3 = System.currentTimeMillis();
        log.info("sendBatchV2 t2 -- t3: {}", (t3 - t2));

        // 内容消息 contentMd5 组装
        List<AppMsgRepeatInfo> appMsgRepeatInfoList = msgRepeat(batchMessage, sysId);

        // 构建消息体，投建到 engine
        List<MessageUser> messageUserList =
                newMessageUser(sysId, extraId, sysBotInfo.getBotAppId(), appMsgRepeatInfoList, batchMessage);

        long t4 = System.currentTimeMillis();
        log.info("sendBatchV2 t3 -- t4: {}", (t4 - t3));

        MessageUserResponse messageUserResponse =
                messageRpcClient.sendMessageBatch(messageUserList, appSysTemplateInfo.getTemplateId());

        long t5 = System.currentTimeMillis();
        log.info("sendBatchV2 t4 -- t5: {}", (t5 - t4));

        if (messageUserResponse.getCode() == ResponseCode.SUCCESS.getCode()) {
            AppSysPushRecordInfo appSysPushRecordInfo =
                    toAppSysPushRecordInfo(sysId, (long) batchMessage.getUserList().size(), batchMessage.getChannel(),
                            extraId, sysBotInfo.getBotAppId(), botBizId, appSysTemplateInfo);

            ThreadUtil.execute(() -> {
                appSysPushRecordInfoMapper.insert(appSysPushRecordInfo);
                // 异步记录发送的内容的MD5，用于判断是否去重
                appMsgRepeatInfoMapper.batchInsert(appMsgRepeatInfoList);
            });

            MsgResult msgResult = new MsgResult();
            msgResult.setGroupId(extraId);
            return UmsResponse.valueOf(msgResult);
        } else {
            int code = messageUserResponse.getCode();
            String message = messageUserResponse.getMessage();
            log.warn("发送消息失败 appId = [{}], extraId = [{}], code = [{}], msg = [{}]", batchMessage.getBotAppId(),
                    extraId, code, message);
            return UmsResponse.valueOf(code, message);
        }
    }

    private List<MessageUser> newMessageUser(String sysId, String extraId, String botAppId,
                                             List<AppMsgRepeatInfo> appMsgRepeatInfoList, BatchMessage batchMessage) {

        AppSysInfo info = appInfoService.getOne(Wrappers.<AppSysInfo>lambdaQuery().eq(AppSysInfo::getAppId, sysId)
                .eq(AppSysInfo::getAppSysStatus, AppSysStatus.PASS.getCode()), false);

        if (info.getWhiteStatus() == 1) {
            Set<String> sendIdSet = batchMessage.getUserList().stream()
                    .map(p -> MessageUtil.parseSendingId(batchMessage.getChannel(), p)).flatMap(List::stream)
                    .collect(Collectors.toSet());
            // 开启白名单的消息
            Set<String> whiteSendIdSet = appWhiteListInfoMapper.selectList(
                    Wrappers.<AppWhiteListInfo>lambdaQuery()
                            .eq(AppWhiteListInfo::getAppId, info.getAppId())
                            .eq(AppWhiteListInfo::getChannel, batchMessage.getChannel())
                            .in(AppWhiteListInfo::getWhiteId, sendIdSet)
            ).stream().map(AppWhiteListInfo::getWhiteId).collect(Collectors.toSet());

            return MessageUtil.toWhiteConvert(botAppId, extraId, batchMessage.getContentFlag(),
                    batchMessage.getChannel(), batchMessage.getUserList(), whiteSendIdSet, sysId);
        } else if (info.getDeduplicateStatus() == 1) {
            // 去重
            List<String> contentMd5List =
                    appMsgRepeatInfoList.stream().map(AppMsgRepeatInfo::getContentMd5).collect(Collectors.toList());

            long beginTime = System.currentTimeMillis() - AppMsgRepeatInfoService.RECENT_TIME;
            long endTime = System.currentTimeMillis();

            Set<String> hasContentMd5Set = appMsgRepeatInfoMapper.selectList(
                            Wrappers.<AppMsgRepeatInfo>lambdaQuery()
                                    .in(AppMsgRepeatInfo::getContentMd5, contentMd5List)
                                    .between(AppMsgRepeatInfo::getCreateTime, beginTime, endTime)
                                    .select(AppMsgRepeatInfo::getContentMd5)
                    )
                    .stream()
                    .map(AppMsgRepeatInfo::getContentMd5)
                    .collect(Collectors.toSet());

            return batchMessage.getUserList().stream()
                    .map(p -> MessageUtil.toDeduplicateConvert(botAppId, extraId, p, batchMessage.getContentFlag(),
                            hasContentMd5Set, sysId)).collect(Collectors.toList());
        } else {
            // 正常 不做任何处理的消息，直接发
            return batchMessage.getUserList().stream()
                    .map(p -> MessageUtil.toConvert(botAppId, extraId, batchMessage.getContentFlag(), p, sysId))
                    .collect(Collectors.toList());
        }
    }

    private void checkEmailRange(BatchMessage batchMessage, AppSysBotInfo sysBotInfo) {
        if (batchMessage.getChannel() == MessageChannelEnum.EMAIL.getType()) {

            if (sysBotInfo.getIsOutSend() == 0) {
                throw new IllegalArgumentException("该应用不支持邮件发送");
            }

            List<String> emailList =
                    batchMessage.getUserList().stream().map(MsgUser::getEmail).collect(Collectors.toList());
            // 1- 内部邮件，2 -外部邮件， 3- 外部邮件或者内部邮件
            if ((sysBotInfo.getIsOutSend() & 2) != 2) {
                // 不支持外部
                for (String email : emailList) {
                    if (email.lastIndexOf("@xiaomi.com") == -1) {
                        // 非小米邮箱
                        throw new IllegalArgumentException("该应用不支持外部邮件发送");
                    }
                }
            }
        }

    }

    private List<AppMsgRepeatInfo> msgRepeat(BatchMessage batchMessage, String sysId) {
        // 构建消息体
        String botBizId = batchMessage.getBotBizId();
        String templateBizId = batchMessage.getTemplateBizId();
        List<AppMsgRepeatInfo> appMsgRepeatInfoList = new ArrayList<>();
        for (MsgUser msgUser : batchMessage.getUserList()) {
            AppMsgRepeatInfo appMsgRepeatInfo = AppMsgRepeatInfo.newCreateAndUpdateTimeInstant();
            appMsgRepeatInfo.setAppId(sysId);
            appMsgRepeatInfo.setBotBizId(botBizId);
            String md5Content = md5Content(sysId, batchMessage.getChannel(), batchMessage.getTemplateBizId(), msgUser);
            appMsgRepeatInfo.setContentMd5(md5Content);
            appMsgRepeatInfo.setUserId(userIdFromChannel(batchMessage.getChannel(), msgUser));
            appMsgRepeatInfo.setTempUserMd5(
                    md5TempUser(sysId, batchMessage.getChannel(), batchMessage.getTemplateBizId(), msgUser));
            appMsgRepeatInfo.setChannel(batchMessage.getChannel());
            appMsgRepeatInfo.setTemplateBizId(templateBizId);
            appMsgRepeatInfoList.add(appMsgRepeatInfo);
            appMsgRepeatInfo.setCreateTime(System.currentTimeMillis());
            appMsgRepeatInfo.setUpdateTime(System.currentTimeMillis());

            // 为后面进行消息去重中断
            msgUser.setContentMd5(md5Content);
        }
        return appMsgRepeatInfoList;
    }

    private String userIdFromChannel(Byte channel, MsgUser msgUser) {
        MessageChannelEnum messageChannelEnum = MessageChannelEnum.get(channel);
        switch (messageChannelEnum) {
            case MI_WORK:
                if (StringUtils.isNotBlank(msgUser.getUsername())) {
                    return msgUser.getUsername();
                } else if (StringUtils.isNotBlank(msgUser.getEmail())) {
                    return msgUser.getEmail();
                } else if (StringUtils.isNotBlank(msgUser.getChatId())) {
                    return msgUser.getChatId();
                }
                return msgUser.getUsername();
            case SMS:
                return msgUser.getPhone();
            case EMAIL:
                return msgUser.getEmail();
            default:
                return "";
        }
    }

    private String md5Content(String appId, Byte channel, String templateBizId, MsgUser msgUser) {
        MessageChannelEnum messageChannelEnum = MessageChannelEnum.get(channel);
        String content = null;

        switch (messageChannelEnum) {
            case MI_WORK:
                content = String.format("%s|%s|%s|%s|%s|%s", appId, templateBizId, msgUser.getUsername(),
                        msgUser.getEmail(), msgUser.getChatId(), JsonUtils.toJson(msgUser.getParams()));
                break;
            case SMS:
                content = String.format("%s|%s|%s|%s", appId, templateBizId, msgUser.getPhone(),
                        JsonUtils.toJson(msgUser.getParams()));
                break;
            case EMAIL:
                content = String.format("%s|%s|%s|%s", appId, templateBizId, msgUser.getEmail(),
                        JsonUtils.toJson(msgUser.getParams()));
                break;
            default:
                return "";
        }

        return DigestUtils.md5Hex(content.getBytes());
    }

    private String md5TempUser(String appId, Byte channel, String templateBizId, MsgUser msgUser) {
        MessageChannelEnum messageChannelEnum = MessageChannelEnum.get(channel);
        String content = null;

        switch (messageChannelEnum) {
            case MI_WORK:
                content =
                        String.format("%s|%s|%s|%s|%s", appId, templateBizId, msgUser.getUsername(), msgUser.getEmail(),
                                msgUser.getChatId());
                break;
            case SMS:
                content = String.format("%s|%s|%s", appId, templateBizId, msgUser.getPhone());
                break;
            case EMAIL:
                content = String.format("%s|%s|%s", appId, templateBizId, msgUser.getEmail());
                break;
            default:
                return "";
        }

        return DigestUtils.md5Hex(content.getBytes());
    }

    @Override
    public UmsResponse<ImageResult> uploadLarkImage(ImageUrl imageUrl, String appId) {

        String botBizId = imageUrl.getBotBizId();
        AppSysBotInfo bot = getBot(appId, botBizId, MessageChannelEnum.MI_WORK.getType());

        ImageKeyResponse imageKeyResponse = messageRpcClient.uploadLarkImage(bot.getBotAppId(), imageUrl.getUrl());
        if (imageKeyResponse.getCode() == ResponseCode.SUCCESS.getCode()) {
            ImageResult imageResult = new ImageResult();
            imageResult.setKey(imageKeyResponse.getKey());
            imageResult.setUrl(imageUrl.getUrl());
            return UmsResponse.valueOf(imageResult);
        } else {
            return UmsResponse.valueOf(imageKeyResponse.getCode(), imageKeyResponse.getMessage());
        }
    }

    @Override
    public UmsResponse<AppPushResult> pushLogResult(String groupId, String appId) {
        try {
            log.info("业务系统发起了接口调用，groupId = [{}], appId = [{}], time = [{}]", groupId, appId,
                    System.currentTimeMillis());
            RateConfigBO rateConfigBO = new RateConfigBO();
            rateConfigBO.setRate(1);
            rateConfigBO.setRateMs(10 * 60 * 1000L);
            limiterManager.getLimiter(String.format("%s-%s", appId, groupId), rateConfigBO).acquire(1100);
        } catch (LimitException e) {
            throw new BizException(ResponseCode.LIMIT_EXCEED_ERROR);
        }
        MessageResultSingleForCustomResponse messagePageByExtraId =
                messageRpcClient.getMessagePageByExtraId(groupId, 1L, 501);
        List<AppMessageRecord> appMessageRecordList = new ArrayList<>();
        List<MessageRecord> recordsList = messagePageByExtraId.getMessageResultPage().getRecordsList();
        recordsList.forEach(
                messageRecord -> {
                    AppMessageRecord appPushResult = getAppMessageRecord(messageRecord);
                    appMessageRecordList.add(appPushResult);
                }
        );
        AppPushResult appPushResult = new AppPushResult();
        appPushResult.setAppId(appId);
        appPushResult.setGroupId(groupId);
        appPushResult.setList(appMessageRecordList);

        return UmsResponse.valueOf(appPushResult);
    }

    @Override
    public UmsResponse<List<FinalMsgResult>> getFinalMessage(String groupId, String username) {

        FinalContentResponse finalContentResponse = messageRpcClient.getFinalMessage(groupId, username);

        List<FinalMsgResult> list = new ArrayList<>();
        for (FinalContent finalContent : finalContentResponse.getFinalContentList()) {
            FinalMsgResult finalMsgResult = new FinalMsgResult();
            finalMsgResult.setExtraId(finalContent.getExtraId());
            finalMsgResult.setUsername(finalContent.getUsername());
            finalMsgResult.setFinalContent(finalContent.getFinalContent());
            list.add(finalMsgResult);
        }
        return UmsResponse.valueOf(list);
    }

    @Override
    public ImmutablePair<Boolean, String> precheckBotAndTemplate(String appId, Integer channel, String botBizId,
                                                                 String templateBizId) {
        AppSysBotInfo sysBotInfo = null;
        try {
            sysBotInfo = this.getBot(appId, botBizId, channel.byteValue());
        } catch (Exception e) {
            return ImmutablePair.of(false, e.getMessage());
        }
        if (Objects.isNull(sysBotInfo)) {
            return ImmutablePair.of(false, "该渠道机器人不存在");
        }
        AppSysTemplateInfo appSysTemplateInfo = appSysTemplateInfoMapper.selectOne(
                Wrappers.<AppSysTemplateInfo>lambdaQuery().eq(AppSysTemplateInfo::getBizId, templateBizId)
        );
        if (Objects.isNull(appSysTemplateInfo)) {
            return ImmutablePair.of(false, String.format("模板不存在 templateBizId = [%s]", templateBizId));
        }
        if (!appSysTemplateInfo.getBotAppId().equals(sysBotInfo.getBotAppId())) {
            return ImmutablePair.of(false, "模板不属于该机器人");
        }
        return ImmutablePair.of(true, "success");
    }

    private AppMessageRecord getAppMessageRecord(MessageRecord messageRecord) {
        AppMessageRecord appMessageRecord = new AppMessageRecord();
        appMessageRecord.setUsername(messageRecord.getUsername());
        appMessageRecord.setUserEmail(messageRecord.getEmail());
        appMessageRecord.setChatId(messageRecord.getChatId());
        try {
            String phone = AesUtils.decrypt(messageRecord.getPhone());
            if (StringUtils.isNotBlank(phone)) {
                appMessageRecord.setPhone(phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
            } else {
                appMessageRecord.setPhone("");
            }
        } catch (Exception e) {
            log.warn("手机号解密错误", e);
        }
        setMessageStatusEnum(appMessageRecord, messageRecord.getMessageStatus());
        setMessageChannelEnum(appMessageRecord, messageRecord.getChannel());
        if (appMessageRecord.getMessageStatusEnum().getStatus() != 2) {
            appMessageRecord.setErrorLog(messageRecord.getErrorLog());
        }
        return appMessageRecord;
    }

    private void setMessageStatusEnum(AppMessageRecord appMessageRecord, int messageStatus) {
        switch (messageStatus) {
            case 1:
                appMessageRecord.setMessageStatusEnum(MessageStatusEnum.SENDING);
                break;
            case 2:
                appMessageRecord.setMessageStatusEnum(MessageStatusEnum.SEND_SUCCESS);
                break;
            case 3:
                appMessageRecord.setMessageStatusEnum(MessageStatusEnum.SEND_FAIL);
                break;
            case 4:
                appMessageRecord.setMessageStatusEnum(MessageStatusEnum.SEND_INTERRUPT);
                break;
            default:
                break;
        }
    }

    private void setMessageChannelEnum(AppMessageRecord appMessageRecord, int channel) {
        switch (channel) {
            case 1:
                appMessageRecord.setMessageChannelEnum(MessageChannelEnum.MI_WORK);
                break;
            case 2:
                appMessageRecord.setMessageChannelEnum(MessageChannelEnum.EMAIL);
                break;
            case 3:
                appMessageRecord.setMessageChannelEnum(MessageChannelEnum.SMS);
                break;
            case 4:
                appMessageRecord.setMessageChannelEnum(MessageChannelEnum.MI_PUSH);
                break;
            default:
                break;
        }
    }

    /**
     * 检测是否有机器人
     *
     * @param sysId
     * @param botAppId
     * @param sysId
     */
    private void checkHadBot(String sysId, String botAppId) {
        List<AppSysBotInfo> appSysBotInfoList = appSysBotInfoMapper.selectList(
                Wrappers.<AppSysBotInfo>lambdaQuery()
                        .eq(AppSysBotInfo::getAppId, sysId)
                        .eq(AppSysBotInfo::getBotAppId, botAppId)
        );
        Preconditions.checkState(!appSysBotInfoList.isEmpty(),
                String.format("应用没有该机器人 sysId = [%s], botAppId=[%s]", sysId, botAppId));
    }

    public AppSysBotInfo getBot(String sysId, String botBizId, byte channel) {
        List<AppSysBotInfo> appSysBotInfoList = appSysBotInfoMapper.selectList(
                Wrappers.<AppSysBotInfo>lambdaQuery()
                        .eq(AppSysBotInfo::getAppId, sysId)
                        .eq(AppSysBotInfo::getBotBizId, botBizId)
                        .eq(AppSysBotInfo::getChannel, channel)
        );

        Preconditions.checkState(!appSysBotInfoList.isEmpty(),
                String.format("应用没有该机器人 sysId = [%s], botBizId=[%s]", sysId, botBizId));

        return appSysBotInfoList.get(0);
    }

    private AppSysPushRecordInfo toAppSysPushRecordInfo(String sysId, Long allCount, Byte channel, String extraId,
                                                        String botAppId, String botBizId,
                                                        AppSysTemplateInfo appSysTemplateInfo) {
        AppSysPushRecordInfo appSysPushRecordInfo = new AppSysPushRecordInfo();
        appSysPushRecordInfo.setAllCount(allCount);
        appSysPushRecordInfo.setAppId(sysId);
        appSysPushRecordInfo.setBotAppId(botAppId);
        appSysPushRecordInfo.setBotBizId(botBizId);
        appSysPushRecordInfo.setTemplateId(appSysTemplateInfo.getTemplateId());
        appSysPushRecordInfo.setTemplateBizId(appSysTemplateInfo.getBizId());
        appSysPushRecordInfo.setTitleCn(appSysTemplateInfo.getTemplateName());
        appSysPushRecordInfo.setTitleEn(appSysTemplateInfo.getTemplateName());
        appSysPushRecordInfo.setChannel(channel);
        appSysPushRecordInfo.setExtraId(extraId);
        return appSysPushRecordInfo;
    }

}
