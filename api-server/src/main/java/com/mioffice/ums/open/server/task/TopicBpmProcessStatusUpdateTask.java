package com.mioffice.ums.open.server.task;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.mi.oa.infra.oaucf.bpm.rep.QueryProcInstResp;
import com.mi.oa.infra.oaucf.bpm.service.ProcessInstanceService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mioffice.ums.open.server.entity.info.AppTopicInfo;
import com.mioffice.ums.open.server.enums.AppTopicStatusEnum;
import com.mioffice.ums.open.server.service.AppTopicInfoService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * @ClassName BpmProcessStatusUpdateTask
 * @Description Bpm流程状态更新的定时任务
 * <AUTHOR>
 * @Date 2023/3/6 16:48
 **/
@Slf4j
@PlanTask(name = "TopicBpmProcessStatusUpdateTask", quartzCron = "0 0/1 * * * ?", description = "申请专用队列的Bpm流程状态更新的定时任务")
public class TopicBpmProcessStatusUpdateTask implements PlanExecutor {

    @Resource
    private ProcessInstanceService processInstanceService;

    @Resource
    private AppTopicInfoService appTopicInfoService;

    @Override
    public void execute() {
        String requestData = PlanThreadLocal.getRequestData();

        List<AppTopicInfo> appTopicInfoList;

        if (StringUtils.isNotBlank(requestData)) {
            appTopicInfoList =
                    appTopicInfoService.list(
                            Wrappers.<AppTopicInfo>lambdaQuery()
                                    .eq(AppTopicInfo::getStatus, AppTopicStatusEnum.UNDER_REVIEW.getCode())
                                    .in(AppTopicInfo::getId,
                                            Splitter.on(",")
                                                    .splitToList(requestData).stream().map(Long::parseLong)
                                                    .collect(Collectors.toList())));
        } else {
            appTopicInfoList = appTopicInfoService.list(
                    Wrappers.<AppTopicInfo>lambdaQuery()
                            .eq(AppTopicInfo::getStatus, AppTopicStatusEnum.UNDER_REVIEW.getCode()));
        }

        if (CollectionUtils.isNotEmpty(appTopicInfoList)) {
            appTopicInfoList.forEach(appTopicInfo -> {
                try {
                    BaseResp<QueryProcInstResp> resp =
                            processInstanceService.get(appTopicInfo.getBpmInstanceId(), null);
                    if (resp.getCode() == 0 && Objects.nonNull(resp.getData())) {
                        switch (resp.getData().getProcessInstanceStatus()) {
                            case COMPLETED:
                                appTopicInfo.setStatus(AppTopicStatusEnum.READY.getCode());
                                appTopicInfoService.updateById(appTopicInfo);
                                break;
                            case REJECTED:
                                appTopicInfo.setStatus(AppTopicStatusEnum.DENY.getCode());
                                appTopicInfoService.updateById(appTopicInfo);
                                break;
                            case TERMINATED:
                                appTopicInfo.setStatus(AppTopicStatusEnum.CANCEL.getCode());
                                appTopicInfoService.updateById(appTopicInfo);
                                break;
                            default:
                                break;
                        }
                    }
                } catch (Exception ex) {
                    log.error("get_bpm_process_err", ex);
                }
            });
        }
    }
}
