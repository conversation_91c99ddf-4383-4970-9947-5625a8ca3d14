package com.mioffice.ums.open.server.enums;

import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.24
 */
@Getter
public enum AppAlarmStatueEnum {

    /**
     * 报警状态
     */
    OFF((byte) 0, "关闭"),
    ON((byte) 1, "开启");

    private Byte status;
    private String desc;

    AppAlarmStatueEnum(Byte status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
