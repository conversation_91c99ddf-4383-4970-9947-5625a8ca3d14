package com.mioffice.ums.open.server.remote;

import com.xiaomi.info.grpc.client.annotation.RpcClientAutowired;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020.09.11
 */
@Slf4j
@Component
public class MessageRpcClient {

    @RpcClientAutowired("ums-engine-message-server")
    private MessageServiceBlockingClient client;

    public MessageUserResponse sendMessageBatch(List<MessageUser> messageUserList, Long templateId) {

        MessageTemplateId messageTemplateId = MessageTemplateId.newBuilder().setMessageTemplateId(templateId).build();

        MessageRequest messageRequest = MessageRequest
                .newBuilder()
                .setMessageTemplateId(messageTemplateId)
                .addAllUsers(messageUserList)
                .build();
        return client.sendMessage(messageRequest);
    }

    public MessageTemplateResponse createMessageTemplate(String templateName, String templateContent, String botAppId, Integer channel, String appId) {
        MessageTemplateRequest.Builder builder = MessageTemplateRequest.newBuilder();
        builder.setTitleCn(templateName)
                .setContentCn(templateContent)
                .setMsgFormatType(2)
                .setAppId(botAppId)
                .setChannel(channel)
                .setSystemId(appId);
        return client.createMessageTemplate(builder.build());
    }

    public MessageUserResponse innerMessage(List<MessageUser> messageUserList, Long templateId) {
        MessageTemplateId messageTemplateId = MessageTemplateId.newBuilder().setMessageTemplateId(templateId).build();
        MessageRequest messageRequest = MessageRequest
                .newBuilder()
                .setMessageTemplateId(messageTemplateId)
                .addAllUsers(messageUserList)
                .build();
        return client.innerSendMessage(messageRequest);
    }

    public MessageNumberAndTimeResponse getMessagePushRecord(List<String> extraIdList) {
        MessageExtraIdRequest messageExtraIdRequest = MessageExtraIdRequest
                .newBuilder()
                .addAllExtraIdList(extraIdList)
                .build();
        return client.getMessageNumberAndTimeByExtraIdBatch(messageExtraIdRequest);
    }

    public ImageKeyResponse uploadLarkImage(String botAppId, String url) {
        ImageUrlRequest urlRequest = ImageUrlRequest
                .newBuilder()
                .setAppId(botAppId)
                .setUrl(url)
                .build();
        return client.uploadLarkImage(urlRequest);
    }

    public MessageResultSingleForCustomResponse getMessagePageByExtraId(String extraId, Long page, int size) {
        MessageExtraIdPageRequest messageExtraIdPageRequest = MessageExtraIdPageRequest
                .newBuilder()
                .setExtraId(extraId)
                .setPage(page)
                .setSize(size)
                .build();
        return client.getMessagePageByExtraId(messageExtraIdPageRequest);
    }

    public FinalContentResponse getFinalMessage(String extraId, String username) {
        ExtraIdUsernameRequest.Builder builder = ExtraIdUsernameRequest.newBuilder();
        builder.setExtraId(extraId).setUsername(username);
        return client.getFinalContent(builder.build());
    }
}
