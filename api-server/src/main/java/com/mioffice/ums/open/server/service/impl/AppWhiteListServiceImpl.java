package com.mioffice.ums.open.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.open.server.entity.info.AppWhiteListInfo;
import com.mioffice.ums.open.server.entity.info.BaseEntity;
import com.mioffice.ums.open.server.mapper.AppWhiteListInfoMapper;
import com.mioffice.ums.open.server.service.AppWhiteListService;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * AppWhiteListServiceImpl
 * <AUTHOR>
 * @since 2021-12-24
 */
@Service
public class AppWhiteListServiceImpl implements AppWhiteListService {
    @Autowired
    private AppWhiteListInfoMapper appWhiteListInfoMapper;
    private final String MESSAGE = "appId不能为空";

    @Override
    public int addBatchAppWhiteList(AddWhiteListRequest addWhiteListRequest) {
        Assert.notNull(addWhiteListRequest.getAppId(), MESSAGE);
        List<AppWhiteListInfo> appWhiteListInfos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(addWhiteListRequest.getWhiteListsList())) {
            for (WhiteList whiteList : addWhiteListRequest.getWhiteListsList()) {
                AppWhiteListInfo appWhiteListInfo = AppWhiteListInfo.newCreateAndUpdateTimeInstant();
                appWhiteListInfo.setAppId(addWhiteListRequest.getAppId());
                appWhiteListInfo.setChannel((byte) whiteList.getChannel());
                Assert.notNull(whiteList.getWhiteId(), "whiteId不能为空");
                appWhiteListInfo.setWhiteId(whiteList.getWhiteId());
                appWhiteListInfos.add(appWhiteListInfo);
            }

            //去重（同channel和同whiteId)
            ArrayList<AppWhiteListInfo> insertList = appWhiteListInfos.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getChannel() + ";" + o.getWhiteId()))), ArrayList::new));
            AppWhiteListInfo updateTimeEntity = AppWhiteListInfo.newUpdateTimeInstant();

            //批量插入
            appWhiteListInfoMapper.batchInsertUpdate(insertList, updateTimeEntity);
            return insertList.size();
        }
        return 0;
    }

    @Override
    public int deleteBatchAppWhiteList(DeleteWhiteListRequest deleteWhiteListRequest) {
        Assert.notNull(deleteWhiteListRequest.getAppId(), MESSAGE);

        List<Long> idList = deleteWhiteListRequest.getIdList();
        if (CollectionUtil.isEmpty(idList)) {
            return 0;
        }

        return appWhiteListInfoMapper.delete(Wrappers.<AppWhiteListInfo>lambdaQuery()
                .eq(AppWhiteListInfo::getAppId, deleteWhiteListRequest.getAppId())
                .in(BaseEntity::getId, idList));
    }

    @Override
    public Page<AppWhiteListInfo> queryAppWhiteList(QueryWhiteListRequest queryWhiteListRequest) {
        Assert.notNull(queryWhiteListRequest.getAppId(), MESSAGE);
        Page<AppWhiteListInfo> page = new Page<>(queryWhiteListRequest.getPage(), queryWhiteListRequest.getSize());
        return appWhiteListInfoMapper.selectPage(page, Wrappers.<AppWhiteListInfo>lambdaQuery()
                .eq(AppWhiteListInfo::getAppId, queryWhiteListRequest.getAppId())
                .eq(!ObjectUtil.equal(queryWhiteListRequest.getChannel(), 0), AppWhiteListInfo::getChannel, queryWhiteListRequest.getChannel())
                .eq(StringUtils.isNotBlank(queryWhiteListRequest.getWhiteId()), AppWhiteListInfo::getWhiteId, queryWhiteListRequest.getWhiteId()));
    }



}
