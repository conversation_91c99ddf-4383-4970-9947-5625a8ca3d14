package com.mioffice.ums.open.server.web.filter;

import com.google.gson.Gson;
import com.mioffice.ums.open.common.constats.AuthConst;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.util.HmacUtil;
import com.mioffice.ums.open.server.service.AppInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

import static com.mioffice.ums.open.common.constats.ResponseCode.*;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Component
@Slf4j
public class AuthFilter extends OncePerRequestFilter {
    @Autowired
    Gson gson;
    @Autowired
    AppInfoService appInfoService;

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    private static final String POST = "POST";

    private static final List<String> OPEN_PATTERN_LIST = Arrays.asList("/health", "/open/**");

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        if (isOpenApi(request.getServletPath())) {
            filterChain.doFilter(request, response);
            return;
        }

        String appId = request.getHeader(AuthConst.HEADER_APP_ID);
        String signature = request.getHeader(AuthConst.HEADER_SIG);

        if (notExist(response, appId)) {
            return;
        }

        String secret = appInfoService.getSecret(appId);
        if (StringUtils.isEmpty(secret)) {
            handlerResponse(response, NO_APP);
            return;
        }

        String body;

        // 区分post get
        if (POST.equals(request.getMethod())) {
            RequestWrapper wrapper = new RequestWrapper(request);
            body = wrapper.getBody();
            request = wrapper;
        } else {
            body = request.getQueryString();
        }

        String hmacSha256 = HmacUtil.getHmacSha256(secret, body);
        if (!StringUtils.equals(hmacSha256, signature)) {
            handlerResponse(response, AUTH_ERROR);
            return;
        }
        log.info("auth success appId: {}", appId);
        filterChain.doFilter(request, response);
    }

    private boolean notExist(HttpServletResponse response, String appId) throws IOException {
        if (StringUtils.isEmpty(appId)) {
            handlerResponse(response, PARAM_ERROR);
            return true;
        }
        return false;
    }

    private void handlerResponse(HttpServletResponse response, ResponseCode code) throws IOException {
        response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.getWriter().write(gson.toJson(UmsResponse.valueOf(code)));
        response.getWriter().flush();
    }

    private boolean isOpenApi(String servletPath) {
        for (String pattern : OPEN_PATTERN_LIST) {
            if (antPathMatcher.match(pattern, servletPath)) {
                return true;
            }
        }

        return false;
    }
}
