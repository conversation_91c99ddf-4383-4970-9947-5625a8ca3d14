package com.mioffice.ums.open.server.entity.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/16
 */
@Data
public class AppInfoBo {
    private long id;
    private String appName;
    private List<DeptBo> depts;
    private List<AppManagerBo> appManagers;
    private int qps;
    private List<MessageChannelBo> channels;
    private String operatorName;
    private String operatorUsername;
    private String appId;
    private String appSecret;
    private int appSysStatus;
    private Long createTime;
    private int alarmStatus;
    private int alarmThreshold;
    private int deduplicateStatus;
    private Byte whiteStatus;
}
