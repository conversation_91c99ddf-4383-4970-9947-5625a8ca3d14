package com.mioffice.ums.open.server.limiter;

import com.mioffice.ums.open.common.exception.LimitException;
import com.mioffice.ums.open.server.entity.bo.RateConfigBO;

/**
 * <AUTHOR>
 * @since 2020/9/18
 */
public interface DistributedRateLimiter {
    /**
     * acquire
     *
     * @param timeout ms
     */
    void acquire(long timeout) throws LimitException;

    /**
     * start
     */
    void start();

    /**
     * set rate
     *
     * @param rate qps
     */
    void setRate(RateConfigBO rateConf);
}
