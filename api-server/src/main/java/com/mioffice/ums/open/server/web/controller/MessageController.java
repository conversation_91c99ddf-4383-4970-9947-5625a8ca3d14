package com.mioffice.ums.open.server.web.controller;

import com.mioffice.ums.open.common.constats.AuthConst;
import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.message.AppPushResult;
import com.mioffice.ums.open.common.message.BatchMessage;
import com.mioffice.ums.open.common.message.FinalMsgResult;
import com.mioffice.ums.open.common.message.ImageResult;
import com.mioffice.ums.open.common.message.ImageUrl;
import com.mioffice.ums.open.common.message.MsgResult;
import com.mioffice.ums.open.common.message.MsgUser;
import com.mioffice.ums.open.server.service.MessageService;
import com.mioffice.ums.open.server.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Slf4j
@RestController
@RequestMapping("api")
public class MessageController {

    private final MessageService messageService;

    public MessageController(MessageService messageService) {
        this.messageService = messageService;
    }

    /**
     * 批量发送消息
     *
     * @param batchMessage
     * @return
     */
    @PostMapping("message/send/batch")
    public UmsResponse<MsgResult> sendBatch(@RequestBody BatchMessage batchMessage, HttpServletRequest request) {
        String sysId = request.getHeader(AuthConst.HEADER_APP_ID);
        Assert.hasText(batchMessage.getBotAppId(), "botAppId不能为空");
        return messageService.sendBatch(batchMessage, sysId);
    }

    /**
     * 使用 机器人的 botBizId 来发送消息
     *
     * @param batchMessage
     * @param request
     * @return
     */
    @PostMapping("v2/message/send/batch")
    public UmsResponse<MsgResult> sendBatchV2(@RequestBody BatchMessage batchMessage, HttpServletRequest request) {
        String sysId = request.getHeader(AuthConst.HEADER_APP_ID);
        Assert.hasText(batchMessage.getBotBizId(), "botBizId不能为空");
        Assert.isTrue(!batchMessage.getUserList().isEmpty(), "用户列表不能为空");
        Assert.hasText(batchMessage.getTemplateBizId(), "木本不能为空");
        Assert.notNull(batchMessage.getChannel(), "消息渠道不能为空");

        MessageChannelEnum messageChannelEnum = MessageChannelEnum.get(batchMessage.getChannel());
        switch (messageChannelEnum) {
            case MI_WORK:
                for (MsgUser msgUser : batchMessage.getUserList()) {
                    if (StringUtils.isBlank(msgUser.getUsername()) && StringUtils.isBlank(msgUser.getChatId()) && StringUtils.isBlank(msgUser.getEmail())) {
                        throw new IllegalArgumentException("MI_WORK 渠道，username 、email 和 chatId 不能同时为空");
                    }
                }
                break;
            case EMAIL:
                for (MsgUser msgUser : batchMessage.getUserList()) {
                    if (StringUtils.isBlank(msgUser.getEmail())) {
                        throw new IllegalArgumentException("EMAIL 渠道，email 不能为空");
                    }
                }
                break;
            case SMS:
                for (MsgUser msgUser : batchMessage.getUserList()) {
                    if (StringUtils.isBlank(msgUser.getPhone())) {
                        throw new IllegalArgumentException("SMS 渠道，phone 不能为空");
                    }
                }
                break;
            default:

        }

        return messageService.sendBatchV2(batchMessage, sysId, IdUtil.createUUID());
    }

    /**
     * 图片上传
     *
     * @param imageUrl
     * @param request
     * @return
     */
    @PostMapping("lark/image/upload")
    public UmsResponse<ImageResult> larkImageUpload(@RequestBody ImageUrl imageUrl, HttpServletRequest request) {
        String appId = request.getHeader(AuthConst.HEADER_APP_ID);
        Assert.hasText(imageUrl.getUrl(), "图片地址不能为空");
        Assert.hasText(imageUrl.getBotBizId(), "botBizId不能为空");

        return messageService.uploadLarkImage(imageUrl, appId);
    }

    @GetMapping("app/push/log/result")
    public UmsResponse<AppPushResult> pushLogResult(String groupId, HttpServletRequest request) {
        String appId = request.getHeader(AuthConst.HEADER_APP_ID);
        Assert.hasText(groupId, "组Id不能为空");
        return messageService.pushLogResult(groupId, appId);
    }

    @GetMapping("message/final/body/get")
    public UmsResponse<List<FinalMsgResult>> getFinalMessage(String groupId, String username) {
        return messageService.getFinalMessage(groupId, username);
    }

}
