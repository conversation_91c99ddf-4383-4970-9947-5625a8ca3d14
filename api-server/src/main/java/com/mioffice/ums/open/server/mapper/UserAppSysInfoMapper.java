package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.09.16 
 */
@Mapper
public interface UserAppSysInfoMapper extends BaseMapper<UserAppSysInfo> {
    int updateBatch(List<UserAppSysInfo> list);

    int batchInsert(@Param("list") List<UserAppSysInfo> list);

    int insertOrUpdate(UserAppSysInfo record);

    int insertOrUpdateSelective(UserAppSysInfo record);
}