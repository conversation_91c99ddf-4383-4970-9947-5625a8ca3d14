package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 消息规则信息
 * <AUTHOR>
 * @create 12 01,2022
 */

@Data
@TableName(value = "app_msg_rule_info")
public class AppMsgRuleInfo {

    @TableId(value = "id",type = IdType.AUTO )
    private Long id;

    @TableField(value = "rule_name")
    private String ruleName;

    @TableField(value = "app_sys_id")
    private Long appSysId;

    @TableField(value = "app_id")
    private String appId;

    @TableField(value = "callback")
    private String callback;

    @TableField(value = "create_time")
    private Long createTime;

    @TableField(value = "create_username")
    private String createUsername;

    @TableField(value = "update_time")
    private Long updateTime;

    @TableField(value = "update_username")
    private String updateUsername;

    @TableField(value = "placeholder")
    private String placeholder;

}
