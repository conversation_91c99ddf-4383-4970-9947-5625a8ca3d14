package com.mioffice.ums.open.server.service;


import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.server.bo.AppMsgRuleListBO;

import com.mioffice.ums.open.server.bo.GetMsgRulePageRequestBO;
import com.mioffice.ums.open.server.entity.bo.AddMsgRuleInfoRequestBO;
import com.mioffice.ums.open.server.entity.bo.AddMsgRuleInfoResultBO;
import com.mioffice.ums.open.server.entity.bo.CommonResultBO;
import com.mioffice.ums.open.server.entity.bo.UpdateMsgRuleRequestBO;
import com.mioffice.ums.open.server.entity.info.AppMsgRuleInfo;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.List;

public interface AppMsgRuleService {


    AppMsgRuleListBO messageRulePage(GetMsgRulePageRequestBO getMsgRulePageRequestBO);

    AddMsgRuleInfoResultBO addMessageRule(AddMsgRuleInfoRequestBO addMsgRuleInfoRequestBO);

    ImmutablePair<ResponseCode,String> updateMessageRule(UpdateMsgRuleRequestBO updateMsgRuleRequestBO);


    AppMsgRuleInfo getMessageRuleDetail(Long id);

    CommonResultBO deleteMessageRule(List<Long> idsList, String name, String username,
                                                List<String> roleList);
}
