package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.09.28 
 */

/**
 * 批次任务记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_sys_push_record_info")
public class AppSysPushRecordInfo extends BaseEntity {
    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)
     */
    @TableField(value = "channel")
    private Byte channel;

    /**
     * bot对应的appId
     */
    @TableField(value = "bot_app_id")
    private String botAppId;

    /**
     * 标题cn
     */
    @TableField(value = "title_cn")
    private String titleCn;

    /**
     * 标题en
     */
    @TableField(value = "title_en")
    private String titleEn;

    /**
     * 组id
     */
    @TableField(value = "extra_id")
    private String extraId;

    /**
     * 引擎模板id
     */
    @TableField(value = "template_id")
    private Long templateId;

    /**
     * 模板业务id
     */
    @TableField(value = "template_biz_id")
    private String templateBizId;

    /**
     * 总条数
     */
    @TableField(value = "all_count")
    private Long allCount;

    /**
     * 机器人业务id
     */
    @TableField(value = "bot_biz_id")
    private String botBizId;
}