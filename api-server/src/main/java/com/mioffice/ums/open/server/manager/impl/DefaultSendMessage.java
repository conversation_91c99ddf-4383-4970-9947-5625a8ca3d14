package com.mioffice.ums.open.server.manager.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.mioffice.ums.open.common.message.MsgUser;
import com.mioffice.ums.open.server.cache.AppSysTemplateCache;
import com.mioffice.ums.open.server.entity.bo.AppReportBO;
import com.mioffice.ums.open.server.entity.bo.MsgMd5RepeatBO;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.mioffice.ums.open.server.manager.SendMessage;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.mapper.UserAppSysInfoMapper;
import com.mioffice.ums.open.server.remote.MessageRpcClient;
import com.mioffice.ums.open.server.remote.UmsAdminRpcClient;
import com.mioffice.ums.open.server.utils.IdUtil;
import com.mioffice.ums.open.server.utils.MessageUtil;
import com.xiaomi.info.pb.app.mioffice.ums.admin.v1.EmployeeInfo;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUserResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.25
 */
@Slf4j
@Component
public class DefaultSendMessage implements SendMessage {

    @Value("${ums.open.bot-id}")
    private String botAppId;

    @Value("${ums.open.sms-sign}")
    private String smsSign;

    @Value("${ums.open.templates.lark-repeat-tmp-id}")
    private String larkRepeatTmpId;

    @Value("${ums.open.templates.sms-repeat-tmp-id}")
    private String smsRepeatTmpId;

    @Value("${ums.open.templates.lark-report-tmp-id}")
    private String larkReportTmpId;

    @Value("${ums.open.super-sys-admin-list}")
    private String[] superSysAdminList;

    @Autowired
    private AppSysTemplateCache appSysTemplateCache;

    @Autowired
    private MessageRpcClient messageRpcClient;

    @Autowired
    private UserAppSysInfoMapper userAppSysInfoMapper;

    @Autowired
    private AppSysInfoMapper appSysInfoMapper;

    @Autowired
    private UmsAdminRpcClient umsAdminRpcClient;

    @Override
    public void sendRepeatAlarmMsg(Map<String, List<MsgMd5RepeatBO>> appIdMap, Map<String, Integer> appIdMaxMap) {

        if (appIdMap.isEmpty()) {
            return;
        }

        Set<String> appIdList = appIdMap.keySet();

        // app 和 用户列表
        List<UserAppSysInfo> userAppSysInfos = userAppSysInfoMapper.selectList(
                Wrappers.<UserAppSysInfo>lambdaQuery().in(UserAppSysInfo::getAppId, appIdList));
        Map<String, List<UserAppSysInfo>> appIdAndUserMap = userAppSysInfos.stream().collect(
                Collectors.groupingBy(
                        UserAppSysInfo::getAppId,
                        Collectors.mapping(Function.identity(), Collectors.toList())
                )
        );

        // app 详情
        Map<String, AppSysInfo> appIdAndAppMap = appSysInfoMapper
                .selectList(Wrappers.<AppSysInfo>lambdaQuery().in(AppSysInfo::getAppId, appIdList))
                .stream().collect(Collectors.toMap(AppSysInfo::getAppId, Function.identity()));

        // 手机号获取
        List<String> allUsernameList =
                userAppSysInfos.stream().map(UserAppSysInfo::getUsername).collect(Collectors.toList());
        allUsernameList.addAll(Arrays.asList(superSysAdminList));
        List<EmployeeInfo> empList = umsAdminRpcClient.getEmpList(allUsernameList);
        Map<String, EmployeeInfo> userEmpMap = empList.stream()
                .collect(Collectors.toMap(EmployeeInfo::getUsername, Function.identity(), (v1, v2) -> v2));

        List<MessageUser> larkMessageUserList = new ArrayList<>();
        List<MessageUser> smsMessageUserList = new ArrayList<>();

        for (Map.Entry<String, List<MsgMd5RepeatBO>> entry : appIdMap.entrySet()) {
            String appId = entry.getKey();
            MsgMd5RepeatBO msgMd5RepeatBO = entry.getValue().get(0);
            // app 对应负责人列表
            List<UserAppSysInfo> userAppSysInfoList = appIdAndUserMap.get(appId);
            for (UserAppSysInfo userAppSysInfo : userAppSysInfoList) {
                larkMessageUserList.add(newLarkAlarmUser(appId, appIdAndAppMap.get(appId).getId(),
                        appIdAndAppMap.get(appId).getAppName(), appIdMaxMap.get(appId), userAppSysInfo.getUsername(),
                        msgMd5RepeatBO.getTemplateBizId()));
                if (userEmpMap.containsKey(userAppSysInfo.getUsername()) &&
                        StringUtils.isNotBlank(userEmpMap.get(userAppSysInfo.getUsername()).getPhone())) {
                    smsMessageUserList.add(newSmsAlarmUser(appId, appIdAndAppMap.get(appId).getId(),
                            appIdAndAppMap.get(appId).getAppName(), appIdMaxMap.get(appId),
                            userEmpMap.get(userAppSysInfo.getUsername()).getPhone(),
                            msgMd5RepeatBO.getTemplateBizId()));
                }

            }
            // 超管
            for (String username : superSysAdminList) {
                larkMessageUserList.add(newLarkAlarmUser(appId, appIdAndAppMap.get(appId).getId(),
                        appIdAndAppMap.get(appId).getAppName(), appIdMaxMap.get(appId), username,
                        msgMd5RepeatBO.getTemplateBizId()));
                if (userEmpMap.containsKey(username) && StringUtils.isNotBlank(userEmpMap.get(username).getPhone())) {
                    smsMessageUserList.add(newSmsAlarmUser(appId, appIdAndAppMap.get(appId).getId(),
                            appIdAndAppMap.get(appId).getAppName(), appIdMaxMap.get(appId),
                            userEmpMap.get(username).getPhone(), msgMd5RepeatBO.getTemplateBizId()));
                }
            }
        }

        // 飞书消息
        AppSysTemplateInfo appSysTemplateInfo = appSysTemplateCache.get(larkRepeatTmpId);
        MessageUserResponse messageUserResponse =
                messageRpcClient.sendMessageBatch(larkMessageUserList, appSysTemplateInfo.getTemplateId());
        log.info("发送飞书预警消息结果 code=[{}], message=[{}]", messageUserResponse.getCode(),
                messageUserResponse.getMessage());

        // 短信消息
        appSysTemplateInfo = appSysTemplateCache.get(smsRepeatTmpId);
        messageUserResponse = messageRpcClient.sendMessageBatch(smsMessageUserList, appSysTemplateInfo.getTemplateId());
        log.info("发送短信预警消息结果 code=[{}], message=[{}]", messageUserResponse.getCode(),
                messageUserResponse.getMessage());
    }

    private MessageUser newLarkAlarmUser(String sysId, long appSysId, String appName, int maxCount, String username,
                                         String templateBizId) {
        String extraId = IdUtil.createUUID();
        Map<String, Object> params = new HashMap<>(6);
        params.put("appName", appName);
        params.put("appId", sysId);
        params.put("appSysId", appSysId);
        params.put("maxCount", maxCount);
        params.put("date", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        params.put("extraId", extraId);
        params.put("templateBizId", templateBizId);

        MsgUser msgUser = new MsgUser();
        msgUser.setUsername(username);
        msgUser.setParams(params);

        MessageUser.Builder messageUserBuilder = MessageUtil.toConvertBuilder(botAppId, extraId, null, msgUser, sysId);
        // 原样内容进行固化
        messageUserBuilder.setContentFlag(1);
        return messageUserBuilder.build();
    }

    private MessageUser newSmsAlarmUser(String sysId, long appSysId, String appName, int maxCount, String phone,
                                        String templateBizId) {
        String extraId = IdUtil.createUUID();
        Map<String, Object> params = new HashMap<>(6);
        params.put("appName", appName);
        params.put("appId", sysId);
        params.put("appSysId", appSysId);
        params.put("maxCount", maxCount);
        params.put("date", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        params.put("extraId", extraId);
        params.put("templateBizId", templateBizId);

        MsgUser msgUser = new MsgUser();
        msgUser.setPhone(phone);
        msgUser.setParams(params);

        MessageUser.Builder messageUserBuilder = MessageUtil.toConvertBuilder(smsSign, extraId, null, msgUser, sysId);
        // 原样内容进行固化
        messageUserBuilder.setContentFlag(1);
        return messageUserBuilder.build();
    }

    @Override
    public void sendAppDailyReport(List<AppReportBO> appSysMonitorInfoList) {

        List<String> appIdList = appSysMonitorInfoList.stream().map(AppReportBO::getAppId).collect(Collectors.toList());
        Map<String, List<UserAppSysInfo>> appIdAndUserList = userAppSysInfoMapper.selectList(
                        Wrappers.<UserAppSysInfo>lambdaQuery().in(UserAppSysInfo::getAppId, appIdList))
                .stream()
                .collect(
                        Collectors.groupingBy(
                                UserAppSysInfo::getAppId,
                                Collectors.mapping(Function.identity(), Collectors.toList())
                        )
                );

        List<MessageUser> messageUserList = new ArrayList<>();
        for (AppReportBO appReportBO : appSysMonitorInfoList) {
            String appId = appReportBO.getAppId();
            // 负责人
            List<UserAppSysInfo> userAppSysInfos = appIdAndUserList.get(appId);
            List<MessageUser> messageUsers =
                    userAppSysInfos.stream().map(UserAppSysInfo::getUsername).map(u -> newReportUser(appReportBO, u))
                            .collect(Collectors.toList());
            messageUserList.addAll(messageUsers);
        }

        AppSysTemplateInfo appSysTemplateInfo = appSysTemplateCache.get(larkReportTmpId);
        Lists.partition(messageUserList, 500).forEach(batch -> {
            MessageUserResponse messageUserResponse =
                    messageRpcClient.sendMessageBatch(batch, appSysTemplateInfo.getTemplateId());
            log.info("发送日报消息结果 code=[{}], message=[{}]", messageUserResponse.getCode(),
                    messageUserResponse.getMessage());
        });
    }

    private MessageUser newReportUser(AppReportBO appReportBO, String username) {

        String extraId = IdUtil.createUUID();

        Map<String, Object> params = new HashMap<>(8);
        params.put("appId", appReportBO.getAppId());
        params.put("appName", appReportBO.getAppName());
        params.put("allCount", appReportBO.getAllCount());
        params.put("pushCount", appReportBO.getPushCount());
        params.put("failCount", appReportBO.getFailCount());
        params.put("date", appReportBO.getDate());
        params.put("successRate", appReportBO.getSuccessRate());

        MsgUser msgUser = new MsgUser();
        msgUser.setUsername(username);
        msgUser.setParams(params);

        MessageUser.Builder messageUserBuilder =
                MessageUtil.toConvertBuilder(botAppId, extraId, null, msgUser, appReportBO.getAppId());
        // 原样内容进行固化
        messageUserBuilder.setContentFlag(1);
        return messageUserBuilder.build();
    }

}
