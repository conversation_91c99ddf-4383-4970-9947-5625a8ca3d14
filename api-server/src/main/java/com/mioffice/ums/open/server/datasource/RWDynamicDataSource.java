package com.mioffice.ums.open.server.datasource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.11
 */
@Slf4j
public class RWDynamicDataSource extends AbstractRoutingDataSource {


    @Override
    protected Object determineCurrentLookupKey() {
        String name = RWDatasourceContext.INSTANCE.get();
        if (Objects.isNull(name)) {
            return RWDatasourceContext.MASTER;
        }
        return name;
    }
}
