package com.mioffice.ums.open.server.bo;

import com.mioffice.ums.open.server.entity.bo.TemplateListRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/22 1:27 下午
 * version: 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplateListBO {
    private Long current;
    private Long pages;
    private Boolean searchCount;
    private Long size;
    private Long total;
    private List<TemplateListRecord> records;

    public static TemplateListBO newEmptyTemplateListBO(Long page, Long size) {
        TemplateListBO templateListBO = new TemplateListBO();
        templateListBO.setCurrent(page);
        templateListBO.setSize(size);
        templateListBO.setPages(0L);
        templateListBO.setTotal(0L);
        templateListBO.setSearchCount(true);
        templateListBO.setRecords(Collections.emptyList());
        return templateListBO;
    }
}
