package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 应用申请记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_sys_apply_record_info")
public class AppSysApplyRecordInfo extends BaseEntity {
    /**
     * 审批状态 1-审核中，2-审核通过，3-审核拒绝
     */
    @TableField(value = "apply_status")
    private Byte applyStatus;

    /**
     * 申请说明
     */
    @TableField(value = "apply_desc")
    private String applyDesc;

    /**
     * 用户名
     */
    @TableField(value = "apply_user_username")
    private String applyUserUsername;

    /**
     * 姓名
     */
    @TableField(value = "apply_user_name")
    private String applyUserName;

    /**
     * 审批人
     */
    @TableField(value = "approval_username")
    private String approvalUsername;

    /**
     * 审批人姓名
     */
    @TableField(value = "approval_name")
    private String approvalName;

    /**
     * 审批说明
     */
    @TableField(value = "approval_desc")
    private String approvalDesc;

    /**
     * 系统id
     */
    @TableField(value = "app_sys_id")
    private Long appSysId;

    /**
     * 是否有效（0无效，1有效）
     */
    @TableField(value = "valid")
    private Byte valid;

    public static com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo newCreateAndUpdateTimeInstant() {
        com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo appSysApplyRecordInfo = new com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo();
        appSysApplyRecordInfo.setCreateTime(System.currentTimeMillis());
        appSysApplyRecordInfo.setUpdateTime(System.currentTimeMillis());
        return appSysApplyRecordInfo;
    }

    public static com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo newUpdateTimeInstant() {
        com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo appSysApplyRecordInfo = new com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo();
        appSysApplyRecordInfo.setUpdateTime(System.currentTimeMillis());
        return appSysApplyRecordInfo;
    }
}