package com.mioffice.ums.open.server.entity.bo;

import com.mioffice.ums.open.server.entity.info.AppTopicInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName RobotTopicListBO
 * @Description 机器人对应的topic列表
 * <AUTHOR>
 * @Date 2024/2/4 9:31
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RobotTopicListBO {

    private String robotId;

    private List<AppTopicInfo> appTopicInfoList;

}
