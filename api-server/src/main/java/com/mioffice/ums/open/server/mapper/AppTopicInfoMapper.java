package com.mioffice.ums.open.server.mapper;

import com.mioffice.ums.open.server.entity.info.AppTopicInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【app_topic_info(系统队列表)】的数据库操作Mapper
 * @createDate 2024-01-29 20:18:19
 * @Entity com.mioffice.ums.open.server.entity.info.AppTopicInfo
 */
public interface AppTopicInfoMapper extends BaseMapper<AppTopicInfo> {
    List<AppTopicInfo> getAvailableByRobotId(@Param("robotId") String roboId);
}




