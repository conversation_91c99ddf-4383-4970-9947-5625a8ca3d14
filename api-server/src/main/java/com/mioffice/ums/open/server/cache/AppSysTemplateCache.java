package com.mioffice.ums.open.server.cache;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo;
import com.mioffice.ums.open.server.mapper.AppSysTemplateInfoMapper;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.21
 */
@Component
public class AppSysTemplateCache {

    private static final int DEFAULT_MAXIMUM_SIZE = 500;

    private final AppSysTemplateInfoMapper appSysTemplateInfoMapper;
    private final LoadingCache<String, AppSysTemplateInfo> loadingCache;

    public AppSysTemplateCache(AppSysTemplateInfoMapper appSysTemplateInfoMapper) {
        this.appSysTemplateInfoMapper = appSysTemplateInfoMapper;
        this.loadingCache = CacheBuilder.newBuilder()
                .maximumSize(DEFAULT_MAXIMUM_SIZE)
                .build(new CacheLoader<String, AppSysTemplateInfo>() {
                    @Override
                    public AppSysTemplateInfo load(String id) throws Exception {
                        return AppSysTemplateCache.this.appSysTemplateInfoMapper.selectOne(
                                Wrappers.<AppSysTemplateInfo>lambdaQuery().eq(AppSysTemplateInfo::getBizId, id)
                        );
                    }
                });

    }

    public AppSysTemplateInfo get(String bizId) {
        try {
            return loadingCache.get(bizId);
        } catch (ExecutionException e) {
            return null;
        }
    }

    public void remove(String bizId) {
        loadingCache.invalidate(bizId);
    }
}
