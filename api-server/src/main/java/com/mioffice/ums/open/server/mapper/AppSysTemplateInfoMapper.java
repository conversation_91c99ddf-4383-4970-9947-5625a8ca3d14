package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/22 6:07 下午
 * version: 1.0.0
 */
public interface AppSysTemplateInfoMapper extends BaseMapper<AppSysTemplateInfo> {
    int updateBatch(List<AppSysTemplateInfo> list);

    int updateBatchSelective(List<AppSysTemplateInfo> list);

    int batchInsert(@Param("list") List<AppSysTemplateInfo> list);

    int insertOrUpdate(AppSysTemplateInfo record);

    int insertOrUpdateSelective(AppSysTemplateInfo record);
}