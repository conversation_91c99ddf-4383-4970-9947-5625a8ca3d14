package com.mioffice.ums.open.server.rpc;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.exception.BizException;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.message.BatchMessage;
import com.mioffice.ums.open.common.message.MsgResult;
import com.mioffice.ums.open.common.message.MsgUser;
import com.mioffice.ums.open.common.util.JsonUtils;
import com.mioffice.ums.open.server.entity.bo.AppInfoBo;
import com.mioffice.ums.open.server.entity.bo.AppInfoStatusBo;
import com.mioffice.ums.open.server.entity.info.AppWhiteListInfo;
import com.mioffice.ums.open.server.service.AppInfoService;
import com.mioffice.ums.open.server.service.AppWhiteListService;
import com.mioffice.ums.open.server.service.MessageService;
import com.mioffice.ums.open.server.utils.IdUtil;
import com.mioffice.ums.open.server.utils.MapperUtil;
import com.xiaomi.info.grpc.server.annotation.RpcServer;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020/9/16
 */
@RpcServer
@Slf4j
public class OpenServer extends OpenServerGrpc.OpenServerImplBase {
    @Autowired
    AppInfoService appInfoService;
    @Autowired
    AppWhiteListService appWhiteListService;

    @Autowired
    private MessageService messageService;

    @Override
    public void saveOrUpdate(AppInfo request, StreamObserver<AppInfoResponse> responseObserver) {
        AppInfoBo appInfoBo = MapperUtil.INSTANCE.mapToAppInfoBo(request);
        AppInfoResponse response;
        try {
            AppInfoStatusBo statusBo = appInfoService.saveOrUpdate(appInfoBo, request.getRoleList());
            response = AppInfoResponse.newBuilder()
                    .setCode(ResponseCode.SUCCESS.getCode())
                    .setDesc(ResponseCode.SUCCESS.getMsg())
                    .setId(statusBo.getId())
                    .setStatus(statusBo.getStatus())
                    .build();
        } catch (Exception e) {
            response = AppInfoResponse.newBuilder()
                    .setCode(ResponseCode.INTERNAL_ERROR.getCode())
                    .setDesc(e.getMessage())
                    .build();
        }

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void queryAppInfo(QueryAppInfoRequest request, StreamObserver<QueryAppInfoResponse> responseObserver) {
        int code = ResponseCode.SUCCESS.getCode();
        String msg = StringUtils.EMPTY;
        AppInfoBo appInfoBo = null;
        try {
            appInfoBo =
                    appInfoService.queryById(request.getId(), request.getUsername(), request.getRoleList());
            if (Objects.isNull(appInfoBo)) {
                code = ResponseCode.PARAM_ERROR.getCode();
                msg = ResponseCode.PARAM_ERROR.getMsg();
            }
        } catch (BizException ex) {
            code = ex.getCode().getCode();
            msg = ex.getMessage();
        } finally {
            QueryAppInfoResponse.Builder builder = QueryAppInfoResponse.newBuilder();
            builder.setCode(code).setDesc(msg);
            if (Objects.nonNull(appInfoBo)) {
                AppInfo appInfo = MapperUtil.INSTANCE.mapToAppInfo(appInfoBo);
                builder.setAppInfo(appInfo);
                builder.setStatus(appInfoBo.getAppSysStatus());
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    public void queryAppInfoByAppId(com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppInfoByAppIdRequest request,
                                    io.grpc.stub.StreamObserver<com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppInfoResponse> responseObserver) {
        AppInfoBo appInfoBo = appInfoService.queryByAppId(request.getAppId());
        QueryAppInfoResponse.Builder builder = QueryAppInfoResponse.newBuilder();

        if (appInfoBo == null) {
            responseObserver.onNext(builder
                    .setCode(ResponseCode.PARAM_ERROR.getCode())
                    .setDesc(ResponseCode.PARAM_ERROR.getMsg())
                    .build());
            responseObserver.onCompleted();
            return;
        }
        AppInfo appInfo = MapperUtil.INSTANCE.mapToAppInfo(appInfoBo);

        responseObserver.onNext(builder.setAppInfo(appInfo)
                .setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg())
                .setStatus(appInfoBo.getAppSysStatus())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateStatus(StatusRequest request, StreamObserver<AppInfoResponse> responseObserver) {
        appInfoService.updateStatus(request.getId(), request.getStatus());
        AppInfoResponse response = AppInfoResponse.newBuilder().setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg()).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void addAppWhiteList(AddWhiteListRequest request, StreamObserver<AddWhiteListResponse> responseObserver) {
        int count = appWhiteListService.addBatchAppWhiteList(request);
        AddWhiteListResponse response = AddWhiteListResponse.newBuilder()
                .setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg())
                .setCount(count)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void deleteAppWhiteList(DeleteWhiteListRequest request,
                                   StreamObserver<DeleteWhiteListResponse> responseObserver) {
        int count = appWhiteListService.deleteBatchAppWhiteList(request);
        DeleteWhiteListResponse response = DeleteWhiteListResponse.newBuilder()
                .setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg())
                .setCount(count)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void queryAppWhiteList(QueryWhiteListRequest request,
                                  StreamObserver<QueryWhiteListResponse> responseObserver) {
        IPage<AppWhiteListInfo> whiteListIPage = appWhiteListService.queryAppWhiteList(request);
        WhiteListPage whiteListPage = MapperUtil.INSTANCE.mapToQueryWhiteListResponse(whiteListIPage);
        QueryWhiteListResponse response = QueryWhiteListResponse.newBuilder()
                .setCode(ResponseCode.SUCCESS.getCode())
                .setDesc(ResponseCode.SUCCESS.getMsg())
                .setWhiteListPage(whiteListPage)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateWhiteStatus(UpdateWhiteStatusRequest request,
                                  StreamObserver<UpdateWhiteStatusResponse> responseObserver) {
        Assert.notNull(request.getId(), "id不能为空");
        Assert.notNull(request.getWhiteStatus(), "whiteStatus不能为空");
        boolean success = appInfoService.updateWhiteStatus(request.getId(), request.getWhiteStatus());
        UpdateWhiteStatusResponse response = UpdateWhiteStatusResponse
                .newBuilder().setCode(ResponseCode.SUCCESS.getCode()).setDesc(ResponseCode.SUCCESS.getMsg())
                .setSuccess(success).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void sendMessageBatch(com.xiaomi.info.pb.app.mioffice.ums.open.v1.SendMessageBatchRequest request,
                                 io.grpc.stub.StreamObserver<com.xiaomi.info.pb.app.mioffice.ums.open.v1.SendMessageBatchResponse> responseObserver) {
        String sysId = request.getAppId();
        BatchMessage batchMessage = MapperUtil.INSTANCE.mapToBatchMessage(request);

        batchMessage.getUserList().forEach(x -> x.setParams(StringUtils.isNotBlank(x.getPlaceholderContent()) ?
                JsonUtils.parse(x.getPlaceholderContent(), new TypeToken<Map<String, Object>>() {
                }.getType()) :
                Maps.newHashMap()));

        batchMessage.setContentFlag((byte) 1);

        SendMessageBatchResponse.Builder builder = SendMessageBatchResponse.newBuilder();
        try {
            checkMessageParam(batchMessage);
            UmsResponse<MsgResult> result = messageService.sendBatchV2(batchMessage, sysId,
                    StringUtils.isNotBlank(request.getExtraId()) ? request.getExtraId() :
                            IdUtil.createUUID());
            builder.setExtraId(result.getData().getGroupId());
        } catch (IllegalArgumentException e) {
            builder.setCode(ResponseCode.PARAM_ERROR.getCode()).setDesc(e.getMessage()).setSuccess(false);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        } catch (Exception e) {
            log.error("发送消息失败", e);
            builder.setCode(ResponseCode.INTERNAL_ERROR.getCode()).setDesc(ResponseCode.INTERNAL_ERROR.getMsg())
                    .setSuccess(false);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        builder.setCode(ResponseCode.SUCCESS.getCode()).setDesc(ResponseCode.SUCCESS.getMsg()).setSuccess(true);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    public void precheckBotAndTemplate(
            com.xiaomi.info.pb.app.mioffice.ums.open.v1.PrecheckBotAndTemplateRequest request,
            io.grpc.stub.StreamObserver<com.xiaomi.info.pb.app.mioffice.ums.open.v1.PrecheckBotAndTemplateResponse> responseObserver) {
        String appId = request.getAppId();
        String botId = request.getBotBizId();
        String templateId = request.getTemplateBizId();
        Integer channel = request.getChannel();
        ImmutablePair<Boolean, String> pair = messageService.precheckBotAndTemplate(appId, channel, botId, templateId);
        PrecheckBotAndTemplateResponse.Builder builder = PrecheckBotAndTemplateResponse.newBuilder();
        if (!pair.getLeft()) {
            builder.setCode(ResponseCode.PARAM_ERROR.getCode()).setDesc(pair.getRight()).setSuccess(false);
        } else {
            builder.setCode(ResponseCode.SUCCESS.getCode()).setDesc(pair.getRight()).setSuccess(true);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    public void checkMessageParam(BatchMessage batchMessage) {
        Assert.hasText(batchMessage.getBotBizId(), "botBizId不能为空");
        Assert.isTrue(!batchMessage.getUserList().isEmpty(), "用户列表不能为空");
        Assert.hasText(batchMessage.getTemplateBizId(), "模板不能为空");
        Assert.notNull(batchMessage.getChannel(), "消息渠道不能为空");
        MessageChannelEnum messageChannelEnum = MessageChannelEnum.get(batchMessage.getChannel());
        switch (messageChannelEnum) {
            case MI_WORK:
                for (MsgUser msgUser : batchMessage.getUserList()) {
                    if (StringUtils.isBlank(msgUser.getUsername()) && StringUtils.isBlank(msgUser.getChatId()) &&
                            StringUtils.isBlank(msgUser.getEmail())) {
                        throw new IllegalArgumentException("MI_WORK 渠道，username 、email 和 chatId 不能同时为空");
                    }
                }
                break;
            case EMAIL:
                for (MsgUser msgUser : batchMessage.getUserList()) {
                    if (StringUtils.isBlank(msgUser.getEmail())) {
                        throw new IllegalArgumentException("EMAIL 渠道，email 不能为空");
                    }
                }
                break;
            case SMS:
                for (MsgUser msgUser : batchMessage.getUserList()) {
                    if (StringUtils.isBlank(msgUser.getPhone())) {
                        throw new IllegalArgumentException("SMS 渠道，phone 不能为空");
                    }
                }
                break;
            default:
        }
    }

}

