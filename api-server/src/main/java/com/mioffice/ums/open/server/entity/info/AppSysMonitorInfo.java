package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 应用每天统计推送表
 * <AUTHOR>
 */
@Data
@TableName(value = "app_sys_monitor_info")
public class AppSysMonitorInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * app_sys_info 外键id
     */
    @TableField(value = "app_sys_id")
    private Long appSysId;

    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)
     */
    @TableField(value = "channel")
    private Byte channel;

    /**
     * 总条数
     */
    @TableField(value = "all_count")
    private Long allCount;

    /**
     * 推送成功条数
     */
    @TableField(value = "push_count")
    private Long pushCount;

    /**
     * 待推送条数
     */
    @TableField(value = "todo_count")
    private Long todoCount;

    /**
     * 失败条数
     */
    @TableField(value = "fail_count")
    private Long failCount;

    /**
     * 中断条数
     */
    @TableField(value = "interrupt_count")
    private Long interruptCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

    /**
     * 记录推送时间
     */
    @TableField(value = "push_date")
    private Long pushDate;
}