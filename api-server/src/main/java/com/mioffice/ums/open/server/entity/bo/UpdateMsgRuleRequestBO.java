package com.mioffice.ums.open.server.entity.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 12 04,2022
 */
@Data
public class UpdateMsgRuleRequestBO {

    /**
     * 规则ID
     * */
    private Long id;
    /**
     * 规则名字
     * */
    private String ruleName;
    /**
     * 回调地址
     * */
    private String callback;
    /**
     * 占位符
     * */
    private String placeholder;
    /**
     * 操作人ID
     * */
    private String username;

    /**
     * 角色列表
     * */
    private List<String> role;
}
