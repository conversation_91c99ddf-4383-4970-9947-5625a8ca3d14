package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;import com.baomidou.mybatisplus.extension.plugins.pagination.Page;import com.mioffice.ums.open.server.bo.AppSysPushRecordBo;import com.mioffice.ums.open.server.bo.AppTaskLogBo;import com.mioffice.ums.open.server.entity.bo.AppChannelCountBo;import com.mioffice.ums.open.server.entity.bo.AppMessageCountBo;import com.mioffice.ums.open.server.entity.info.AppSysPushRecordInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.28
 */
public interface AppSysPushRecordInfoMapper extends BaseMapper<AppSysPushRecordInfo> {
    int updateBatch(List<AppSysPushRecordInfo> list);

    int updateBatchSelective(List<AppSysPushRecordInfo> list);

    int batchInsert(@Param("list") List<AppSysPushRecordInfo> list);

    int insertOrUpdate(AppSysPushRecordInfo record);

    int insertOrUpdateSelective(AppSysPushRecordInfo record);

    List<AppSysPushRecordBo> selectAppPushRecords(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("appId") String appId);

    List<AppMessageCountBo> selectTopCount(@Param("topCount") Integer topCount, @Param("beginTime") Long beginTime, @Param("endTime") Long endTime);

    List<AppChannelCountBo> selectAppChannelCount(@Param("appSysIdList") List<Long> appSysIdList, @Param("beginTime") Long beginTime, @Param("endTime") Long endTime);

    IPage<AppTaskLogBo> selectAppTaskLog(Page<AppTaskLogBo> page, @Param("channels") List<Byte> channel, @Param("extraId") String extraId, @Param("appId") String appId, @Param("startTime") Long startTime, @Param("endTime") Long endTime);
}