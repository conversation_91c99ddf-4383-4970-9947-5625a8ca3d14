package com.mioffice.ums.open.server.limiter;

import com.mioffice.ums.open.server.config.LuaScriptConfig;
import com.mioffice.ums.open.server.entity.bo.RateConfigBO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @since 2020/8/25
 */
@Slf4j
@Component
public class LimiterManager {

    private final ConcurrentMap<String, DistributedRateLimiter> limitCache = new ConcurrentHashMap<>();

    final LuaScriptConfig luaScriptConfig;
    final RedissonClient redissonClient;

    public LimiterManager(RedissonClient redissonClient, LuaScriptConfig luaScriptConfig) {
        this.luaScriptConfig = luaScriptConfig;
        this.redissonClient = redissonClient;
    }

    public DistributedRateLimiter getLimiter(String limitKey) {
        return this.getLimiter(limitKey, null);
    }


    public DistributedRateLimiter getLimiter(String limitKey, RateConfigBO rateConfigBO) {
        return limitCache.computeIfAbsent(limitKey, k -> {
            log.info("create new RedisRateLimiter k: {}", k);
            return new RedisRateLimiter(redissonClient, k, luaScriptConfig.getScriptSha(), rateConfigBO);
        });
    }
}
