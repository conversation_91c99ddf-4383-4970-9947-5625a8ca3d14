package com.mioffice.ums.open.server.web.controller;

import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.server.cache.AppSysTemplateCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021.02.20
 */
@Slf4j
@RestController
@RequestMapping("/open")
public class OpenController {

    @Autowired
    private AppSysTemplateCache appSysTemplateCache;

    @GetMapping("/sync/remove/template/local")
    public UmsResponse<Object> syncRemoveLocalTemplateCache(String bizId) {
        appSysTemplateCache.remove(bizId);
        return UmsResponse.valueOf(ResponseCode.SUCCESS);
    }
}
