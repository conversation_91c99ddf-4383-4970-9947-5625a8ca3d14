package com.mioffice.ums.open.server.datasource;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;

/**
 * <p>
 * 数据库连接池 切换
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.11
 */
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class DatasourceInterceptor implements Interceptor {

    private List<String> updateActions = Arrays.asList("insert", "update", "delete");
    private List<String> queryActions = Arrays.asList("select");

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        boolean isTransactionActive = TransactionSynchronizationManager.isSynchronizationActive();
        if (isTransactionActive) {
            RWDatasourceContext.INSTANCE.master();
            return invocation.proceed();
        }

        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        // 获取 SQL 命令
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        String action = sqlCommandType.name().toLowerCase();

        if (updateActions.contains(action)) {
            RWDatasourceContext.INSTANCE.master();
        } else {
            RWDatasourceContext.INSTANCE.slave();
        }
        return invocation.proceed();
    }

    @Override
    public void setProperties(Properties properties) {
        // nothing
    }
}
