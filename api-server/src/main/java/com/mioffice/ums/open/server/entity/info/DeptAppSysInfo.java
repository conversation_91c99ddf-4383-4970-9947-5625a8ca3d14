package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *     应用部门表
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "dept_app_sys_info")
public class DeptAppSysInfo extends BaseEntity {

    /**
     * app_sys_info 外键id
     */
    @TableField(value = "app_sys_id")
    private Long appSysId;

    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 部门name
     */
    @TableField(value = "dept_name")
    private String deptName;

}