package com.mioffice.ums.open.server.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.open.server.entity.info.AppWhiteListInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddWhiteListRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.DeleteWhiteListRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryWhiteListRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateWhiteStatusRequest;

/**
 * <AUTHOR>
 * @since 2021/12/22
 */
public interface AppWhiteListService {

    /**
     * 添加白名单
     * @param addWhiteListRequest
     * @return 数量
     */
    int addBatchAppWhiteList(AddWhiteListRequest addWhiteListRequest);

    /**
     * 删除白名单
     * @param deleteWhiteListRequest
     * @return 数量
     */
    int deleteBatchAppWhiteList(DeleteWhiteListRequest deleteWhiteListRequest);

    /**
     * 查询白名单列表
     * @param queryWhiteListRequest
     * @return 分页
     */
    Page<AppWhiteListInfo> queryAppWhiteList(QueryWhiteListRequest queryWhiteListRequest);
}
