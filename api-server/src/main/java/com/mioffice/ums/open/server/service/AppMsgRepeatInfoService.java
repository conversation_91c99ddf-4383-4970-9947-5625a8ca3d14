package com.mioffice.ums.open.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.23
 */
public interface AppMsgRepeatInfoService extends IService<AppMsgRepeatInfo> {

    /**
     * 最近1h的
     */
    Long RECENT_TIME = 60 * 60 * 1000L;

    /**
     *
     */
    void scanRepeatMsg();

    /**
     * 统计日报（统计前一天的）
     */
    void reportAppByDay();

    /**
     * 删除过期的消息
     * 凌晨2点
     * @param timeMills
     */
    void deleteExpiredRepeatInfo(Long timeMills);
}
