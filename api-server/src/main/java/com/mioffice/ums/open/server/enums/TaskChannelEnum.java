package com.mioffice.ums.open.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 任务类型
 * </p>
 *
 * <AUTHOR>
 * @date 2020/8/12 20:12
 */
@Getter
@AllArgsConstructor
public enum TaskChannelEnum {
    /**
     * 消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)
     */
    NO("非法发布渠道", (byte) 0),
    CHANNEL_LARK("飞书", (byte) 1),
    CHANNEL_EMAIL("邮件", (byte) 2),
    CHANNEL_SMS("短信", (byte) 3),
    CHANNEL_MI_PUSH("MiPush", (byte) 4);

    private final String msg;

    private final byte code;

}
