package com.mioffice.ums.open.server.service;

import com.mioffice.ums.open.server.entity.bo.ApprovalResultBO;
import com.mioffice.ums.open.server.entity.bo.CommonResultBO;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalListInfo;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/21 3:59 上午
 * version: 1.0.0
 */
public interface AppApprovalListService {

    AppApprovalListInfo getAppApprovalList(String username, Long page, Long size, String appName, String managerUsernameList, String channel, String beginDate, String endDate);

    ApprovalResultBO appApprovalPass(String username, String name, Long id);

    ApprovalResultBO appApprovalReject(String username, String name, String reason, Long id);

    CommonResultBO appApplyCancel(String username, String name, Long id, List<String> roleList);
}
