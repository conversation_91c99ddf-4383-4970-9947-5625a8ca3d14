package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.open.server.entity.info.AppSysApplyRecordInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/25 11:28 上午
 * version: 1.0.0
 */
public interface AppSysApplyRecordInfoMapper extends BaseMapper<AppSysApplyRecordInfo> {
    int updateBatch(List<AppSysApplyRecordInfo> list);

    int updateBatchSelective(List<AppSysApplyRecordInfo> list);

    int batchInsert(@Param("list") List<AppSysApplyRecordInfo> list);

    int insertOrUpdate(AppSysApplyRecordInfo record);

    int insertOrUpdateSelective(AppSysApplyRecordInfo record);
}