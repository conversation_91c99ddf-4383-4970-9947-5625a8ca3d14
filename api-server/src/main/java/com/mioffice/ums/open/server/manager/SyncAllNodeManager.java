package com.mioffice.ums.open.server.manager;

import java.io.IOException;
import java.util.List;

import com.github.kevinsawicki.http.HttpRequest;
import com.xiaomi.info.infra.soa.SoaUtil;
import com.xiaomi.info.infra.soa.pojo.IpPort;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 提供同步本服务所有的节点同步操作
 * </p>
 *
 * <AUTHOR>
 * @date 2021.02.20
 */
@Slf4j
@Component
public class SyncAllNodeManager {


    @Value("${server.port}")
    private String serverPort;

    @Value("${mrpc.server.server-name}")
    private String serverName;




    public void syncRemoveTemplate(String bizId) throws IOException {
        List<IpPort> list = SoaUtil.getAddressesNoCache(serverName, "", "");
        log.info("获取ums-open-api实例列表 {}", list.toString());
        list.forEach(item->{
            String ip = item.getIp();
            try {
                HttpRequest httpRequest = HttpRequest.get(String.format("http://%s:%s/open/sync/remove/template/local?bizId=%s", ip, serverPort, bizId));
                int code = httpRequest.code();
                if (code == HttpStatus.OK.value()) {
                    log.info("删除远程模板成功 ip = [{}]", ip);
                } else {
                    log.info("删除远程模板失败 ip = [{}], code = [{}]", ip, code);
                }
            } catch (Exception e) {
                log.error("执行删除远程模板请求异常 ip = [{}]", ip, e);
            }
        });
    }
}
