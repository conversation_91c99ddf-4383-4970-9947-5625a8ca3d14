package com.mioffice.ums.open.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 系统状态
 * </p>
 *
 * <AUTHOR>
 * @since 2020/9/21 2:36
 */
@Getter
@AllArgsConstructor
public enum AppRecordValidStatusEnum {
    /**
     * 系统状态
     */
    NO("非法状态", (byte) 0),
    UN_VALID_STATUS_ENUM("无效", (byte) 1),
    VALID_STATUS_ENUM("有效", (byte) 2);


    private final String msg;

    private final byte code;

    public static String getMsgByCode(byte code) {
        AppRecordValidStatusEnum[] values = AppRecordValidStatusEnum.values();
        for (AppRecordValidStatusEnum appSysStatusEnum : values) {
            if (appSysStatusEnum.getCode() == code) {
                return appSysStatusEnum.getMsg();
            }
        }
        return "";
    }

    public static AppRecordValidStatusEnum getByCode(byte code) {
        for (AppRecordValidStatusEnum appSysStatusEnum : AppRecordValidStatusEnum.values()) {
            if (appSysStatusEnum.getCode() == code) {
                return appSysStatusEnum;
            }
        }
        return NO;
    }

}
