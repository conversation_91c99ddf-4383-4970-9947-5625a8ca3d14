package com.mioffice.ums.open.server.service;

import com.mioffice.ums.open.server.entity.bo.RobotTopicListBO;
import com.mioffice.ums.open.server.entity.info.AppTopicInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【app_topic_info(系统队列表)】的数据库操作Service
 * @createDate 2024-01-29 20:18:19
 */
public interface AppTopicInfoService extends IService<AppTopicInfo> {
    RobotTopicListBO getAvailableRobotTopicList(String robotId);

    Boolean createAppTopic(String topic,
                           String appId,
                           String createBy,
                           String untilLevel2Leader,
                           String itOperators,
                           String desc);

    Boolean cancelAppTopic(String appId, String updateBy);

    AppTopicInfo getAppTopic(String appId);
}
