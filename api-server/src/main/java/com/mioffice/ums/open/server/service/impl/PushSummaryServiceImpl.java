package com.mioffice.ums.open.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.open.server.bo.AppSysPushRecordBo;
import com.mioffice.ums.open.server.bo.AppTaskLogBo;
import com.mioffice.ums.open.server.bo.PushSummaryBo;
import com.mioffice.ums.open.server.entity.bo.AppChannelCountBo;
import com.mioffice.ums.open.server.entity.bo.AppMessageCountBo;
import com.mioffice.ums.open.server.entity.bo.SyncTimeScopeBo;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.entity.info.AppSysMonitorInfo;
import com.mioffice.ums.open.server.entity.info.AppSysPushRecordInfo;
import com.mioffice.ums.open.server.enums.TaskChannelEnum;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysMonitorInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysPushRecordInfoMapper;
import com.mioffice.ums.open.server.remote.MessageRpcClient;
import com.mioffice.ums.open.server.service.PushSummaryService;
import com.mioffice.ums.open.server.utils.LocalDateTimeUtil;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTimeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 推送概览
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/18 11:42 上午
 */
@Slf4j
@Service
public class PushSummaryServiceImpl extends BaseServiceImpl<AppSysMonitorInfoMapper, AppSysMonitorInfo> implements PushSummaryService {

    @Autowired
    private AppSysPushRecordInfoMapper appSysPushRecordInfoMapper;
    @Autowired
    private AppSysMonitorInfoMapper appSysMonitorInfoMapper;
    @Autowired
    private AppSysInfoMapper appSysInfoMapper;
    @Autowired
    private MessageRpcClient messageRpcClient;

    private final static int RECENT_7_DAYS = 7;

    private final static int RECENT_30_DAYS = 30;

    private final static int RECENT_1_YEAR = 365;

    private final static int SYNC_TODAY = 1;

    private final static int SYNC_YESTERDAY = 2;

    private final static int SYNC_ANY_TIME = 3;

    @Override
    public PushSummaryBo getUseSummary(List<Long> systemIds, String startTime, String endTime) {
        PushSummaryBo pushSummaryBo  = new PushSummaryBo();
        List<String> xAxisList = new ArrayList<>();
        Long startTimeLong = null;
        Long endTimeLong = null;
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            startTimeLong = LocalDateTimeUtil.getMonthStart();
            endTimeLong   = LocalDateTimeUtil.getMonthEnd();
            xAxisList =  getRecent30ChartAxis();
        }else {
            startTimeLong = LocalDateTimeUtil.getDayBeginLong(startTime);
            endTimeLong   = LocalDateTimeUtil.getDayEndLong(endTime);
            xAxisList = this.getChartAxis(startTime, endTime);
//            xAxisList =  getRecent30ChartAxis();
        }
        PushSummaryBo.PushDetail yAxis = new PushSummaryBo.PushDetail();
        List<Long> miWork = new ArrayList<>();
        List<Long> email = new ArrayList<>();
        List<Long> sms = new ArrayList<>();
        List<AppSysPushRecordBo> appSysPushRecordBos = appSysMonitorInfoMapper.selectAppPushRecords(startTimeLong, endTimeLong, systemIds);
        xAxisList.forEach(
            xAxis -> {
                Long miWorkPush = appSysPushRecordBos.
                        stream()
                        .filter(item -> xAxis.equals(item.getDateStr()))
                        .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode())
                        .mapToLong(AppSysPushRecordBo::getPushCount)
                        .sum();
                Long emailPush = appSysPushRecordBos.
                        stream()
                        .filter(item -> xAxis.equals(item.getDateStr()))
                        .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_EMAIL.getCode())
                        .mapToLong(AppSysPushRecordBo::getPushCount)
                        .sum();
                Long smsPush = appSysPushRecordBos.
                        stream()
                        .filter(item -> xAxis.equals(item.getDateStr()))
                        .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_SMS.getCode())
                        .mapToLong(AppSysPushRecordBo::getPushCount)
                        .sum();
                miWork.add(miWorkPush);
                email.add(emailPush);
                sms.add(smsPush);
            }
        );
        yAxis.setEmail(email);
        yAxis.setMiWork(miWork);
        yAxis.setSms(sms);
        pushSummaryBo.setXAxis(xAxisList);
        pushSummaryBo.setYAxis(yAxis);
        return pushSummaryBo;
    }

    @Override
    public IPage<AppTaskLogBo> getAppTaskLog(String extraId, Long systemId, List<Byte> channels, Integer page, Integer size) {
        Page<AppTaskLogBo> iPage = new Page<>(page, size);
        // 0. 获取应用对应的appId
        AppSysInfo appSysInfo = appSysInfoMapper.selectById(systemId);
        if (Objects.isNull(appSysInfo)){
            return iPage;
        }
        // 1. 先获取分页
        IPage<AppTaskLogBo> appTaskLogBoPage = appSysPushRecordInfoMapper.selectAppTaskLog(iPage, channels, extraId, appSysInfo.getAppId(), LocalDateTimeUtil.getSevenDaysAgo(), LocalDateTimeUtil.getTodayEndLong());
        try {
            List<AppTaskLogBo> appTaskLogBos = appTaskLogBoPage.getRecords();
            // 2. 去Engine查询发送失败的、成功的、待发的、数据
            List<String> appExtraIdList  = appTaskLogBos.stream().map(AppTaskLogBo::getExtraId).collect(Collectors.toList());
            if (!appExtraIdList.isEmpty()){
                MessageNumberAndTimeResponse messagePushRecords = messageRpcClient.getMessagePushRecord(appExtraIdList);
                List<MessageNumberAndTime> messageNumberAndTimeListList = messagePushRecords.getMessageNumberAndTimeListList();
                Map<String, MessageNumberAndTime> messageRecordsMap = messageNumberAndTimeListList.stream()
                        .collect(Collectors.toMap(MessageNumberAndTime::getExtraId, Function.identity(), (v1, v2) -> v2));
                appTaskLogBos.forEach(
                        appTaskLogBo -> {
                            appTaskLogBo.setSystemId(systemId);
                            appTaskLogBo.setFailCount(messageRecordsMap.get(appTaskLogBo.getExtraId()).getFailCount());
                            appTaskLogBo.setPushCount(messageRecordsMap.get(appTaskLogBo.getExtraId()).getPushCount());
                            appTaskLogBo.setTodoCount(messageRecordsMap.get(appTaskLogBo.getExtraId()).getTodoCount());
                            appTaskLogBo.setInterruptCount(messageRecordsMap.get(appTaskLogBo.getExtraId()).getInterruptCount());
                        }
                );
            }
        } catch (Exception e){
            log.warn("查询推送日志异常，原因:[{}]", e.getMessage());
        }
        return appTaskLogBoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncYesterdayAppPushRecords(Long startTime, Long endTime) {
        // 如果一次都么同步过，先同步所有
        Integer haveSyncOnce =  appSysMonitorInfoMapper.selectCount(
                Wrappers.<AppSysMonitorInfo>lambdaQuery()
        );
        if (haveSyncOnce == 0){
            this.syncAppPushRecordsWithStartEndTime(null, null);
        }else {
            if (Objects.nonNull(startTime) && Objects.nonNull(endTime)){
                this.startSync(startTime, endTime, SYNC_YESTERDAY);
            }else {
                Long yesterdayStart = LocalDateTimeUtil.getYesterdayBeginLong();
                Long yesterdayEnd   = LocalDateTimeUtil.getYesterdayEndLong();
                this.startSync(yesterdayStart, yesterdayEnd, SYNC_YESTERDAY);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncTodayAppPushRecords(Long startTime, Long endTime) {
        // 如果一次都么同步过，先同步所有
        Integer haveSyncOnce =  appSysMonitorInfoMapper.selectCount(
                Wrappers.<AppSysMonitorInfo>lambdaQuery()
        );
        if (haveSyncOnce == 0){
            this.syncAppPushRecordsWithStartEndTime(null, null);
        }else {
            if (Objects.nonNull(startTime) && Objects.nonNull(endTime)){
                this.startSync(startTime, endTime, SYNC_TODAY);
            }else {
                Long todayBeginLong = LocalDateTimeUtil.getTodayBeginLong();
                Long todayEndLong   = LocalDateTimeUtil.getTodayEndLong();
                this.startSync(todayBeginLong, todayEndLong, SYNC_TODAY);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncAppPushRecordsWithStartEndTime(Long startTime, Long endTime) {
        if (Objects.nonNull(startTime) && Objects.nonNull(endTime)) {
            this.startSync(startTime, endTime, SYNC_ANY_TIME);
        }else {
            List<SyncTimeScopeBo> syncTimeScopeBos = this.getSyncTimeScopeList();
            for (SyncTimeScopeBo syncTimeScopeBo : syncTimeScopeBos) {
                this.startSync(syncTimeScopeBo.getStartTime(), syncTimeScopeBo.getEndTime(), SYNC_ANY_TIME);
            }
        }
    }

    @Override
    public void deleteExpiredPushRecord(Long timeMills) {
        LocalDate now;
        if (timeMills == 0) {
            now = LocalDate.now();
        } else {
            now = Instant.ofEpochMilli(timeMills).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        }
        LocalDate localDate = now.minusDays(7);
        LocalDateTime beginLocalDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
        while (true) {
            LambdaQueryWrapper<AppSysPushRecordInfo> lambdaQueryWrapper = Wrappers.<AppSysPushRecordInfo>lambdaQuery()
                    .le(AppSysPushRecordInfo::getCreateTime, beginLocalDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
            lambdaQueryWrapper
                    .orderByAsc(AppSysPushRecordInfo::getId)
                    .last("limit 500");
            List<AppSysPushRecordInfo> appSysPushRecordInfos = appSysPushRecordInfoMapper.selectList(lambdaQueryWrapper);

            if (appSysPushRecordInfos.isEmpty()) {
                break;
            }

            List<Long> pushIds = appSysPushRecordInfos.stream().map(AppSysPushRecordInfo::getId).collect(Collectors.toList());
            appSysPushRecordInfoMapper.deleteBatchIds(pushIds);
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(100));
        }
    }

    private void startSync(Long startTime, Long endTime, int syncType){

        List<AppSysPushRecordInfo> appListRecordInfos = appSysPushRecordInfoMapper.selectList(
                Wrappers.<AppSysPushRecordInfo>lambdaQuery()
                        .ge(AppSysPushRecordInfo::getCreateTime, startTime)
                        .le(AppSysPushRecordInfo::getCreateTime, endTime)
        );
        if (!appListRecordInfos.isEmpty()) {

            List<String> extraIdList = appListRecordInfos.stream()
                    .map(AppSysPushRecordInfo::getExtraId)
                    .collect(Collectors.toList());

            List<String> appIdList = appListRecordInfos.stream()
                    .map(AppSysPushRecordInfo::getAppId)
                    .distinct()
                    .collect(Collectors.toList());

            // 1, 查询消息推送情况
            MessageNumberAndTimeResponse messagePushRecords = messageRpcClient.getMessagePushRecord(extraIdList);
            List<MessageNumberAndTime> messageNumberAndTimeListList = messagePushRecords.getMessageNumberAndTimeListList();

            // 2, 构造更新对象
            Map<String, MessageNumberAndTime> messageRecordsMap = messageNumberAndTimeListList.stream()
                    .collect(Collectors.toMap(MessageNumberAndTime::getExtraId, Function.identity(), (v1, v2) -> v2));

            Map<String, Long> systemInfoMap = appSysInfoMapper.selectList(Wrappers.<AppSysInfo>lambdaQuery()).stream()
                    .collect(Collectors.toMap(AppSysInfo::getAppId, AppSysInfo::getId));

            List<AppSysMonitorInfo> collectPushDetailList = new ArrayList<>();

            long pushDate = 0L;
            if (syncType == SYNC_YESTERDAY){
                pushDate   = LocalDateTimeUtil.getYesterdayBeginLong();
            }else if (syncType == SYNC_TODAY){
                pushDate   = LocalDateTimeUtil.getTodayBeginLong();
            }else if (syncType == SYNC_ANY_TIME){
                pushDate   = startTime;
            }

            Long curTime  = System.currentTimeMillis();

            Long finalPushDate = pushDate;

            appListRecordInfos.forEach(
                    appSysPushRecordInfo -> {
                        MessageNumberAndTime messageNumberAndTime = messageRecordsMap.get(appSysPushRecordInfo.getExtraId());
                        AppSysMonitorInfo appSysMonitorInfo = new AppSysMonitorInfo();
                        appSysMonitorInfo.setAppId(appSysPushRecordInfo.getAppId());
                        appSysMonitorInfo.setAppSysId(systemInfoMap.get(appSysPushRecordInfo.getAppId()));
                        appSysMonitorInfo.setChannel(appSysPushRecordInfo.getChannel());
                        appSysMonitorInfo.setAllCount(appSysPushRecordInfo.getAllCount());
                        appSysMonitorInfo.setFailCount(messageNumberAndTime.getFailCount());
                        appSysMonitorInfo.setTodoCount(messageNumberAndTime.getTodoCount());
                        appSysMonitorInfo.setPushCount(messageNumberAndTime.getPushCount());
                        appSysMonitorInfo.setInterruptCount(messageNumberAndTime.getInterruptCount());
                        appSysMonitorInfo.setCreateTime(curTime);
                        appSysMonitorInfo.setPushDate(finalPushDate);
                        collectPushDetailList.add(appSysMonitorInfo);
                    }
            );

            List<AppSysMonitorInfo> toSaveList = new ArrayList<>();
            List<AppSysMonitorInfo> toUpdateList = new ArrayList<>();
            List<AppSysMonitorInfo> hadSyncedMonitorList =  appSysMonitorInfoMapper.selectList(
                    Wrappers.<AppSysMonitorInfo>lambdaQuery()
//                            .ge(AppSysMonitorInfo::getCreateTime, LocalDateTimeUtil.getTodayBeginLong())
//                            .le(AppSysMonitorInfo::getCreateTime, LocalDateTimeUtil.getTodayEndLong())
                            .eq(AppSysMonitorInfo::getPushDate, pushDate)
            );
            boolean hadSynced = !hadSyncedMonitorList.isEmpty();

            appIdList.forEach(
                    appId -> {
                        List<AppSysMonitorInfo> miWorkAppMonitorList = collectPushDetailList.stream()
                                .filter(item -> item.getAppId().equals(appId))
                                .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode())
                                .collect(Collectors.toList());
                        List<AppSysMonitorInfo> emailAppMonitorList = collectPushDetailList.stream()
                                .filter(item -> item.getAppId().equals(appId))
                                .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_EMAIL.getCode())
                                .collect(Collectors.toList());
                        List<AppSysMonitorInfo> smsAppMonitorList = collectPushDetailList.stream()
                                .filter(item -> item.getAppId().equals(appId))
                                .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_SMS.getCode())
                                .collect(Collectors.toList());

                        if (!miWorkAppMonitorList.isEmpty()){
                            Long miWorkAllCount = miWorkAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getAllCount).sum();
                            Long miWorkPushCount = miWorkAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getPushCount).sum();
                            Long miWorkFailCount = miWorkAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getFailCount).sum();
                            Long miWorkInterruptCount = miWorkAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getInterruptCount).sum();
                            Long miWorkTodoCount = miWorkAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getTodoCount).sum();
                            AppSysMonitorInfo miWorkMonitor = miWorkAppMonitorList.get(0);
                            miWorkMonitor.setAllCount(miWorkAllCount);
                            miWorkMonitor.setPushCount(miWorkPushCount);
                            miWorkMonitor.setTodoCount(miWorkTodoCount);
                            miWorkMonitor.setFailCount(miWorkFailCount);
                            miWorkMonitor.setInterruptCount(miWorkInterruptCount);
                            if (hadSynced) {
                                List<AppSysMonitorInfo> hadSyncedMiWorkMonitor = hadSyncedMonitorList.stream()
                                        .filter(item -> item.getAppId().equals(miWorkMonitor.getAppId()))
                                        .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_LARK.getCode())
                                        .collect(Collectors.toList());
                                if (!hadSyncedMiWorkMonitor.isEmpty()){
                                    miWorkMonitor.setId(hadSyncedMiWorkMonitor.get(0).getId());
                                    miWorkMonitor.setUpdateTime(curTime);
                                    toUpdateList.add(miWorkMonitor);
                                }else {
                                    toSaveList.add(miWorkMonitor);
                                }
                            }else {
                                toSaveList.add(miWorkMonitor);
                            }
                        }

                        if (!emailAppMonitorList.isEmpty()){
                            Long emailAllCount = emailAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getAllCount).sum();
                            Long emailFailCount = emailAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getFailCount).sum();
                            Long emailInterruptAllCount = emailAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getInterruptCount).sum();
                            Long emailTodoCount = emailAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getTodoCount).sum();
                            Long miWorkPushCount = emailAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getPushCount).sum();
                            AppSysMonitorInfo emailMonitor = emailAppMonitorList.get(0);
                            emailMonitor.setAllCount(emailAllCount);
                            emailMonitor.setPushCount(miWorkPushCount);
                            emailMonitor.setTodoCount(emailTodoCount);
                            emailMonitor.setFailCount(emailFailCount);
                            emailMonitor.setInterruptCount(emailInterruptAllCount);
                            if (hadSynced) {
                                List<AppSysMonitorInfo> hadSyncedMiWorkMonitor = hadSyncedMonitorList.stream()
                                        .filter(item -> item.getAppId().equals(emailMonitor.getAppId()))
                                        .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_EMAIL.getCode())
                                        .collect(Collectors.toList());
                                if (!hadSyncedMiWorkMonitor.isEmpty()){
                                    emailMonitor.setId(hadSyncedMiWorkMonitor.get(0).getId());
                                    emailMonitor.setUpdateTime(curTime);
                                    toUpdateList.add(emailMonitor);
                                }else {
                                    toSaveList.add(emailMonitor);
                                }
                            }else {
                                toSaveList.add(emailMonitor);
                            }
                        }

                        if (!smsAppMonitorList.isEmpty()){
                            Long smsPushCount = smsAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getPushCount).sum();
                            Long smsAllCount = smsAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getAllCount).sum();
                            Long smsFailCount = smsAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getFailCount).sum();
                            Long smsInterruptCount = smsAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getInterruptCount).sum();
                            Long smsTodoCount = smsAppMonitorList.stream().mapToLong(AppSysMonitorInfo::getTodoCount).sum();
                            AppSysMonitorInfo smsMonitor = smsAppMonitorList.get(0);
                            smsMonitor.setAllCount(smsAllCount);
                            smsMonitor.setPushCount(smsPushCount);
                            smsMonitor.setTodoCount(smsTodoCount);
                            smsMonitor.setFailCount(smsFailCount);
                            smsMonitor.setInterruptCount(smsInterruptCount);
                            if (hadSynced) {
                                List<AppSysMonitorInfo> hadSyncedMiWorkMonitor = hadSyncedMonitorList.stream()
                                        .filter(item -> item.getAppId().equals(smsMonitor.getAppId()))
                                        .filter(item -> item.getChannel() == TaskChannelEnum.CHANNEL_SMS.getCode())
                                        .collect(Collectors.toList());
                                if (!hadSyncedMiWorkMonitor.isEmpty()){
                                    smsMonitor.setId(hadSyncedMiWorkMonitor.get(0).getId());
                                    smsMonitor.setUpdateTime(curTime);
                                    toUpdateList.add(smsMonitor);
                                }else {
                                    toSaveList.add(smsMonitor);
                                }
                            }else {
                                toSaveList.add(smsMonitor);
                            }
                        }

                    }
            );
            if (!toSaveList.isEmpty()){
                // 新增
                this.saveOrUpdateBatch(toSaveList);
            }
            if (!toUpdateList.isEmpty()){
                // 更新
                this.updateBatchById(toUpdateList);
            }
        }
    }


    public List<String> getChartAxis(String startTime, String endTime){

        List<String> axisList = new ArrayList<>();

        Long longStartTime = LocalDateTimeUtil.getLongTime(startTime, LocalDateTimeUtil.DATE_PATTERN);
        Long longEndTime = LocalDateTimeUtil.getLongTime(endTime, LocalDateTimeUtil.DATE_PATTERN);

        if (longStartTime > longEndTime){
            return axisList;
        }

        // 1. 判断几天
        int days = (int) ((longEndTime - longStartTime) / (1000 * 3600 * 24)) + 1;
        // 2. 取最早的一天
        LocalDate earlyDay = LocalDate.parse(startTime);
        LocalDate endDay = LocalDate.parse(endTime);

        if (days < RECENT_7_DAYS){
            // 小于7天就只拿一天的数据
//            axisList.add(earlyDay.toString());
            // 为了前端美观，也展示7天的时间轴，但是数据只为查询的天
            axisList.add(earlyDay.toString());
            for (int i = 1; i < 7; i++) {
                LocalDate day = earlyDay.plusDays(i);
                axisList.add(day.toString());
            }
        } else if (days == RECENT_7_DAYS){
            axisList.add(earlyDay.toString());
            for (int i = 1; i < 7; i++) {
                LocalDate day = earlyDay.plusDays(i);
                axisList.add(day.toString());
            }
        }else if (days == RECENT_30_DAYS){
            axisList.add(earlyDay.toString());
            for (int i = 1; i < 30; i++) {
                axisList.add(earlyDay.plusDays(i).toString());
            }
        }else if (days == RECENT_1_YEAR || days == RECENT_1_YEAR + 1){
            for (int i = 11; i > 0; i--) {
                LocalDate localDate = endDay.minusMonths(i);
                axisList.add(localDate.toString().substring(0,7));
            }
            axisList.add(endDay.toString().substring(0,7));
        }
        return axisList;
    }

    private List<String> getRecent30ChartAxis(){
        List<String> axisList = new ArrayList<>();
        LocalDate localDate = LocalDate.now();
        LocalDate monthStartDay = localDate.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate mothEndDay = localDate.with(TemporalAdjusters.lastDayOfMonth());
        int index = 1;
        axisList.add(monthStartDay.toString());
        while (true){
            LocalDate localDate1 = monthStartDay.plusDays(index);
            axisList.add(localDate1.toString());
            if (localDate1.toString().equals(mothEndDay.toString())){
                break;
            }
            index++;
        }
        return axisList;
    }

    @Override
    public List<AppMessageCountBo> getTopAppMsgList(int topCount, long beginTime, long endTime) {
        return appSysPushRecordInfoMapper.selectTopCount(topCount, beginTime, endTime);
    }

    @Override
    public List<AppChannelCountBo> getAppMsgChannelCount(List<Long> appSysIdList, long beginTime, long endTime) {
        List<AppChannelCountBo> appChannelCountBoList = appSysPushRecordInfoMapper.selectAppChannelCount(appSysIdList, beginTime, endTime);
        appChannelCountBoList.remove(null);
        return appChannelCountBoList;
    }

    private List<SyncTimeScopeBo> getSyncTimeScopeList(){
        List<SyncTimeScopeBo> syncTimeScopeList = new ArrayList<>();
        LocalDate earlyDay = LocalDate.of(2020, 9, 27);

        Long startTimeLong = LocalDateTimeUtil.getDayBeginLong(earlyDay.toString());
        Long endTimeLong   = LocalDateTimeUtil.getDayEndLong(earlyDay.toString());

        SyncTimeScopeBo syncTimeScope1 = new SyncTimeScopeBo();
        syncTimeScope1.setStartTime(startTimeLong);
        syncTimeScope1.setEndTime(endTimeLong);
        syncTimeScopeList.add(syncTimeScope1);

        for (int i = 1; i < 30; i++) {
            LocalDate localDate = earlyDay.plusDays(i);

            Long startTimeLong1 = LocalDateTimeUtil.getDayBeginLong(localDate.toString());
            Long endTimeLong1   = LocalDateTimeUtil.getDayEndLong(localDate.toString());

            SyncTimeScopeBo syncTimeScope2 = new SyncTimeScopeBo();
            syncTimeScope2.setStartTime(startTimeLong1);
            syncTimeScope2.setEndTime(endTimeLong1);

            syncTimeScopeList.add(syncTimeScope2);
        }
        return syncTimeScopeList;
    }
}
