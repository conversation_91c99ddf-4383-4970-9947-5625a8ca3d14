package com.mioffice.ums.open.server.config;

import com.mioffice.ums.open.common.exception.BizException;
import com.mioffice.ums.open.common.http.UmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2020/9/18
 */
@Slf4j
@RestControllerAdvice
public class GlobalControllerHandler {
    @ExceptionHandler
    public UmsResponse jsonErrorHandler(HttpServletRequest req, Throwable throwable) {
        log.error("{} {}", req.getRequestURI(), throwable);
        if (throwable instanceof BizException) {
            return UmsResponse.valueOf(((BizException) throwable).getCode());
        }
        return UmsResponse.valueOf(500, throwable.getMessage());
    }
}
