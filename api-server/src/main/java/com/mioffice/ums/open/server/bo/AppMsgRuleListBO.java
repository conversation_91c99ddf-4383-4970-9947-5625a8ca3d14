package com.mioffice.ums.open.server.bo;

import com.mioffice.ums.open.server.entity.bo.AppMsgRuleListRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 12 04,2022
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppMsgRuleListBO {
    private Long current;
    private Long pages;
    private Boolean searchCount;
    private Long size;
    private Long total;
    private List<AppMsgRuleListRecord> records;

    public static AppMsgRuleListBO newEmptyTemplateListBO(Long page, Long size) {
        AppMsgRuleListBO appMsgRuleListBO = new AppMsgRuleListBO();
        appMsgRuleListBO.setCurrent(page);
        appMsgRuleListBO.setSize(size);
        appMsgRuleListBO.setPages(0L);
        appMsgRuleListBO.setTotal(0L);
        appMsgRuleListBO.setSearchCount(true);
        appMsgRuleListBO.setRecords(Collections.emptyList());
        return appMsgRuleListBO;
    }
}
