package com.mioffice.ums.open.server.web.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Slf4j
public class RequestWrapper extends HttpServletRequestWrapper {
    private final String body;
    private InputStream inputStream;

    public RequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        body = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
        inputStream = new ByteArrayInputStream(body.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public ServletInputStream getInputStream() {
        return new DelegateInputStream(inputStream);
    }

    @Override
    public BufferedReader getReader() {
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    public String getBody() {
        return this.body;
    }
}
