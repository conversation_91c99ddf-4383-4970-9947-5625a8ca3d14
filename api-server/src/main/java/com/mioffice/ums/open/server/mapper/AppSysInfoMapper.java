package com.mioffice.ums.open.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;import com.mioffice.ums.open.server.entity.bo.AppApprovalListRecordInfoBO;import com.mioffice.ums.open.server.entity.bo.AppListRecordInfoBO;import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.01
 */
@Mapper
public interface AppSysInfoMapper extends BaseMapper<AppSysInfo> {
    int updateBatch(List<AppSysInfo> list);

    int updateBatchSelective(List<AppSysInfo> list);

    int batchInsert(@Param("list") List<AppSysInfo> list);

    int insertOrUpdate(AppSysInfo record);

    int insertOrUpdateSelective(AppSysInfo record);

    List<AppListRecordInfoBO> selectAppListPage(Page<AppListRecordInfoBO> appListRecordInfoPage, @Param("loginUsername") String loginUsername, @Param("appName") String appName, @Param("managerUsernameList") List<String> managerUsernameList, @Param("applyUsernameList") List<String> applyUsernameList, @Param("channelList") List<Byte> channelList, @Param("appSysStatusList") List<Byte> appSysStatusList, @Param("beginTime") long beginTime, @Param("endTime") long endTime);

    List<AppApprovalListRecordInfoBO> selectAppApprovalListPage(Page<AppApprovalListRecordInfoBO> dataPage, @Param("appName") String appName, @Param("managerUsernameList") List<String> managerList, @Param("channelList") List<Byte> channelList, @Param("beginTime") long beginTime, @Param("endTime") long endTime);
}