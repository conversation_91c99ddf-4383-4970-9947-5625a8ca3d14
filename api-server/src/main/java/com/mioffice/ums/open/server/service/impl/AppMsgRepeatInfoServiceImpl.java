package com.mioffice.ums.open.server.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.open.server.entity.bo.AppReportBO;
import com.mioffice.ums.open.server.entity.bo.MsgMd5RepeatBO;
import com.mioffice.ums.open.server.entity.info.AppMsgRepeatInfo;
import com.mioffice.ums.open.server.manager.SendMessage;
import com.mioffice.ums.open.server.mapper.AppMsgRepeatInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysMonitorInfoMapper;
import com.mioffice.ums.open.server.service.AppMsgRepeatInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.23
 */
@Slf4j
@Service
public class AppMsgRepeatInfoServiceImpl extends BaseServiceImpl<AppMsgRepeatInfoMapper, AppMsgRepeatInfo> implements AppMsgRepeatInfoService {

    @Autowired
    private AppMsgRepeatInfoMapper appMsgRepeatInfoMapper;

    @Autowired
    private SendMessage sendMessage;

    @Autowired
    private AppSysMonitorInfoMapper appSysMonitorInfoMapper;


    @Override
    public void scanRepeatMsg() {
        this.analysisContentMd5();
    }

    /**
     * md5(userId+ templateId+ params+ appId)
     * 进行分析，观察是否有重复
     */
    private void analysisContentMd5() {

        long beginTime = System.currentTimeMillis() - RECENT_TIME;
        long endTime = System.currentTimeMillis();
        List<MsgMd5RepeatBO> msgMd5RepeatBOList = appMsgRepeatInfoMapper.selectContentMd5CountByRecentTime(beginTime, endTime);

        Map<String, List<MsgMd5RepeatBO>> appIdMap = msgMd5RepeatBOList.stream().collect(
                Collectors.groupingBy(
                        MsgMd5RepeatBO::getAppId,
                        Collectors.mapping(Function.identity(), Collectors.toList())
                )
        );

        Map<String, Integer> appIdMaxMap = msgMd5RepeatBOList.stream().collect(
                Collectors.toMap(MsgMd5RepeatBO::getAppId, MsgMd5RepeatBO::getCount, (v1, v2) -> v1 > v2 ? v1 : v2)
        );

        ThreadUtil.execute(() -> sendMessage.sendRepeatAlarmMsg(appIdMap, appIdMaxMap));

    }

    @Override
    public void reportAppByDay() {

        DateTime dateTime = DateUtil.offsetDay(new Date(), -1);
        DateTime beginTime = DateUtil.beginOfDay(dateTime);
        DateTime endTime = DateUtil.endOfDay(dateTime);

        List<AppReportBO> appSysMonitorInfoList = appSysMonitorInfoMapper.selectReportData(beginTime.getTime(), endTime.getTime());

        if (appSysMonitorInfoList.isEmpty()) {
            return;
        }

        String date = DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN);

        appSysMonitorInfoList.forEach(p -> calRate(p, date));
        ThreadUtil.execute(() -> sendMessage.sendAppDailyReport(appSysMonitorInfoList));
    }

    @Override
    public void deleteExpiredRepeatInfo(Long timeMills) {
        LocalDate now;
        if (timeMills == 0) {
            now = LocalDate.now();
        } else {
            now = Instant.ofEpochMilli(timeMills).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        }
        LocalDate localDate = now.minusDays(7);
        LocalDateTime beginLocalDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
        while (true) {
            LambdaQueryWrapper<AppMsgRepeatInfo> lambdaQueryWrapper = Wrappers.<AppMsgRepeatInfo>lambdaQuery()
                    .le(AppMsgRepeatInfo::getCreateTime, beginLocalDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
            lambdaQueryWrapper
                    .orderByAsc(AppMsgRepeatInfo::getId)
                    .last("limit 500");

            List<AppMsgRepeatInfo> appMsgRepeatInfos = appMsgRepeatInfoMapper.selectList(lambdaQueryWrapper);
            if (appMsgRepeatInfos.isEmpty()) {
                break;
            }

            List<Long> repeatIds = appMsgRepeatInfos.stream().map(AppMsgRepeatInfo::getId).collect(Collectors.toList());
            appMsgRepeatInfoMapper.deleteBatchIds(repeatIds);

            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(100));
        }
    }

    private AppReportBO calRate(AppReportBO appReportBO, String date) {
        String successRate = String.format("%.2f", appReportBO.getPushCount() * 1.0 / appReportBO.getAllCount() * 100).concat("%");
        appReportBO.setSuccessRate(successRate);
        appReportBO.setDate(date);
        return appReportBO;
    }
}
