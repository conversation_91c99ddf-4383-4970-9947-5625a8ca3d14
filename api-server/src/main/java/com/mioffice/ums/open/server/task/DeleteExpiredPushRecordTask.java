package com.mioffice.ums.open.server.task;

import cn.hutool.core.thread.ThreadUtil;
import com.mioffice.ums.open.server.service.PushSummaryService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @since 2021.09.22
 */
@Slf4j
@PlanTask(name = "DeleteExpiredPushRecordTask", quartzCron = "0 0 2 * * ?", description = "删除过期的推送记录消息")
public class DeleteExpiredPushRecordTask implements PlanExecutor {

    private final PushSummaryService pushSummaryService;

    public DeleteExpiredPushRecordTask(PushSummaryService pushSummaryService) {
        this.pushSummaryService = pushSummaryService;
    }

    @Override
    public void execute() {
        String taskData = PlanThreadLocal.getRequestData();
        long timeMills;
        if (StringUtils.isNotBlank(taskData)) {
            timeMills = Long.parseLong(taskData);
        } else {
            timeMills = 0;
        }
        ThreadUtil.execute(
                () -> {
                    log.info("删除过期的推送记录消息任务开始前");
                    pushSummaryService.deleteExpiredPushRecord(timeMills);
                    log.info("删除过期的推送记录消息任务结束");
                }
        );
    }
}
