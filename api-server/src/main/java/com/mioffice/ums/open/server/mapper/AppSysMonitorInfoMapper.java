package com.mioffice.ums.open.server.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.open.server.bo.AppSysPushRecordBo;
import com.mioffice.ums.open.server.entity.bo.AppReportBO;
import com.mioffice.ums.open.server.entity.info.AppSysMonitorInfo;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface AppSysMonitorInfoMapper extends BaseMapper<AppSysMonitorInfo> {
    int updateBatch(List<AppSysMonitorInfo> list);

    int batchInsert(@Param("list") List<AppSysMonitorInfo> list);

    int insertOrUpdate(AppSysMonitorInfo record);

    int insertOrUpdateSelective(AppSysMonitorInfo record);

    List<AppSysPushRecordBo> selectAppPushRecords(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("systemIdList") List<Long> systemIdList);

    List<AppReportBO> selectReportData(@Param("beginTime") Long beginTime, @Param("endTime") Long endTime);
}