package com.mioffice.ums.open.server.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 应用拥有机器人资源表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_sys_bot_info")
public class AppSysBotInfo extends BaseEntity {
    /**
     * app_sys_info 外键id
     */
    @TableField(value = "app_sys_id")
    private Long appSysId;

    /**
     * 应用id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * bot对应的appId
     */
    @TableField(value = "bot_app_id")
    private String botAppId;

    /**
     * 消息发布渠道(1-飞书， 2-邮件，3-短信，4-MiPush)
     */
    @TableField(value = "channel")
    private Byte channel;

    /**
     * 是否发送外部(1 内部，2 外部， 3 内部和外部)
     */
    @TableField(value = "is_out_send")
    private Byte isOutSend;

    /**
     * 机器人业务id
     */
    @TableField(value = "bot_biz_id")
    private String botBizId;
}