package com.mioffice.ums.open.server.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mioffice.ums.open.common.constats.RedisConst;
import com.mioffice.ums.open.common.constats.ResponseCode;
import com.mioffice.ums.open.common.util.JsonUtils;
import com.mioffice.ums.open.server.bo.BotAppIdAndBotBizIdBO;
import com.mioffice.ums.open.server.bo.GetTemplatePageRequestBO;
import com.mioffice.ums.open.server.bo.TemplateListBO;
import com.mioffice.ums.open.server.entity.bo.AddTemplateRequestBO;
import com.mioffice.ums.open.server.entity.bo.AddTemplateResultBO;
import com.mioffice.ums.open.server.entity.bo.CommonResultBO;
import com.mioffice.ums.open.server.entity.bo.TemplateListRecord;
import com.mioffice.ums.open.server.entity.bo.UpdateTemplateRequestBO;
import com.mioffice.ums.open.server.entity.info.AppSysBotInfo;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.mioffice.ums.open.server.enums.AppSysStatusEnum;
import com.mioffice.ums.open.server.enums.AppUserRoleEnum;
import com.mioffice.ums.open.server.manager.SyncAllNodeManager;
import com.mioffice.ums.open.server.mapper.AppSysBotInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.mapper.AppSysTemplateInfoMapper;
import com.mioffice.ums.open.server.mapper.UserAppSysInfoMapper;
import com.mioffice.ums.open.server.remote.MessageRpcClient;
import com.mioffice.ums.open.server.service.AppSysTemplateService;
import com.xiaomi.info.infra.util.JsonUtil;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateId;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUserResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/22 5:22 下午
 * version: 1.0.0
 */
@Slf4j
@Service
public class AppSysTemplateServiceImpl extends ServiceImpl<AppSysTemplateInfoMapper,AppSysTemplateInfo> implements AppSysTemplateService {

    @Value("${lark.appId}")
    private String innerBotAppId;

    private final AppSysTemplateInfoMapper appSysTemplateInfoMapper;

    private final AppSysInfoMapper appSysInfoMapper;

    private final MessageRpcClient messageRpcClient;

    private final AppSysBotInfoMapper appSysBotInfoMapper;

    private final UserAppSysInfoMapper userAppSysInfoMapper;

    private final RedissonClient redissonClient;

    @Autowired
    private SyncAllNodeManager syncAllNodeManager;

    public AppSysTemplateServiceImpl(AppSysTemplateInfoMapper appSysTemplateInfoMapper, AppSysInfoMapper appSysInfoMapper, MessageRpcClient messageRpcClient, AppSysBotInfoMapper appSysBotInfoMapper, UserAppSysInfoMapper userAppSysInfoMapper, RedissonClient redissonClient) {
        this.appSysTemplateInfoMapper = appSysTemplateInfoMapper;
        this.appSysInfoMapper = appSysInfoMapper;
        this.messageRpcClient = messageRpcClient;
        this.appSysBotInfoMapper = appSysBotInfoMapper;
        this.userAppSysInfoMapper = userAppSysInfoMapper;
        this.redissonClient = redissonClient;
    }

    @Override
    public AppSysTemplateInfo getMessageTemplateDetail(Long id) {
        AppSysTemplateInfo appSysTemplateInfo = appSysTemplateInfoMapper.selectById(id);
        if (Objects.isNull(appSysTemplateInfo)) {
            log.error("id = [{}]的系统模板不存在", id);
        }
        return appSysTemplateInfo;
    }

    @Override
    public CommonResultBO deleteMessageTemplate(List<Long> idsList, String name, String username, List<String> roleList) {
        CommonResultBO commonResultBO = new CommonResultBO();
        try {
            List<AppSysTemplateInfo> appSysTemplateInfoList = appSysTemplateInfoMapper.selectBatchIds(idsList);
            List<Long> appSysIdList = appSysTemplateInfoList.stream().map(AppSysTemplateInfo::getAppSysId).collect(Collectors.toList());
            Map<Long, Long> templateIdAndAppSysIdMap = appSysTemplateInfoList.stream().collect(Collectors.toMap(AppSysTemplateInfo::getId, AppSysTemplateInfo::getAppSysId, (v1, v2) -> v2));
            List<Long> templateIdList = new ArrayList<>();
            if (!roleList.contains("ROLE_SYS_SUPER_ADMIN") && !roleList.contains("ROLE_SYS_ADMIN")) {
                List<UserAppSysInfo> userAppSysInfoList = userAppSysInfoMapper.selectList(
                        Wrappers.<UserAppSysInfo>lambdaQuery()
                                .in(UserAppSysInfo::getAppSysId, appSysIdList)
                                .eq(UserAppSysInfo::getUserType, AppUserRoleEnum.APP_USER_ROLE_CHARGE.getCode())
                );
                Map<Long, List<UserAppSysInfo>> appSysIdAndListMap = userAppSysInfoList.stream().collect(Collectors.groupingBy(UserAppSysInfo::getAppSysId));
                idsList.forEach(
                        id -> {
                            if (Objects.nonNull(templateIdAndAppSysIdMap.get(id))) {
                                List<UserAppSysInfo> userAppSysInfos = appSysIdAndListMap.get(templateIdAndAppSysIdMap.get(id));
                                boolean isHaveAccess = false;
                                for (UserAppSysInfo userAppSysInfo : userAppSysInfos) {
                                    if (username.equals(userAppSysInfo.getUsername())) {
                                        isHaveAccess = true;
                                        break;
                                    }
                                }
                                if (!isHaveAccess) {
                                    templateIdList.add(id);
                                }
                            }
                        }
                );
            }
            if (!templateIdList.isEmpty()) {
                commonResultBO.setCode(ResponseCode.AUTH_ERROR.getCode());
                commonResultBO.setIsSuccess(false);
                commonResultBO.setMessage("您没有id为".concat(JsonUtils.toJson(templateIdList)).concat("的模板删除权限"));
            } else {
                appSysTemplateInfoMapper.deleteBatchIds(idsList);
                commonResultBO.setIsSuccess(true);
            }
            return commonResultBO;
        } catch (Exception e) {
            log.error("idsList = [{}]", JsonUtils.toJson(idsList));
            commonResultBO.setIsSuccess(false);
            return commonResultBO;
        }
    }

    @Override
    public TemplateListBO getTemplatePage(GetTemplatePageRequestBO getTemplatePageRequestBO) {
        Long appSysId = getTemplatePageRequestBO.getAppSysId();
        Byte channel = getTemplatePageRequestBO.getChannel();
        Long page = getTemplatePageRequestBO.getPage();
        Long size = getTemplatePageRequestBO.getSize();
        String templateName = getTemplatePageRequestBO.getTemplateName();
        Page<AppSysTemplateInfo> appSysTemplateInfoPage = getAppSysTemplateInfoPage(appSysId, channel, page, size, templateName);

        List<AppSysTemplateInfo> records = appSysTemplateInfoPage.getRecords();

        if (records.isEmpty()) {
            return TemplateListBO.newEmptyTemplateListBO(page, size);
        }

        List<Long> appSysIdList = records.stream().map(AppSysTemplateInfo::getAppSysId).distinct().collect(Collectors.toList());

        List<AppSysBotInfo> appSysBotInfos = appSysBotInfoMapper.selectList(
                Wrappers.<AppSysBotInfo>lambdaQuery()
                        .eq(AppSysBotInfo::getChannel, channel).in(AppSysBotInfo::getAppSysId, appSysIdList)
        );

        TemplateListBO templateListBO = new TemplateListBO();

        List<TemplateListRecord> templateListRecordList = new ArrayList<>();
        records.forEach(
                record -> {
                    TemplateListRecord templateListRecord = new TemplateListRecord();
                    templateListRecord.setBizId(record.getBizId());
                    appSysBotInfos.forEach(
                            appSysBotInfo -> {
                                if (appSysBotInfo.getBotAppId().equals(record.getBotAppId()) && appSysBotInfo.getAppSysId().equals(record.getAppSysId()) && appSysBotInfo.getChannel().equals(record.getTemplateChannel())) {
                                    templateListRecord.setBotBizId(appSysBotInfo.getBotBizId());
                                }
                            }
                    );
                    templateListRecord.setBotAppId(record.getBotAppId());
                    templateListRecord.setCreateName(record.getCreateName());
                    templateListRecord.setCreateTime(record.getCreateTime());
                    templateListRecord.setCreateUsername(record.getCreateUsername());
                    templateListRecord.setId(record.getId());
                    templateListRecord.setTemplateName(record.getTemplateName());
                    templateListRecordList.add(templateListRecord);
                }
        );
        templateListBO.setSearchCount(appSysTemplateInfoPage.isSearchCount());
        templateListBO.setCurrent(appSysTemplateInfoPage.getCurrent());
        templateListBO.setPages(appSysTemplateInfoPage.getPages());
        templateListBO.setSize(appSysTemplateInfoPage.getSize());
        templateListBO.setTotal(appSysTemplateInfoPage.getTotal());
        templateListBO.setRecords(templateListRecordList);

        return templateListBO;
    }

    @Override
    public AddTemplateResultBO addTemplate(AddTemplateRequestBO addTemplateRequestBO) {
        AddTemplateResultBO addTemplateResultBO = new AddTemplateResultBO();
        String templateName = addTemplateRequestBO.getTemplateName();
        String templateContent = addTemplateRequestBO.getTemplateContent();
        List<String> botBizIds = addTemplateRequestBO.getBotBizIds();
        Long appSysId = addTemplateRequestBO.getAppSysId();
        Integer channel = addTemplateRequestBO.getChannel();
        String name = addTemplateRequestBO.getName();
        String username = addTemplateRequestBO.getUsername();
        List<String> roleList = addTemplateRequestBO.getRoleList();
        AppSysInfo appSysInfo = appSysInfoMapper.selectById(appSysId);
        if (!roleList.contains("ROLE_SYS_SUPER_ADMIN") && !roleList.contains("ROLE_SYS_ADMIN")) {
            List<UserAppSysInfo> userAppSysInfoList = userAppSysInfoMapper.selectList(
                    Wrappers.<UserAppSysInfo>lambdaQuery()
                            .eq(UserAppSysInfo::getAppSysId, appSysId)
                            .eq(UserAppSysInfo::getUserType, AppUserRoleEnum.APP_USER_ROLE_CHARGE.getCode())
            );
            boolean isHaveAccess = false;
            for (UserAppSysInfo userAppSysInfo : userAppSysInfoList) {
                if (username.equals(userAppSysInfo.getUsername())) {
                    isHaveAccess = true;
                    break;
                }
            }
            if (!isHaveAccess) {
                addTemplateResultBO.setAdd(false);
                addTemplateResultBO.setCode(ResponseCode.AUTH_ERROR.getCode());
                addTemplateResultBO.setMessage("用户没有权限访问此接口");
                return addTemplateResultBO;
            }
        }

        if (appSysInfo.getAppSysStatus() != AppSysStatusEnum.APPROVED.getCode()) {
            addTemplateResultBO.setAdd(false);
            addTemplateResultBO.setMessage("只有审核通过的系统才可创建模板");
            return addTemplateResultBO;
        }

        List<AppSysBotInfo> appSysBotInfoList = appSysBotInfoMapper.selectList(
                Wrappers.<AppSysBotInfo>lambdaQuery().in(AppSysBotInfo::getBotBizId, botBizIds)
        );

        Map<String, String> botBizIdAndBotAppIdMap = appSysBotInfoList.stream().distinct().collect(Collectors.toMap(AppSysBotInfo::getBotBizId, AppSysBotInfo::getBotAppId, (x, y) -> y));

        List<AppSysTemplateInfo> appSysTemplateInfoList = new ArrayList<>();
        String bizIdTmp = "";
        for (String botBizId : botBizIds) {
            MessageTemplateResponse messageTemplate;
            try {
                messageTemplate = messageRpcClient.createMessageTemplate(templateName, templateContent, botBizIdAndBotAppIdMap.get(botBizId), channel, appSysInfo.getAppId());
            } catch (Exception e) {
                log.error("调用创建模板出错", e);
                addTemplateResultBO.setAdd(false);
                addTemplateResultBO.setMessage("调用引擎层创建模板出错");
                return addTemplateResultBO;
            }
            int code = messageTemplate.getCode();
            String message = messageTemplate.getMessage();
            if (code != ResponseCode.SUCCESS.getCode()) {
                log.error("调用创建模板出错, 报错原因 = [{}]", message);
                addTemplateResultBO.setAdd(false);
                addTemplateResultBO.setMessage(message);
                return addTemplateResultBO;
            }
            MessageTemplateId messageTemplateId = messageTemplate.getMessageTemplateId();
            long templateId = messageTemplateId.getMessageTemplateId();

            AppSysBotInfo appSysBotInfo = appSysBotInfoMapper.selectOne(
                    Wrappers.<AppSysBotInfo>lambdaQuery()
                            .eq(AppSysBotInfo::getAppSysId, appSysId)
                            .eq(AppSysBotInfo::getBotAppId, botBizIdAndBotAppIdMap.get(botBizId))
                            .eq(AppSysBotInfo::getChannel, channel)
            );

            if (Objects.isNull(appSysBotInfo)) {
                log.error("创建模板错误");
                addTemplateResultBO.setAdd(false);
                addTemplateResultBO.setMessage("缺少机器人导致创建模板错误");
                return addTemplateResultBO;
            }
            String bizId = generateTemplateBizId(channel, appSysBotInfo);
            buildAppSysTemplateInfo(templateName, templateContent, appSysId, channel, name, username, appSysInfo, botBizIdAndBotAppIdMap, appSysTemplateInfoList, botBizId, templateId, bizId);
            bizIdTmp = bizIdTmp.concat(bizId).concat(",");
        }
        appSysTemplateInfoMapper.batchInsert(appSysTemplateInfoList);
        addTemplateResultBO.setAdd(true);
        addTemplateResultBO.setMessage("ok");
        addTemplateResultBO.setBizId(bizIdTmp);
        return addTemplateResultBO;
    }

    @Override
    public boolean updateTemplate(UpdateTemplateRequestBO updateTemplateRequestBO) {

        Long id = updateTemplateRequestBO.getId();

        AppSysTemplateInfo appSysTemplateInfo = appSysTemplateInfoMapper.selectById(id);
        if (Objects.isNull(appSysTemplateInfo)) {
            log.error("更新模板失败，模板不存在 id = [{}]", updateTemplateRequestBO.getId());
            return false;
        }

        String templateName = StringUtils.isBlank(updateTemplateRequestBO.getTemplateName()) ? appSysTemplateInfo.getTemplateName() : updateTemplateRequestBO.getTemplateName();
        String templateContent = StringUtils.isBlank(updateTemplateRequestBO.getTemplateContent()) ? appSysTemplateInfo.getTemplateContent() : updateTemplateRequestBO.getTemplateContent();
        String botBizId = StringUtils.isBlank(updateTemplateRequestBO.getBotBizId()) ? appSysTemplateInfo.getBotBizId() : updateTemplateRequestBO.getBotBizId();
        String botAppId = StringUtils.isBlank(updateTemplateRequestBO.getBotKey()) ? appSysTemplateInfo.getBotAppId() : updateTemplateRequestBO.getBotKey();


        AppSysBotInfo appSysBotInfo = appSysBotInfoMapper.selectOne(Wrappers.<AppSysBotInfo>lambdaQuery().eq(AppSysBotInfo::getBotBizId, botBizId).last("limit 1"));

        try {
            MessageTemplateResponse messageTemplateResponse = messageRpcClient.createMessageTemplate(templateName, templateContent, appSysBotInfo.getBotAppId(), appSysTemplateInfo.getTemplateChannel().intValue(), appSysTemplateInfo.getAppId());
            if (messageTemplateResponse.getCode() != 200) {
                log.error("调用创建模板出错 message = [{}]", messageTemplateResponse.getMessage());
                throw new IllegalArgumentException(messageTemplateResponse.getMessage());
            }

            AppSysTemplateInfo updateAppSysTemplateInfo = new AppSysTemplateInfo();
            updateAppSysTemplateInfo.setId(id);
            updateAppSysTemplateInfo.setTemplateName(templateName);
            updateAppSysTemplateInfo.setTemplateContent(templateContent);
            updateAppSysTemplateInfo.setBotBizId(botBizId);
            updateAppSysTemplateInfo.setBotAppId(botAppId);
            updateAppSysTemplateInfo.setUpdateTime(System.currentTimeMillis());
            updateAppSysTemplateInfo.setTemplateId(messageTemplateResponse.getMessageTemplateId().getMessageTemplateId());
            updateAppSysTemplateInfo.setUpdateUsername(updateTemplateRequestBO.getUpdateUsername());
            updateAppSysTemplateInfo.setUpdateName(updateTemplateRequestBO.getUpdateName());

            appSysTemplateInfoMapper.updateById(updateAppSysTemplateInfo);
            ThreadUtil.execute(() -> {
                try {
                    syncAllNodeManager.syncRemoveTemplate(appSysTemplateInfo.getBizId());
                } catch (IOException e) {
                    log.error("更新模板，同步其他节点失败", e);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("调用创建模板出错", e);
            throw new IllegalArgumentException(e.getMessage());
        }

    }

    private String generateTemplateBizId(Integer channel, AppSysBotInfo appSysBotInfo) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConst.GROUP_ID_COUNT_KEY.concat(appSysBotInfo.getBotBizId()));
        long incr = atomicLong.incrementAndGet();
        String bizId = "";
        if (channel == 1) {
            bizId = bizId.concat("TL").concat(appSysBotInfo.getBotBizId()).concat(String.format("%05d", incr));
        } else if (channel == 2) {
            bizId = bizId.concat("TE").concat(appSysBotInfo.getBotBizId()).concat(String.format("%05d", incr));
        } else if (channel == 3) {
            bizId = bizId.concat("TM").concat(appSysBotInfo.getBotBizId()).concat(String.format("%05d", incr));
        }
        return bizId;
    }

    private void buildAppSysTemplateInfo(String templateName, String templateContent, Long appSysId, Integer channel, String name, String username, AppSysInfo appSysInfo, Map<String, String> botBizIdAndBotAppIdMap, List<AppSysTemplateInfo> appSysTemplateInfoList, String botBizId, long templateId, String bizId) {
        AppSysTemplateInfo appSysTemplateInfoTmp = new AppSysTemplateInfo();
        appSysTemplateInfoTmp.setBizId(bizId);
        appSysTemplateInfoTmp.setAppSysId(appSysId);
        appSysTemplateInfoTmp.setBotBizId(botBizId);
        appSysTemplateInfoTmp.setAppId(appSysInfo.getAppId());
        appSysTemplateInfoTmp.setTemplateName(templateName);
        appSysTemplateInfoTmp.setBotAppId(botBizIdAndBotAppIdMap.get(botBizId));
        appSysTemplateInfoTmp.setTemplateContent(templateContent);
        appSysTemplateInfoTmp.setTemplateId(templateId);
        appSysTemplateInfoTmp.setCreateTime(System.currentTimeMillis());
        appSysTemplateInfoTmp.setUpdateTime(System.currentTimeMillis());
        appSysTemplateInfoTmp.setCreateUsername(username);
        appSysTemplateInfoTmp.setCreateName(name);
        appSysTemplateInfoTmp.setUpdateUsername(username);
        appSysTemplateInfoTmp.setUpdateName(name);
        appSysTemplateInfoTmp.setTemplateChannel(channel.byteValue());
        appSysTemplateInfoList.add(appSysTemplateInfoTmp);
    }

    @Override
    public List<BotAppIdAndBotBizIdBO> getSysBotList(long appSysId, int channel) {
        List<AppSysBotInfo> appSysBotInfoList = appSysBotInfoMapper.selectList(
                Wrappers.<AppSysBotInfo>lambdaQuery()
                        .eq(AppSysBotInfo::getAppSysId, appSysId).eq(AppSysBotInfo::getChannel, channel)
        );
        if (appSysBotInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        return appSysBotInfoList.stream().map(t -> new BotAppIdAndBotBizIdBO(t.getBotAppId(), t.getBotBizId())).distinct().collect(Collectors.toList());
    }

    private boolean innerMessage(AddTemplateResultBO addTemplateResultBO, long templateId) {
        try {
            MessageUser.Builder builder = MessageUser.newBuilder();
            builder.setUsername("yangguanlin");
            builder.setAppId(innerBotAppId);
            builder.setExtraId(UUID.randomUUID().toString().replace("-", ""));
            HashMap<String, Object> map = new HashMap<>();
            builder.setPlaceholderContent(JsonUtil.writeValueAsString(map));
            MessageUserResponse messageUserResponse = messageRpcClient.innerMessage(Collections.singletonList(builder.build()), templateId);
            int code1 = messageUserResponse.getCode();
            if (code1 != ResponseCode.SUCCESS.getCode()) {
                addTemplateResultBO.setAdd(false);
                addTemplateResultBO.setMessage("模板参数有误，请检查模板参数是否正确");
                log.warn("模板参数有误，错误为=[{}]", messageUserResponse.getMessage());
                return true;
            }
        } catch (Exception e) {
            log.error("创建模板发生异常", e);
            addTemplateResultBO.setAdd(false);
            addTemplateResultBO.setMessage("创建的模板有误");
            return true;
        }
        return false;
    }

    private Page<AppSysTemplateInfo> getAppSysTemplateInfoPage(Long appSysId, Byte channel, Long page, Long size, String templateName) {
        // page
        Page<AppSysTemplateInfo> taskInfoPage = new Page<>(page, size);

        LambdaQueryWrapper<AppSysTemplateInfo> lambdaQuery = Wrappers.lambdaQuery();

        List<AppSysBotInfo> appSysBotInfos = appSysBotInfoMapper.selectList(
                Wrappers.<AppSysBotInfo>lambdaQuery().eq(AppSysBotInfo::getAppSysId, appSysId).eq(AppSysBotInfo::getChannel, channel)
        );

        if (appSysBotInfos.isEmpty()) {
            taskInfoPage.setRecords(Collections.emptyList());
            taskInfoPage.setCurrent(page);
            taskInfoPage.setTotal(0L);
            taskInfoPage.setSize(size);
            taskInfoPage.setSearchCount(false);
            return taskInfoPage;
        }

        List<String> botAppIds = appSysBotInfos.stream().map(AppSysBotInfo::getBotAppId).distinct().collect(Collectors.toList());

        if (StringUtils.isNotBlank(templateName)) {
            lambdaQuery.and(
                    wrapper -> wrapper.like(AppSysTemplateInfo::getTemplateName, templateName)
            );
        }

        lambdaQuery.in(AppSysTemplateInfo::getBotAppId, botAppIds);

        lambdaQuery.eq(AppSysTemplateInfo::getAppSysId, appSysId);

        lambdaQuery.eq(AppSysTemplateInfo::getTemplateChannel, channel);

        lambdaQuery.orderByDesc(AppSysTemplateInfo::getId);

        return appSysTemplateInfoMapper.selectPage(taskInfoPage, lambdaQuery);
    }
}
