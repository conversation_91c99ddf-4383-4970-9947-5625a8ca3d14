package com.mioffice.ums.open.server.service.impl;

import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.mapper.AppSysPushRecordInfoMapper;
import com.mioffice.ums.open.server.service.AppListService;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * AppListService 测试类
 * 主要测试推送次数批量查询优化
 */
@Slf4j
public class AppListServiceImplTest extends BaseTest {

    @Autowired
    private AppListService appListService;

    @Autowired
    private AppSysPushRecordInfoMapper appSysPushRecordInfoMapper;

    /**
     * 测试批量查询推送次数的方法
     */
    @Test
    public void testSelectPushCountBatch() {
        // 准备测试数据 - 使用一些示例APP ID
        List<String> testAppIds = Arrays.asList("test_app_1", "test_app_2", "test_app_3");
        
        try {
            // 调用批量查询方法
            List<Map<String, Object>> results = appSysPushRecordInfoMapper.selectPushCountBatch(testAppIds);
            
            log.info("批量查询推送次数测试结果:");
            log.info("查询APP数量: {}", testAppIds.size());
            log.info("返回结果数量: {}", results.size());
            
            for (Map<String, Object> result : results) {
                String appId = (String) result.get("appId");
                Long pushCount = ((Number) result.get("pushCount")).longValue();
                log.info("APP ID: {}, 推送次数: {}", appId, pushCount);
            }
            
        } catch (Exception e) {
            log.error("批量查询推送次数测试失败", e);
        }
    }

    /**
     * 测试优化后的分页查询
     */
    @Test
    public void testGetAppListOptimized() {
        try {
            long startTime = System.currentTimeMillis();
            
            // 调用优化后的分页查询
            AppListInfo appListInfo = appListService.getAppList(
                    "test_user",           // loginName
                    "test_username",       // loginUsername  
                    1L,                    // page
                    10L,                   // size
                    null,                  // appName
                    null,                  // managerUsernameList
                    null,                  // applyUsername
                    null,                  // channel
                    null,                  // appSysStatus
                    null,                  // beginDate
                    null                   // endDate
            );
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("优化后的分页查询测试结果:");
            log.info("查询耗时: {} ms", duration);
            log.info("返回记录数: {}", appListInfo.getRecordsCount());
            log.info("总页数: {}", appListInfo.getPages());
            log.info("总记录数: {}", appListInfo.getTotal());
            
            // 检查推送次数是否正确设置
            appListInfo.getRecordsList().forEach(record -> {
                log.info("APP: {}, 推送次数: {}", record.getAppName(), record.getPushCount());
            });
            
        } catch (Exception e) {
            log.error("优化后的分页查询测试失败", e);
        }
    }
}
