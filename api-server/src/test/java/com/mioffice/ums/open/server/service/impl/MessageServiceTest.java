package com.mioffice.ums.open.server.service.impl;

import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.message.BatchMessage;
import com.mioffice.ums.open.common.message.FinalMsgResult;
import com.mioffice.ums.open.common.message.MsgResult;
import com.mioffice.ums.open.common.message.MsgUser;
import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.service.MessageService;
import com.mioffice.ums.open.server.utils.IdUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021.03.12
 */
public class MessageServiceTest extends BaseTest {

    @Autowired
    private MessageService messageService;

    @Test
    public void testGetFinal() {
        UmsResponse<List<FinalMsgResult>> umsResponse = messageService.getFinalMessage("295908b2187e47ada53483e719bbdbcc", "yangguanlin");

        System.out.println(umsResponse);
    }

    @Test
    public void testSendBatchV2() {
        BatchMessage batchMessage = new BatchMessage();
        batchMessage.setBotBizId("B0290");
        batchMessage.setChannel(MessageChannelEnum.MI_WORK.getType());
        batchMessage.setTemplateBizId("TLB029000020");

        Map<String, Object> params = new HashMap<>();
        params.put("title", "测试");
        params.put("content", "永远相信美好的事情即将发生");

        MsgUser msgUser1 = new MsgUser();
        msgUser1.setParams(params);
        msgUser1.setUsername("junfudong");
        msgUser1.setContentFlag(2);


        batchMessage.setUserList(Collections.singletonList(msgUser1));

        UmsResponse<MsgResult> umsResponse = messageService.sendBatchV2(batchMessage,"S00296", IdUtil.createUUID());

        System.out.println(umsResponse);
    }
}
