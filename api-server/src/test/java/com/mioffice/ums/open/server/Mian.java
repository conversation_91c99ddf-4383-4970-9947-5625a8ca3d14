package com.mioffice.ums.open.server;

import com.mioffice.ums.open.common.util.HmacUtil;
import com.mioffice.ums.open.common.util.JsonUtils;

import java.text.Format;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/9/25 4:55 下午
 */
public class Mian {

    public static void main(String[] args) {
        System.out.println(String.format("B%4d", 1120));

        System.out.println(String.format("B%04d", 120));
        System.out.println(String.format("B%04d", 10));

        System.out.println(JsonUtils.toJson(null));

        String ss= "{\"msg_type\":\"interactive\",\"update_multi\":false,\"card\":{\"config\":{\"wide_screen_mode\":true},\"header\":{\"title\":{\"tag\":\"plain_text\",\"content\":\"修改报名信息\"}},\"elements\":[{\"tag\":\"div\",\"text\":{\"tag\":\"plain_text\",\"content\":\"您已更新了战队“${teamName!}”的报名信息，可点击按钮查看报名详情。\"}},{\"tag\":\"action\",\"actions\":[{\"tag\":\"button\",\"text\":{\"tag\":\"plain_text\",\"content\":\"查看详情\"},\"type\":\"default\",\"url\":\"https://alpha.tech.xiaomi.cn/hacker/\"}]}]}}\n";
    }
}
