package com.mioffice.ums.open.server.service.impl;

import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.entity.bo.UpdateTemplateRequestBO;
import com.mioffice.ums.open.server.entity.info.AppSysTemplateInfo;
import com.mioffice.ums.open.server.manager.SyncAllNodeManager;
import com.mioffice.ums.open.server.mapper.AppSysTemplateInfoMapper;
import com.mioffice.ums.open.server.remote.MessageRpcClient;
import com.mioffice.ums.open.server.service.AppSysTemplateService;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageTemplateDetail;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;

import java.io.IOException;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/23 12:13 下午
 * version: 1.0.0
 */
@Slf4j
@EnableAutoConfiguration(exclude = {WebMvcAutoConfiguration.class})
public class TemplateCreateTest extends BaseTest {

    @Autowired
    private AppSysTemplateInfoMapper appSysTemplateInfoMapper;

    @Autowired
    private MessageRpcClient messageRpcClient;

    @Autowired
    private AppSysTemplateService appSysTemplateService;

    @Autowired
    private SyncAllNodeManager syncAllNodeManager;

    @Test
    public void test() {
        MessageTemplateRequest.Builder builder = MessageTemplateRequest.newBuilder();
        builder.setTitleCn("测试")
                .setContentCn("{\"content\":{\"i18n_elements\":{\"en_us\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"test message\",\"tag\":\"lark_md\"}}],\"zh_cn\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"测试消息\",\"tag\":\"lark_md\"}}],\"ja_jp\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"test message\",\"tag\":\"lark_md\"}}]},\"header\":{\"title\":{\"lines\":0,\"i18n\":{\"zh_cn\":\"【测试】测试\",\"ja_jp\":\"【test】test\",\"en_us\":\"【test】test\"},\"tag\":\"plain_text\"}},\"config\":{\"wide_screen_mode\":true}}}\n")
        .setMsgFormatType(2)
        .setAppId("cli_9e6998059bb59062")
        .setChannel(1)
        .setSystemId("S00005");
        MessageTemplateResponse messageTemplate = messageRpcClient.createMessageTemplate(
                "测试", "{\"content\":{\"i18n_elements\":{\"en_us\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"test message\",\"tag\":\"lark_md\"}}],\"zh_cn\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"测试消息\",\"tag\":\"lark_md\"}}],\"ja_jp\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"test message\",\"tag\":\"lark_md\"}}]},\"header\":{\"title\":{\"lines\":0,\"i18n\":{\"zh_cn\":\"【测试】测试\",\"ja_jp\":\"【test】test\",\"en_us\":\"【test】test\"},\"tag\":\"plain_text\"}},\"config\":{\"wide_screen_mode\":true}}}\n",
                "cli_9e6998059bb59062", 1, "S00005");
        System.out.println(messageTemplate.getMessageTemplateId());
    }

    @Test
    public void test1() {
        AppSysTemplateInfo appSysTemplateInfo = new AppSysTemplateInfo();
        appSysTemplateInfo.setBizId("TL00010002");
        appSysTemplateInfo.setAppSysId(11L);
        appSysTemplateInfo.setAppId("S00005");
        appSysTemplateInfo.setTemplateName("测试");
        appSysTemplateInfo.setBotAppId("cli_9e6998059bb59062");
        appSysTemplateInfo.setTemplateContent("{\"content\":{\"i18n_elements\":{\"en_us\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"test message\",\"tag\":\"lark_md\"}}],\"zh_cn\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"测试消息\",\"tag\":\"lark_md\"}}],\"ja_jp\":[{\"tag\":\"hr\"},{\"tag\":\"div\",\"text\":{\"lines\":0,\"content\":\"test message\",\"tag\":\"lark_md\"}}]},\"header\":{\"title\":{\"lines\":0,\"i18n\":{\"zh_cn\":\"【测试】测试\",\"ja_jp\":\"【test】test\",\"en_us\":\"【test】test\"},\"tag\":\"plain_text\"}},\"config\":{\"wide_screen_mode\":true}}}\n");
        appSysTemplateInfo.setTemplateId(2071L);
        appSysTemplateInfo.setCreateTime(System.currentTimeMillis());
        appSysTemplateInfo.setUpdateTime(System.currentTimeMillis());
        appSysTemplateInfo.setCreateUsername("niuwenyu");
        appSysTemplateInfo.setCreateName("牛文雨");
        appSysTemplateInfo.setTemplateChannel((byte)1);
        appSysTemplateInfoMapper.insert(appSysTemplateInfo);
    }

    @Test
    public void  updateTemplate() {

        UpdateTemplateRequestBO updateTemplateRequestBO = new UpdateTemplateRequestBO();
        updateTemplateRequestBO.setId(572L);
        updateTemplateRequestBO.setTemplateName("测试1");
        updateTemplateRequestBO.setTemplateContent("{\"msg_type\":\"interactive\",\"update_multi\":false,\"card\":{\"config\":{\"wide_screen_mode\":true},\"header\":{\"title\":{\"tag\":\"plain_text\",\"content\":\"修改报名信息111\"}},\"elements\":[{\"tag\":\"div\",\"text\":{\"tag\":\"plain_text\",\"content\":\"您已更新了战队“${teamName!}”的报名信息，可点击按钮查看报名详情。\"}},{\"tag\":\"action\",\"actions\":[{\"tag\":\"button\",\"text\":{\"tag\":\"plain_text\",\"content\":\"查看详情\"},\"type\":\"default\",\"url\":\"https://alpha.tech.xiaomi.cn/hacker/\"}]}]}}\n");

        boolean isUpdate = appSysTemplateService.updateTemplate(updateTemplateRequestBO);
        Assert.assertTrue(isUpdate);
    }

    @Test
    public void syncNeoNode() throws IOException {
        syncAllNodeManager.syncRemoveTemplate("1111");
    }
}
