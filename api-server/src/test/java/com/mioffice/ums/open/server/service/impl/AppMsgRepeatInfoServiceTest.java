package com.mioffice.ums.open.server.service.impl;

import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.service.AppMsgRepeatInfoService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.25
 */
@EnableAutoConfiguration(exclude = {WebMvcAutoConfiguration.class})
public class AppMsgRepeatInfoServiceTest extends BaseTest {

    @Autowired
    private AppMsgRepeatInfoService appMsgRepeatInfoService;

    @Test
    public void testScan() {
        appMsgRepeatInfoService.scanRepeatMsg();
    }

    @Test
    public void testReport() {
        appMsgRepeatInfoService.reportAppByDay();
    }
}
