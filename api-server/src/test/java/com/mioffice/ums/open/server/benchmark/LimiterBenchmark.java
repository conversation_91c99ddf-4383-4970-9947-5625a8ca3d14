package com.mioffice.ums.open.server.benchmark;

import com.mioffice.ums.open.common.exception.LimitException;
import com.mioffice.ums.open.server.config.LuaScriptConfig;
import com.mioffice.ums.open.server.limiter.RedisRateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;

import java.util.concurrent.TimeUnit;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.10.09
 */
@Slf4j
@BenchmarkMode(Mode.Throughput)
@OutputTimeUnit(TimeUnit.SECONDS)
@State(Scope.Thread)
public class LimiterBenchmark {

    private RedissonClient client;
    private RedisRateLimiter limiter;

    @Setup
    public void init() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://wcc.cache01.test.b2c.srv:22122")
                .setPassword("cn_info-application_cn_info-application_administration_notice_admin_svxri9jWvrSo")
                .setDatabase(0);
        client = Redisson.create(config);

        LuaScriptConfig luaScriptConfig = new LuaScriptConfig();


//        limiter = new RedisRateLimiter(client, "app1", luaScriptConfig.getScriptSha());
//        limiter.setRate(50);
    }

    @TearDown
    public void tearDown() {
        log.info("========================");
        client.shutdown();
    }

    @Benchmark
    public void limiter() throws LimitException {
        limiter.acquire(1000);
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(LimiterBenchmark.class.getSimpleName())
                .forks(2)
                .warmupIterations(3)
                .measurementIterations(3)
                .build();

        new Runner(opt).run();
    }
}
