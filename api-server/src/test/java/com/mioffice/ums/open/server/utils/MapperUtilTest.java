package com.mioffice.ums.open.server.utils;

import com.mioffice.ums.open.server.entity.bo.AppInfoBo;
import com.mioffice.ums.open.server.entity.bo.AppManagerBo;
import com.mioffice.ums.open.server.entity.bo.MessageChannelBo;
import com.mioffice.ums.open.server.entity.bo.AppListRecordInfoBO;
import com.mioffice.ums.open.server.entity.info.UserAppSysInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @since 2020/9/16
 */
@Slf4j
public class MapperUtilTest {
    @Test
    public void testConvert() {
        MessageChannel channel = MessageChannel.newBuilder()
                .addBotInfos(BotInfo.newBuilder().setBotAppId("1").setBotBizId("biz1"))
                .addBotInfos(BotInfo.newBuilder().setBotAppId("2").setBotBizId("biz2"))
                .setChannel(1).build();
        MessageChannel channel1 = MessageChannel.newBuilder()
                .addBotInfos(BotInfo.newBuilder().setBotAppId("3").setBotBizId("biz3"))
                .addBotInfos(BotInfo.newBuilder().setBotAppId("4").setBotBizId("biz4"))
                .setIsOutSend(2).setChannel(1).build();
        AppManager manager = AppManager.newBuilder().setName("zhangsan").setUsername("aaa").build();
        AppManager manager2 = AppManager.newBuilder().setName("123").setUsername("bbb").build();
        Dept dept = Dept.newBuilder().setDeptName("dept_1").setDeptId("1").build();
        AppInfo appInfo = AppInfo.newBuilder()
                .setAppName("app1")
                .addAppManagers(manager)
                .addAppManagers(manager2)
                .setQps(12)
                .addChannels(channel)
                .addChannels(channel1)
                .addDepts(dept)
                .build();

        AppInfoBo appInfoBo = MapperUtil.INSTANCE.mapToAppInfoBo(appInfo);
        log.info("{}", appInfoBo);
        assertEquals("app1", appInfoBo.getAppName());

        AppInfo appInfo1 = MapperUtil.INSTANCE.mapToAppInfo(appInfoBo);
        log.info("{}", appInfo1);
    }

    @Test
    public void testMapToAppManagerBo() {
        UserAppSysInfo info = new UserAppSysInfo();
        info.setUsername("张三");
        info.setName("zhangsan");
        List<UserAppSysInfo> userAppSysInfos = Arrays.asList(info);
        List<AppManagerBo> managerBos = MapperUtil.INSTANCE.mapToAppManagerBo(userAppSysInfos);
        log.info("{}", managerBos);
        assertTrue(managerBos.size() == 1);
    }

    @Test
    public void testMapToAppListRecordInfo() {
        AppListRecordInfoBO appListRecordInfoBO = new AppListRecordInfoBO();
        appListRecordInfoBO.setChannel(Arrays.asList(1, 2, 3, 4, 5));
        appListRecordInfoBO.setAppId("100");
        AppListRecordInfo info = MapperUtil.INSTANCE.mapToAppListRecordInfo(appListRecordInfoBO);
        log.info("{}", info);

        MessageChannelBo bo = new MessageChannelBo();
        bo.setChannel(1);
        bo.setBotAppIds(Arrays.asList(new MessageChannelBo.BotInfoDesc("a", "biz a"),
                new MessageChannelBo.BotInfoDesc("b", "biz b"),
                new MessageChannelBo.BotInfoDesc("c", "biz c")));
        MessageChannel messageChannel = MapperUtil.INSTANCE.mapToMessageChannel(bo);
        log.info("{}", messageChannel);
    }
}