package com.mioffice.ums.open.server.rpc;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.remote.MessageRpcClient;
import com.mioffice.ums.open.server.remote.UmsAdminRpcClient;
import com.mioffice.ums.open.server.service.PushSummaryService;
import com.xiaomi.info.infra.util.JsonUtil;
import com.xiaomi.info.pb.app.mioffice.ums.admin.v1.EmployeeInfo;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUserResponse;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.11
 */
public class MessageTest extends BaseTest {

    @Autowired
    private MessageRpcClient messageRpcClient;

    @Autowired
    private PushSummaryService pushSummaryService;

    @Autowired
    private UmsAdminRpcClient umsAdminRpcClient;

    @Test
    public void testSendMsg() throws JsonProcessingException {

        MessageUser.Builder builder = MessageUser.newBuilder();
        builder.setUsername("junfudong");
        builder.setExtraId(UUID.randomUUID().toString().replace("-", ""));
        builder.setAppId("cli_9fcecd191e7a5063");

        HashMap<String, Object> map = new HashMap<>();
        map.put("title", "测试");
        map.put("content", "请忽略");
        builder.setPlaceholderContent(JsonUtil.writeValueAsString(map));

        MessageUserResponse messageUserResponse = messageRpcClient.sendMessageBatch(Arrays.asList(builder.build()), 1544L);
        Assert.assertEquals(messageUserResponse.getCode(), 200);
    }

    @Test
    public void test2(){
        pushSummaryService.syncTodayAppPushRecords(null, null);
    }

    @Test
    public void testGetEmp() {
        List<EmployeeInfo> empList = umsAdminRpcClient.getEmpList(Arrays.asList("yangguanlin"));
        System.out.println(empList);
    }
}
