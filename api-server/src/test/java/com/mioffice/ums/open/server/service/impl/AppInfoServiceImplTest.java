package com.mioffice.ums.open.server.service.impl;


import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.entity.bo.AppInfoBo;
import com.mioffice.ums.open.server.entity.bo.AppManagerBo;
import com.mioffice.ums.open.server.entity.bo.DeptBo;
import com.mioffice.ums.open.server.entity.bo.MessageChannelBo;
import com.mioffice.ums.open.server.entity.info.AppSysInfo;
import com.mioffice.ums.open.server.service.AppInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/17
 */
@Slf4j
@EnableAutoConfiguration(exclude = {WebMvcAutoConfiguration.class})
public class AppInfoServiceImplTest extends BaseTest {
    @Autowired
    AppInfoService appInfoService;

    @Test
    public void testSaveOrUpdate() {
        AppInfoBo appInfoBo = new AppInfoBo();

        appInfoBo.setOperatorName("zhangqingbo3");
        appInfoBo.setQps(10);
        appInfoBo.setAppName("test1");

        List<DeptBo> deptBos = new LinkedList<>();
        DeptBo bo = new DeptBo();
        bo.setDeptId("dept id 1");
        bo.setDeptName("dept name 000");

        DeptBo nextBo = new DeptBo();
        nextBo.setDeptId("dept id 2");
        nextBo.setDeptName("dept name 2");
        deptBos.add(bo);
        deptBos.add(nextBo);
        appInfoBo.setDepts(deptBos);

        List<MessageChannelBo> messageChannelBos = new LinkedList<>();
        MessageChannelBo messageChannelBo = new MessageChannelBo();
        messageChannelBo.setIsOutSend(0);
        messageChannelBo.setChannel(1);
        messageChannelBo.setBotAppIds(Arrays.asList(new MessageChannelBo.BotInfoDesc("bot1", "biz1"),
                new MessageChannelBo.BotInfoDesc("bot2", "biz2"),
                new MessageChannelBo.BotInfoDesc("bot3", "biz3")));

        messageChannelBos.add(messageChannelBo);
        appInfoBo.setChannels(messageChannelBos);

        List<AppManagerBo> managerBos = new LinkedList<>();
        AppManagerBo appManagerBo = new AppManagerBo();
        appManagerBo.setName("zhangsan");
        appManagerBo.setUsername("zhangsan3");
        managerBos.add(appManagerBo);

        appInfoBo.setAppManagers(managerBos);
        appInfoService.saveOrUpdate(appInfoBo, Arrays.asList("ROLE_SYS_SUPER_ADMIN"));

        bo.setDeptName("dept name afa");
        appInfoBo.setId(1);
        nextBo.setDeptId("dept id 3");
        messageChannelBo.setBotAppIds(Arrays.asList(
                new MessageChannelBo.BotInfoDesc("bot2", "biz2"),
                new MessageChannelBo.BotInfoDesc("bot3", "biz3"),
                new MessageChannelBo.BotInfoDesc("bot4", "biz4")));

        appInfoService.saveOrUpdate(appInfoBo, Arrays.asList("ROLE_SYS_SUPER_ADMIN"));
    }

    @Test
    public void insertTest() {
        AppSysInfo appSysInfo = new AppSysInfo();
        appSysInfo.setAppId("123");
        appSysInfo.setAppSecret("123456");
        appSysInfo.setAppSysStatus((byte) 0);
        appSysInfo.setQps(100);
        appInfoService.saveOrUpdate(appSysInfo);
    }
}