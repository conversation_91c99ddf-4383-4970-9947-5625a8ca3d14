package com.mioffice.ums.open.server.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.entity.bo.AppListRecordInfoBO;
import com.mioffice.ums.open.server.mapper.AppSysInfoMapper;
import com.mioffice.ums.open.server.service.AppApprovalListService;
import com.mioffice.ums.open.server.service.AppListService;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalListInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.data.redis.core.RedisKeyValueTemplate;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/20 4:04 下午
 * version: 1.0.0
 */
@Slf4j
@EnableAutoConfiguration(exclude = {WebMvcAutoConfiguration.class})
public class AppSysInfoTest extends BaseTest {

    @Autowired
    private AppSysInfoMapper appSysInfoMapper;

    @Autowired
    private AppListService appListService;

    @Autowired
    private AppApprovalListService appApprovalListService;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    private RedisKeyValueTemplate redisKeyValueTemplate;


    @Test
    public void testSelectAppListPage() {
        Page<AppListRecordInfoBO> dataPage = new Page<>(1, 2);
        List<AppListRecordInfoBO> appListRecordInfos = appSysInfoMapper.selectAppListPage(dataPage, "niuwenyu", "", null, null, null, null, 0, 0);
        System.out.println(appListRecordInfos);
    }

    @Test
    public void testGetAppList() {
        AppListInfo appList = appListService.getAppList("zuolei", "zuolei1", 1L, 2L, "", null, null, null, null, "", "");
        System.out.println(appList);
    }

    @Test
    public void testGetAppApprovalList() {
        AppApprovalListInfo appApprovalList = appApprovalListService.getAppApprovalList("niuwenyu", 1L, 10L, "", "", "", "", "");
        System.out.println(appApprovalList);
    }

    @Test
    public void testDelApp() {
//        boolean isDel = redissonClient.getBucket("ums:open:server:app_info::S00005").delete();
////        long n = redissonClient.getKeys().delete("ums:open:server:app_info::S00005");
//        System.out.println(isDel);
//        redissonClient.get("ums:open:server:app_info::S00005").delete()

//        String ss = redisKeyValueTemplate.delete("ums:open:server:app_info::S00005");
//        System.out.println(ss);
//        redissonClient.getScript(StringCodec.INSTANCE).eval(RScript.Mode.READ_WRITE, "");

        long n = redissonClient.getKeys().delete("ums:open:server:app_info::S00110");

//        boolean delete = redissonClient.getBucket("ums:open:server:app_info::S00110").delete();
//        System.out.println(delete);
    }
}
