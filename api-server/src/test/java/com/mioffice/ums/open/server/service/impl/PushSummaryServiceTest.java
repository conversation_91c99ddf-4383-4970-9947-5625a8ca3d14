package com.mioffice.ums.open.server.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.entity.bo.AppChannelCountBo;
import com.mioffice.ums.open.server.entity.bo.AppMessageCountBo;
import com.mioffice.ums.open.server.service.PushSummaryService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.25
 */
public class PushSummaryServiceTest extends BaseTest {

    @Autowired
    private PushSummaryService pushSummaryService;

    @Test
    public void testGetCount() {
        DateTime begin = DateUtil.beginOfDay(new Date());
        DateTime end = DateUtil.endOfDay(new Date());
        List<AppMessageCountBo> topAppMsgList = pushSummaryService.getTopAppMsgList(10, begin.getTime(), end.getTime());
        System.out.println(topAppMsgList);
    }

    @Test
    public void testGetChannelCount() {
        DateTime begin = DateUtil.beginOfDay(new Date());
        DateTime end = DateUtil.endOfDay(new Date());
        List<AppChannelCountBo> appMsgChannelCount = pushSummaryService.getAppMsgChannelCount(Collections.emptyList(), begin.getTime(), end.getTime());
        System.out.println(appMsgChannelCount);
    }

    @Test
    public void testDelete() {
        pushSummaryService.deleteExpiredPushRecord(1635142258000L);
    }
}
