package com.mioffice.ums.open.server.service.impl;

import com.mioffice.ums.open.server.BaseTest;
import com.mioffice.ums.open.server.service.PushSummaryService;
import com.mioffice.ums.open.server.task.AppTodayPushRecordTask;
import com.mioffice.ums.open.server.task.AppYesterdayPushRecordTask;
import lombok.Data;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020/10/14 8:50 下午
 */
public class PushSummaryTaskTest extends BaseTest {

    @Autowired
    PushSummaryService pushSummaryService;
    @Test
    public void test1(){
        pushSummaryService.syncTodayAppPushRecords(null, null);
    }

    @Data
    static class SyncTime{
        private Long startTime;
        private Long endTime;
    }

    /**
     * 1601136000000  1601222399999
     * 1601222400000  1601308799999
     * 1601308800000  1601395199999
     * 1601395200000  1601481599999
     * 1602172800000  1602259199999
     * 1602259200000  1602345599999
     * 1602345600000  1602431999999
     * 1602432000000  1602518399999
     * 1602518400000  1602604799999
     * 1602604800000  1602691199999
     * 1602691200000  1602777599999
     * 1602777600000  1602863999999
     * 1602864000000  1602950399999
     * 1602950400000  1603036799999
     * 1603036800000  1603123199999
     */
    @Test
    public void test2(){
        List<SyncTime> syncTimes = getSyncTime();
        for (SyncTime syncTime: syncTimes) {
            pushSummaryService.syncAppPushRecordsWithStartEndTime(syncTime.startTime, syncTime.endTime);
        }
    }

    List<SyncTime> getSyncTime(){
        List<SyncTime> syncTimes = new ArrayList<>();
        SyncTime syncTime = new SyncTime();
        syncTime.setStartTime(1602259200000L);
        syncTime.setEndTime(1602345599999L);
        syncTimes.add(syncTime);
        SyncTime syncTime1 = new SyncTime();
        syncTime1.setStartTime(1602345600000L);
        syncTime1.setEndTime(1602431999999L);
        syncTimes.add(syncTime1);
        SyncTime syncTime2 = new SyncTime();
        syncTime2.setStartTime(1602432000000L);
        syncTime2.setEndTime(1602518399999L);
        syncTimes.add(syncTime2);
        SyncTime syncTime3 = new SyncTime();
        syncTime3.setStartTime(1602518400000L);
        syncTime3.setEndTime(1602604799999L);
        syncTimes.add(syncTime3);

        SyncTime syncTime4 = new SyncTime();
        syncTime4.setStartTime(1602518400000L);
        syncTime4.setEndTime(1602604799999L);
        syncTimes.add(syncTime4);

        SyncTime syncTime5 = new SyncTime();
        syncTime5.setStartTime(1602604800000L);
        syncTime5.setEndTime(1602691199999L);
        syncTimes.add(syncTime5);

        SyncTime syncTime6 = new SyncTime();
        syncTime6.setStartTime(1602777600000L);
        syncTime6.setEndTime(1602863999999L);
        syncTimes.add(syncTime6);

        SyncTime syncTime7 = new SyncTime();
        syncTime7.setStartTime(1602691200000L);
        syncTime7.setEndTime(1602691199999L);
        syncTimes.add(syncTime7);

        SyncTime syncTime8 = new SyncTime();
        syncTime8.setStartTime(1602864000000L);
        syncTime8.setEndTime(1602950399999L);
        syncTimes.add(syncTime8);

        SyncTime syncTime9 = new SyncTime();
        syncTime9.setStartTime(1602950400000L);
        syncTime9.setEndTime(1603036799999L);
        syncTimes.add(syncTime9);

        SyncTime syncTime10 = new SyncTime();
        syncTime10.setStartTime(1603036800000L);
        syncTime10.setEndTime(1603123199999L);
        syncTimes.add(syncTime9);

        return syncTimes;
    }
}
