package com.mioffice.ums.open.common.constats;

/**
 * <AUTHOR>
 * @since 2020/9/18
 */
public class RedisConstants {
    /**
     * hash
     * key: ums:engine:rate:limit
     * field: MessageProcessor#getRateLimitKey
     * v: timestamp ms
     * <p>
     * field: MessageProcessor#getRateLimitKey + rate
     * v: int
     */
    public static final String RATE_LIMIT_KEY = "ums:open:rate:limit";

    public static final String RATE_LIMIT_FIELD = "rate";
    public static final String RATE_MS_FIELD = "rate_ms";

    /**
     * 默认速率 qps 50/s
     */
    public static final int DEFAULT_RATE = 50;
    public static final int RATE_MS = 1000;

    private RedisConstants() {

    }
}
