package com.mioffice.ums.open.common.message;

import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.constats.MessageStatusEnum;
import lombok.Data;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/10/16 7:20 下午
 * version: 1.0.0
 */
@Data
public class AppMessageRecord {

    private String username;

    private String userEmail;

    private String phone;

    private String chatId;

    private MessageStatusEnum messageStatusEnum;

    private MessageChannelEnum messageChannelEnum;

    private String errorLog;

}
