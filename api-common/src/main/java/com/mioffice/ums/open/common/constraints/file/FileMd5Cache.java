package com.mioffice.ums.open.common.constraints.file;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.digest.Md5Crypt;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.10.21
 */

public enum FileMd5Cache {

    /**
     * 单例
     */
    INSTANCE;

    private final OkHttpClient httpClient;

    private final Cache<String, Long> cache;

    FileMd5Cache() {
        httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .retryOnConnectionFailure(false)
                .followRedirects(false)
                .build();

        cache = CacheBuilder.newBuilder().maximumSize(1000).build();
    }

    public Long getByCache(String url) throws ExecutionException, IOException {
        String md5 = Md5Crypt.md5Crypt(url.getBytes());
        int index = url.indexOf('?');
        if (index != -1) {
            return cache.get(md5, () -> getFileLength(url.substring(0, index)));
        } else {
            return cache.get(md5, () -> getFileLength(url));
        }
    }

    public Long getFileLength(String url) throws IOException {
        Request request = new Request.Builder().url(url).head().build();
        Response response = httpClient.newCall(request).execute();
        int code = response.code();
        if (code != 200) {
            throw new IOException(String.format("http status %d", code));
        }
        String len = response.header("Content-Length");
        if (Objects.nonNull(len)) {
            return Long.valueOf(len);
        }
        return null;
    }

}
