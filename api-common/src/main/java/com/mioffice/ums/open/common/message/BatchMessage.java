package com.mioffice.ums.open.common.message;

import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.11
 */
@Data
public class BatchMessage {

    /**
     * 飞书appId 或者 邮件sender
     * <p>
     * 尽量使用 botBizId
     */
    @Deprecated
    private String botAppId;

    /**
     * 机器人id
     */
    private String botBizId;

    /**
     * 消息渠道
     */
    private Byte channel;

    /**
     * 模板id
     */
    private String templateBizId;

    /**
     * 消息内容固化
     */
    private Byte contentFlag;

    @Valid
    private List<MsgUser> userList;
}
