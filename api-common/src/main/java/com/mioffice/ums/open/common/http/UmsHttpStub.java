package com.mioffice.ums.open.common.http;

import com.mioffice.ums.open.common.http.annotation.CheckParam;
import com.mioffice.ums.open.common.http.annotation.Get;
import com.mioffice.ums.open.common.http.annotation.Post;
import com.mioffice.ums.open.common.message.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
public interface UmsHttpStub {

    /**
     * 发批量消息
     *
     * @param batchMessage
     * @return
     */
    @Post("api/v2/message/send/batch")
    @CheckParam
    UmsResponse<MsgResult> sendBatch(BatchMessage batchMessage);

    /**
     * 上传飞书图片
     *
     * @param imageUrl
     * @return
     */
    @Post("api/lark/image/upload")
    UmsResponse<ImageResult> uploadLarkImage(ImageUrl imageUrl);

    /**
     * 根据groupId获取推送日志
     *
     * @param groupId 组id
     * @return 推送结果
     */
    @Get("api/app/push/log/result")
    UmsResponse<AppPushResult> pushLogResult(String groupId);

    /**
     * 获取飞书最终发送的消息体
     *
     * @param groupId
     * @param username
     * @return
     */
    @Get("api/message/final/body/get")
    UmsResponse<List<FinalMsgResult>> getFinalMsgResult(String groupId, String username);
}
