package com.mioffice.ums.open.common.constraints.file;

import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.10.21
 */
@Slf4j
public class FileUrlSizeConstraintValidation implements ConstraintValidator<FileUrlSizeValidation, List<String>> {

    private final FileMd5Cache fileMd5Cache = FileMd5Cache.INSTANCE;
    private long maxLength;

    @Override
    public void initialize(FileUrlSizeValidation constraintAnnotation) {
        maxLength = constraintAnnotation.maxLength();
    }

    @Override
    public boolean isValid(List<String> list, ConstraintValidatorContext constraintValidatorContext) {
        if (Objects.isNull(list)) {
            return true;
        }
        long length = 0L;
        for (String url : list) {
            try {
                length += fileMd5Cache.getByCache(url);
            } catch (ExecutionException e) {
                log.error("文件大小校验失败, url = [{}]", url, e);
                return false;
            } catch (IOException e) {
                log.error("附件访问失败, url = [{}]", url, e);
                return false;
            }
        }

        return length <= maxLength;
    }

}
