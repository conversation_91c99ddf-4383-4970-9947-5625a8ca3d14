package com.mioffice.ums.open.common.constats;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Getter
@AllArgsConstructor
public enum ResponseCode {
    /**
     * 成功
     */
    SUCCESS(200, "success"),
    PARAM_ERROR(300, "参数错误"),
    AUTH_ERROR(401, "unauthorized"),
    LIMIT_EXCEED_ERROR(402, "limit exceed error"),
    INTERNAL_ERROR(500, "内部错误"),
    NO_APP(100044, "应用不存在"),
    APP_HAS_TOPIC(100045, "应用已申请专用队列"),
    APP_TOPIC_UNDER_REVIEW(100046,"应用队列审核中"),
    APP_NOT_EXISTS(100047, "应用不存在|未审批通过|已停用"),
    APP_NO_TOPIC(100048, "应用不存在专用队列"),
    APP_NO_MANAGER(100049, "应用不存在管理员"),
    APP_TOPIC_DUPLICATE(100050,"topic名称重复"),
    APP_NO_AUTH(100051,"无此应用对应的查看权限");

    private int code;
    private String msg;
}
