package com.mioffice.ums.open.common.constraints.file;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.10.21
 */
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = FileUrlSizeConstraintValidation.class)
@Target(value = {ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
public @interface FileUrlSizeValidation {

    String message() default "文件超大";

    //分组
    Class<?>[] groups() default {};

    //负载
    Class<? extends Payload>[] payload() default {};

    long maxLength() default 2147483647;

}
