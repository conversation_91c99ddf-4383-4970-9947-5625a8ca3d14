package com.mioffice.ums.open.common.message;

import com.mioffice.ums.open.common.constats.AttachFileConst;
import com.mioffice.ums.open.common.constraints.file.FileUrlSizeValidation;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.11
 */
@Data
public class MsgUser {

    private String username;
    private String email;
    private String phone;
    private String chatId;

    /**
     * 邮件抄送列表
     */
    private List<String> ccEmails;

    /**
     * 邮件附件 FDS 地址
     */
    @Size(max = AttachFileConst.MAX_NUMBER, message = "附件最大个数超过20个")
    @FileUrlSizeValidation(maxLength = AttachFileConst.MAX_LENGTH, message = "附件校验失败")
    private List<String> attachUrls;


    private Map<String, Object> params;

    private String placeholderContent;

    /**
     * 内部使用属性，内容的md5
     */
    private String contentMd5;

    /**
     * 内容固话标识
     * */
    private Integer contentFlag;
}
