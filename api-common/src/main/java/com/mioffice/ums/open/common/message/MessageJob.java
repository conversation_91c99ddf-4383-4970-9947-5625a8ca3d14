package com.mioffice.ums.open.common.message;

import lombok.Data;
import java.util.Map;


/**
 * 消息任务信息
 * <AUTHOR>
 * @create 11 24,2022
 */
@Data
public class MessageJob {

    /**
     * 规则名称
     * */
    private String ruleName;


    /**
     * 回调地址
     * */
    private String  callback;


    /**
     * 定时规则,需要符合corn规范
     * */
    private String cornExpr;


    /**
     * 占位符参数
     * */
    private Map<String,String> placeholder;

    /**
     * 是否开启飞书渠道
     * */
    private Byte larkChannel;

    /**
     * 飞书机器人ID
     * */
    private String larkBotId;

    /**
     * 飞书模板ID
     * */
    private String larkTemplateId;



    /**
     * 是否开启邮件渠道
     * */
    private Byte mailChannel;

    /**
     * 邮件机器人ID
     * */
    private String mailBotId;

    /**
     * 邮件模板ID
     * */
    private String mailTemplateId;



    /**
     * 是否开启短信渠道
     * */
    private Byte smsChannel;

    /**
     * 短信机器人ID
     * */
    private String smsBotId;

    /**
     * 短信模板ID
     * */
    private String smsTemplateId;


}
