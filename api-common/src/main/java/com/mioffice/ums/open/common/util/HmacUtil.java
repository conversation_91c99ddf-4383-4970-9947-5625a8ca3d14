package com.mioffice.ums.open.common.util;

import com.mioffice.ums.open.common.exception.HMacException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Slf4j
public class HmacUtil {
    private static final ThreadLocal<Mac> MESSAGE_DIGEST_LOCAL = ThreadLocal.withInitial(() -> {
        try {
            return Mac.getInstance("HmacSHA256");
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
            throw new HMacException(e);
        }
    });

    public static String getHmacSha256(String secretKey, String message) {
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
        Mac mac = MESSAGE_DIGEST_LOCAL.get();
        try {
            mac.init(secretKeySpec);
            return Hex.encodeHexString(mac.doFinal(message.getBytes(StandardCharsets.UTF_8)));
        } catch (InvalidKeyException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    private HmacUtil() {
    }
}
