package com.mioffice.ums.open.common.util;

import com.google.gson.*;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.12
 */
@SuppressWarnings("all")
public class JsonUtils {

    private static final Gson GSON;

    static {
        GsonBuilder gsonBuilder = new GsonBuilder();
        // Map 处理
        gsonBuilder.registerTypeAdapter(new TypeToken<Map>() {
        }.getType(), new MapDeserializerDoubleAsIntFix());
        // List 处理
        gsonBuilder.registerTypeAdapter(new TypeToken<List>() {
        }.getType(), new ListDeserializerDoubleAsIntFix());

        GSON = gsonBuilder.create();
    }

    public static Gson getGSON() {
        return GSON;
    }

    public static String toJson(Object object) {
        return GSON.toJson(object);
    }

    public static <T> T parse(String json, Class<T> cls) {
        return GSON.fromJson(json, cls);
    }

    public static <T> T parse(String json, Type cls) {
        return GSON.fromJson(json, cls);
    }

    public static <T> List<T> toList(String json, Class<T> cls) {
        return GSON.fromJson(json, new TypeToken<List<T>>() {
        }.getType());
    }

    public static <T> Map<String, T> toMap(String gsonString) {
        return GSON.fromJson(gsonString, new TypeToken<Map<String, T>>() {
        }.getType());
    }


    public static class MapDeserializerDoubleAsIntFix implements JsonDeserializer<Map<String, Object>> {
        @Override
        public Map<String, Object> deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
            return (Map<String, Object>) read(jsonElement);
        }
    }

    public static class ListDeserializerDoubleAsIntFix implements JsonDeserializer<List<Object>> {
        @Override
        public List deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
            return (List<Object>) read(jsonElement);
        }
    }

    private static Object read(JsonElement in) {
        if (in.isJsonArray()) {
            List<Object> list = new ArrayList<>();
            JsonArray arr = in.getAsJsonArray();
            for (JsonElement anArr : arr) {
                list.add(read(anArr));
            }
            return list;
        } else if (in.isJsonObject()) {
            Map<String, Object> map = new LinkedTreeMap<>();
            JsonObject obj = in.getAsJsonObject();
            Set<Map.Entry<String, JsonElement>> entitySet = obj.entrySet();
            for (Map.Entry<String, JsonElement> entry : entitySet) {
                map.put(entry.getKey(), read(entry.getValue()));
            }
            return map;
        } else if (in.isJsonPrimitive()) {
            JsonPrimitive prim = in.getAsJsonPrimitive();
            if (prim.isBoolean()) {
                return prim.getAsBoolean();
            } else if (prim.isString()) {
                return prim.getAsString();
            } else if (prim.isNumber()) {
                Number num = prim.getAsNumber();
                if (Math.ceil(num.doubleValue()) == num.longValue()) {
                    return num.longValue();
                } else {
                    return num.doubleValue();
                }
            }
        }
        return null;
    }

}
