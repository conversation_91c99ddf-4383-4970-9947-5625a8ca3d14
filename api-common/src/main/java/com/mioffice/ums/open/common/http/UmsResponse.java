package com.mioffice.ums.open.common.http;

import com.mioffice.ums.open.common.constats.ResponseCode;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Data
public class UmsResponse<T> implements Serializable {
    private Integer code;
    private String msg;
    private T data;

    public static <T> UmsResponse<T> valueOf(ResponseCode responseCode) {
        UmsResponse<T> umsResponse = new UmsResponse<>();
        umsResponse.setCode(responseCode.getCode());
        umsResponse.setMsg(responseCode.getMsg());
        return umsResponse;
    }

    public static <T> UmsResponse<T> valueOf(ResponseCode responseCode, T data) {
        UmsResponse<T> umsResponse = new UmsResponse<>();
        umsResponse.setCode(responseCode.getCode());
        umsResponse.setMsg(responseCode.getMsg());
        umsResponse.setData(data);
        return umsResponse;
    }

    public static <T> UmsResponse<T> valueOf(T data) {
        UmsResponse<T> umsResponse = new UmsResponse<>();
        umsResponse.setCode(ResponseCode.SUCCESS.getCode());
        umsResponse.setMsg(ResponseCode.SUCCESS.getMsg());
        umsResponse.setData(data);
        return umsResponse;
    }

    public static <T> UmsResponse<T> valueOf(Integer code, String msg) {
        UmsResponse<T> umsResponse = new UmsResponse<>();
        umsResponse.setCode(code);
        umsResponse.setMsg(msg);
        return umsResponse;
    }
}
