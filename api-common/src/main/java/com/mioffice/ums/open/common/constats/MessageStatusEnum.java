package com.mioffice.ums.open.common.constats;

import lombok.Getter;

/**
 * <p>
 * 消息状态
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.11
 */
@Getter
public enum MessageStatusEnum {

    /**
     * 发送中
     */
    SENDING((byte) 1, "发送中"),

    /**
     * 发送成功
     */
    SEND_SUCCESS((byte) 2, "发送成功"),

    /**
     * 发送失败
     */
    SEND_FAIL((byte) 3, "发送失败"),

    /**
     * 中断
     */
    SEND_INTERRUPT((byte) 4, "消息中断");

    private final byte status;
    private final String desc;

    MessageStatusEnum(byte status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
