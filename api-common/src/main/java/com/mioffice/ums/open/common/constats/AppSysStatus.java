package com.mioffice.ums.open.common.constats;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/9/22
 */
@Getter
public enum AppSysStatus {
    /**
     * 1 审核中，2 审核通过，3 审核驳回， 4 已取消，5 已停用
     */
    REVIEW((byte) 1, "审核中"),
    PASS((byte) 2, "审核通过"),
    REJECT((byte) 3, "审核驳回"),
    CANCEL((byte) 4, "已取消"),
    DISABLE((byte) 5, "已停用");

    private Byte code;
    private String msg;

    AppSysStatus(Byte code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
