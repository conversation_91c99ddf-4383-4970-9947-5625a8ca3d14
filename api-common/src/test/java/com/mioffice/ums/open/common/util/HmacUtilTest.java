package com.mioffice.ums.open.common.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Slf4j
public class HmacUtilTest {

    @Test
    public void testHmac() {
        String test = HmacUtil.getHmacSha256("test", "{\"hello\":\"test\"}");
        log.info("{}", test);
        test = HmacUtil.getHmacSha256("test", "param=a&param2=aa");
        log.info("{}", test);
    }

    @Test
    public void testFmt() {
        String format = String.format("S%05d", 100);
        log.info("{}", format);
        String secret = HmacUtil.getHmacSha256(format, "appSecret");
        log.info("{}", secret);
    }
}