package com.mioffice.ums.open.sdk.factory;

import com.google.gson.Gson;
import com.mioffice.ums.open.common.constats.AuthConst;
import com.mioffice.ums.open.common.http.UmsHttpStub;
import com.mioffice.ums.open.common.http.annotation.Get;
import com.mioffice.ums.open.common.http.annotation.Post;
import com.mioffice.ums.open.common.util.HmacUtil;
import com.mioffice.ums.open.sdk.config.SdkProperties;
import com.mioffice.ums.open.sdk.exception.HttpInvokeException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Slf4j
public class HttpInvokeHandler implements InvocationHandler {

    private final OkHttpClient httpClient;
    private final SdkProperties sdkProperties;
    private final Gson gson = new Gson();

    public HttpInvokeHandler(OkHttpClient httpClient, SdkProperties sdkProperties) {
        this.httpClient = httpClient;
        this.sdkProperties = sdkProperties;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 调用object的方法
        if (method.getDeclaringClass() == Object.class) {
            return invokeObjectMethod(proxy, method, args);
        }

        return invoke(method, args);
    }

    private Object invoke(Method method, Object[] args) throws IOException {
        Post post = method.getAnnotation(Post.class);
        Get get = method.getAnnotation(Get.class);
        if (post == null && get == null) {
            throw new HttpInvokeException("http stub error");
        }

        Request.Builder requestBuilder = new Request.Builder();
        String sig;
        if (get != null) {
            sig = fillGet(method, args, get, requestBuilder);
        } else {
            sig = fillPost(method, args, post, requestBuilder);
        }

        fillHeader(requestBuilder, sig);

        Response response = httpClient.newCall(requestBuilder.build()).execute();
        if (!response.isSuccessful() || response.body() == null) {
            throw new HttpInvokeException("nothing response code:" + response.code());
        }
        try {

            Object result = gson.fromJson(response.body().string(), method.getGenericReturnType());
            log.info("response: {}", result);
            return result;
        } catch (IOException e) {
            throw new HttpInvokeException(e);
        }
    }

    private void fillHeader(Request.Builder requestBuilder, String sig) {
        requestBuilder.header(AuthConst.HEADER_APP_ID, sdkProperties.getAppConfig().getAppId())
                .header(AuthConst.HEADER_SIG, sig);
    }

    private String fillGet(Method method, Object[] args, Get get, Request.Builder requestBuilder) {
        String sig;
        String url = getUrl(method, get.value());
        HttpUrl.Builder httpBuilder = HttpUrl.parse(url).newBuilder();
        String queryStr = "";
        if (args != null && args.length > 0) {
            List<String> queryPairs = new ArrayList<>(args.length);
            Parameter[] parameters = method.getParameters();
            for (int i = 0; i < parameters.length; i++) {
                if (args[i] == null) {
                    continue;
                }
                queryPairs.add(String.format("%s=%s", parameters[i].getName(), args[i].toString()));
                httpBuilder.addQueryParameter(parameters[i].getName(), args[i].toString());
            }
            queryStr = String.join("&", queryPairs);
        }
        sig = HmacUtil.getHmacSha256(sdkProperties.getAppConfig().getAppSecret(), queryStr);
        requestBuilder.url(httpBuilder.build()).get();
        return sig;
    }

    private String fillPost(Method method, Object[] args, Post post, Request.Builder requestBuilder) {
        String sig;
        String url = getUrl(method, post.value());
        if (args == null || args.length > 1) {
            throw new HttpInvokeException("http stub post param error");
        }
        String json = gson.toJson(args[0]);
        sig = HmacUtil.getHmacSha256(sdkProperties.getAppConfig().getAppSecret(), json);
        requestBuilder.url(url).post(RequestBody.create(MediaType.parse("application/json"), json));
        return sig;
    }

    private String getUrl(Method method, String path) {
        return String.format("%s/%s", sdkProperties.getEnv().getHost(), path == null ? method.getName() : path);
    }

    private Object invokeObjectMethod(Object proxy, Method method, Object[] args) throws CloneNotSupportedException {
        String methodName = method.getName();
        if (methodName.equals("toString")) {
            return HttpInvokeHandler.this.toString();
        }
        if (methodName.equals("hashCode")) {
            return UmsHttpStub.class.hashCode() * 13 + this.hashCode();
        }
        if (methodName.equals("equals")) {
            return args[0] == proxy;
        }
        if (methodName.equals("clone")) {
            throw new CloneNotSupportedException("clone is not supported for jade dao.");
        }
        throw new UnsupportedOperationException(UmsHttpStub.class.getName() + "#" + method.getName());
    }
}
