package com.mioffice.ums.open.sdk.factory;

import com.mioffice.ums.open.common.http.annotation.CheckParam;
import com.mioffice.ums.open.sdk.exception.ParamErrorException;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.hibernate.validator.internal.engine.ConstraintViolationImpl;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.lang.reflect.Method;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020/10/21
 */
@Slf4j
public class CheckHandler implements MethodInterceptor {
    private Validator validator;

    public CheckHandler(Validator validator) {
        this.validator = validator;
    }

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        log.info("before CheckHandler");
        Method method = invocation.getMethod();
        CheckParam annotation = method.getAnnotation(CheckParam.class);
        if (annotation != null) {
            Object[] arguments = invocation.getArguments();
            for (Object argument : arguments) {
                String error = paramValid(argument);
                if (!StringUtils.isEmpty(error)) {
                    throw new ParamErrorException(error);
                }
            }
        }
        Object proceed = invocation.proceed();
        log.info("after CheckHandler");
        return proceed;
    }

    private String paramValid(Object object) {
        Set<ConstraintViolation<Object>> validate = validator.validate(object);
        if (!validate.isEmpty()) {
            List<String> list = new LinkedList<>();
            validate.stream()
                    .filter(v -> v instanceof ConstraintViolationImpl)
                    .forEach(v -> list.add(v.getPropertyPath().toString() + " " + v.getMessage()));
            return String.join(",", list);
        } else {
            return null;
        }
    }
}
