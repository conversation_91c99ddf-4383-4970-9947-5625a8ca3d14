package com.mioffice.ums.open.sdk.factory;

import com.mioffice.ums.open.common.http.UmsHttpStub;
import com.mioffice.ums.open.sdk.config.SdkProperties;
import okhttp3.OkHttpClient;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.SmartFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Validator;
import java.lang.reflect.Proxy;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Component
public class HttpClientFactory implements SmartFactoryBean<UmsHttpStub>, InitializingBean {

    @Autowired
    Validator validator;
    @Autowired
    private SdkProperties sdkProperties;
    private OkHttpClient httpClient;

    @Override
    public void afterPropertiesSet() throws Exception {
        httpClient = new OkHttpClient.Builder()
                .connectTimeout(sdkProperties.getTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(sdkProperties.getTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(sdkProperties.getTimeout(), TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(false)
                .followRedirects(false)
                .build();
    }

    @Override
    public UmsHttpStub getObject() throws Exception {
        UmsHttpStub umsHttpStub = (UmsHttpStub) Proxy.newProxyInstance(UmsHttpStub.class.getClassLoader(), new Class[]{UmsHttpStub.class}, new HttpInvokeHandler(httpClient, sdkProperties));
        ProxyFactory proxyFactory = new ProxyFactory();
        proxyFactory.setTarget(umsHttpStub);
        proxyFactory.setInterfaces(UmsHttpStub.class);
        proxyFactory.addAdvice(new CheckHandler(validator));
        return (UmsHttpStub) proxyFactory.getProxy();
    }

    @Override
    public Class<?> getObjectType() {
        return UmsHttpStub.class;
    }

    public SdkProperties getSdkProperties() {
        return sdkProperties;
    }

    public Validator getValidator() {
        return validator;
    }

    public void setValidator(Validator validator) {
        this.validator = validator;
    }

    public void setSdkProperties(SdkProperties sdkProperties) {
        this.sdkProperties = sdkProperties;
    }

    @Override
    public boolean isPrototype() {
        return false;
    }

    @Override
    public boolean isEagerInit() {
        return false;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }
}
