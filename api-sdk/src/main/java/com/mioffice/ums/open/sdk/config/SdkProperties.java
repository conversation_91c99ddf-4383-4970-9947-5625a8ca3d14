package com.mioffice.ums.open.sdk.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2020/9/9
 */
@Data
@ConfigurationProperties(prefix = "ums.open.sdk")
public class SdkProperties {

    private AppInfoConfig appConfig;
    private Env env = Env.DEV;
    /**
     * http 超时时间 默认 60s
     */
    private Long timeout = 60000L;

    @Data
    public static class AppInfoConfig {
        private String appId;
        private String appSecret;
    }
}
