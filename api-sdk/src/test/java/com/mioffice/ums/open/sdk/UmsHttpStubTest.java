package com.mioffice.ums.open.sdk;

import com.google.common.base.Splitter;
import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.http.UmsHttpStub;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.message.AppPushResult;
import com.mioffice.ums.open.common.message.BatchMessage;
import com.mioffice.ums.open.common.message.ImageResult;
import com.mioffice.ums.open.common.message.ImageUrl;
import com.mioffice.ums.open.common.message.MsgResult;
import com.mioffice.ums.open.common.message.MsgUser;
import com.mioffice.ums.open.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/10
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class UmsHttpStubTest {
    @Autowired
    UmsHttpStub umsHttpStub;

    @Value("${sms.white-list}")
    private List<String> whiteList;

    @Test
    public void testHttp() throws InterruptedException {
        BatchMessage batchMessage = new BatchMessage();
//        batchMessage.setBotAppId("cli_9fcecd191e7a5063");
        batchMessage.setBotBizId("B0217");
        batchMessage.setChannel(MessageChannelEnum.MI_WORK.getType());
        batchMessage.setTemplateBizId("TLB021700060");

        List<MsgUser> msgUserList = whiteList.stream().map(x->{
            List<String> nameAndPhone =  Splitter.on("_").splitToList(x);
            Map<String, Object> params = new HashMap<>();
            params.put("fullName", nameAndPhone.get(0));

            MsgUser msgUser = new MsgUser();
            msgUser.setParams(params);
            msgUser.setPhone(nameAndPhone.get(1));

            return msgUser;
        }).collect(Collectors.toList());

        Map<String, Object> params = new HashMap<>();
        params.put("fullNames", "董俊甫");
        params.put("afterOneWeekDay","2024-04-17");
        params.put("url","http://www.mioffice.cn");

        MsgUser msgUser1 = new MsgUser();
        msgUser1.setUsername("junfudong");
        msgUser1.setParams(params);

//        MsgUser msgUser1 = new MsgUser();
//        msgUser1.setParams(params);
//        msgUser1.setPhone("18601201415");




//        MsgUser msgUser1 = new MsgUser();
//        msgUser1.setParams(params);
//        msgUser1.setUsername("p-chenkuihua");


//        msgUser1.setChatId("oc_312643ce45a40d0d972dc9a7fe020606");
//        msgUser1.setEmail("<EMAIL>");
//        msgUser1.setCcEmails(Collections.singletonList("<EMAIL>"));
//        msgUser1.setParams(params);
//        msgUser1.setPhone("17600181980");
//        msgUser1.setEmail("<EMAIL>");
//        msgUser1.setCcEmails(Arrays.asList("<EMAIL>"));
//        msgUser1.setAttachUrls(Arrays.asList(
//                "https://cnbj1.fds.api.xiaomi.com/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95docx%E6%96%87%E4%BB%B6.docx",
//                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95docx%E6%96%87%E4%BB%B62.docx",
//                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95docx%E6%96%87%E4%BB%B63.docx",
//                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95xlsx%E6%96%87%E4%BB%B6.xlsx",
//                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95xlsx%E6%96%87%E4%BB%B62.xlsx",
//                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95xlsx%E6%96%87%E4%BB%B63.xlsx"
//        ));

//        MsgUser msgUser2 = new MsgUser();
//        msgUser2.setUsername("niuwenyu");
//        msgUser2.setParams(params);

        batchMessage.setUserList(Collections.singletonList(msgUser1));

        System.out.println(JsonUtils.toJson(batchMessage));

        UmsResponse<MsgResult> stringUmsResponse = umsHttpStub.sendBatch(batchMessage);

        System.out.println(stringUmsResponse);

        Thread.sleep(2000);
        UmsResponse<AppPushResult> appPushResultUmsResponse =
                umsHttpStub.pushLogResult(stringUmsResponse.getData().getGroupId());
        System.out.println(appPushResultUmsResponse);
    }

    @Test
    public void testUpload() {
        ImageUrl imageUrl = new ImageUrl();
//        imageUrl.setUrl("https://cnbj1.fds.api.xiaomi.com/ums/notice/2020-08-28/25a0349a-aa15-4c41-9832-5592052526a6.png?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=Zk1kkokCjaBtF6e7htI2vKIbRQU=");
        imageUrl.setUrl(
                "https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/5df0838b-dc74-4424-b004-6434bd9113fl");
        imageUrl.setBotBizId("B0106");
        UmsResponse<ImageResult> imageKeyUmsResponse = umsHttpStub.uploadLarkImage(imageUrl);
        System.out.println(imageKeyUmsResponse);
    }

    @Test
    public void testGetPushLogResult() {
        String groupId = "1b1bd7c8bd0b4bada0549959e486357d";
        UmsResponse<AppPushResult> listUmsResponse = umsHttpStub.pushLogResult(groupId);
        System.out.println(listUmsResponse);
    }

    @Test
    public void testCheckParam() {
        BatchMessage batchMessage = new BatchMessage();
        List<MsgUser> list = new LinkedList<>();
        MsgUser msgUser = new MsgUser();
        msgUser.setAttachUrls(Arrays.asList(
                "https://cnbj1.fds.api.xiaomi.com/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95docx%E6%96%87%E4%BB%B6.docx",
                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95docx%E6%96%87%E4%BB%B62.docx",
                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95docx%E6%96%87%E4%BB%B63.docx",
                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95xlsx%E6%96%87%E4%BB%B6.xlsx",
                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95xlsx%E6%96%87%E4%BB%B62.xlsx",
                "https://cnbj1-fds.api.xiaomi.net/xiaoxipingtaitest/%E6%B6%88%E6%81%AF%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95xlsx%E6%96%87%E4%BB%B63.xlsx"));
        list.add(msgUser);
        batchMessage.setUserList(list);

        umsHttpStub.sendBatch(batchMessage);
    }
}
