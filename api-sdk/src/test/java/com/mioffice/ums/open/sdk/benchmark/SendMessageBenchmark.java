package com.mioffice.ums.open.sdk.benchmark;

import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.http.UmsHttpStub;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.message.BatchMessage;
import com.mioffice.ums.open.common.message.MsgResult;
import com.mioffice.ums.open.common.message.MsgUser;
import com.mioffice.ums.open.sdk.config.Env;
import com.mioffice.ums.open.sdk.config.SdkProperties;
import com.mioffice.ums.open.sdk.factory.HttpClientFactory;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.Collections;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.10.09
 */
@BenchmarkMode(Mode.Throughput)
@State(Scope.Thread)
@Threads(50)
public class SendMessageBenchmark {

    private UmsHttpStub umsHttpStub;

    @Setup
    public void init() throws Exception {
        SdkProperties sdkProperties = new SdkProperties();
        sdkProperties.setEnv(Env.TEST);

        SdkProperties.AppInfoConfig appInfoConfig = new SdkProperties.AppInfoConfig();
        appInfoConfig.setAppId("S00028");
        appInfoConfig.setAppSecret("3fcb8385329f959a603e93a094a5c789caae5b17a50782456d3f9c378bfafd28");
        sdkProperties.setAppConfig(appInfoConfig);

        HttpClientFactory httpClientFactory = new HttpClientFactory();
        httpClientFactory.setSdkProperties(sdkProperties);
        umsHttpStub = httpClientFactory.getObject();
    }

    @Benchmark
    public void sendMessage() throws Exception {

        BatchMessage batchMessage = new BatchMessage();
        // 机器人 id
        batchMessage.setBotBizId("B0132");
        // 消息渠道
        batchMessage.setChannel(MessageChannelEnum.MI_WORK.getType());
        batchMessage.setTemplateBizId("TL0006200022");
        // 用户列表，最大1000
        MsgUser msgUser1 = new MsgUser();
        msgUser1.setUsername("yangguanlin");
        batchMessage.setUserList(Collections.singletonList(msgUser1));
        // 发送消息
        UmsResponse<MsgResult> response = umsHttpStub.sendBatch(batchMessage);
        if (response.getCode() != 200) {
            throw new RuntimeException(response.getMsg());
        }
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(SendMessageBenchmark.class.getSimpleName())
                .forks(2)
                .warmupIterations(5)
                .measurementIterations(5)
                .build();

        new Runner(opt).run();
    }
}
