package com.mioffice.ums.open.sdk.factory;

import com.mioffice.ums.open.common.http.UmsHttpStub;
import com.mioffice.ums.open.sdk.config.Env;
import com.mioffice.ums.open.sdk.config.SdkProperties;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2020/9/10
 */
@Slf4j
public class HttpClientFactoryTest {

    @Test
    public void testReq() throws Exception {
        SdkProperties sdkProperties = new SdkProperties();
        sdkProperties.setEnv(Env.DEV);
        SdkProperties.AppInfoConfig appConfig = new SdkProperties.AppInfoConfig();
        appConfig.setAppId("test");
        appConfig.setAppSecret("test");
        sdkProperties.setAppConfig(appConfig);
        HttpClientFactory factory = new HttpClientFactory();
        factory.setSdkProperties(sdkProperties);
        UmsHttpStub umsHttpStub = factory.getObject();

//        UmsResponse<List<String>> hello = httpStub.hello("a", null);
//        log.info("{}", hello);
//
//        SendMsg msg = new SendMsg();
//        msg.setContent("agaegagaeg");
//        UmsResponse<String> test = httpStub.send(msg);
//        log.info("{}", test);
//        Assert.assertTrue(hello != null);
    }
}
